# pnpm Monorepo EAS Build Configuration Summary

## Overview

This document summarizes all the configuration changes made to enable successful EAS builds in the HVPPYPlug+ pnpm monorepo. The configuration addresses compatibility issues between pnpm's symlink-based dependency management and Expo's EAS Build Service.

## Changes Made

### 1. Enhanced .npmrc Configuration

**File:** `.npmrc`

**Key Changes:**
- Added `node-linker=hoisted` (essential for React Native)
- Added `shamefully-hoist=true` for better compatibility
- Added workspace configuration settings
- Added dependency resolution optimizations
- Added EAS Build specific hoist patterns
- Added registry and network timeout settings

### 2. Updated pnpm-workspace.yaml

**File:** `pnpm-workspace.yaml`

**Key Changes:**
- Cleaned up duplicate package entries
- Added version catalog for consistent React versions
- Added shared workspace lockfile configuration

### 3. Created Metro Configurations

**Files Created:**
- `apps/vendor-app/metro.config.js`
- `apps/runner-app/metro.config.js`

**Key Features:**
- Proper monorepo package resolution
- Optimized watch folders for performance
- Disabled hierarchical lookup for consistent resolution
- Platform-specific extensions support

### 4. Enhanced EAS Build Configurations

**Files Updated:**
- `apps/customer-app/eas.json`
- `apps/vendor-app/eas.json`
- `apps/runner-app/eas.json`

**Key Changes:**
- Added `EXPO_USE_METRO_WORKSPACE_ROOT=1` environment variable
- Added pnpm cache configuration
- Added custom cache paths for better build performance
- Added CI environment variable

### 5. Environment Variables

**File:** `.env`

**Added:**
- `EXPO_USE_METRO_WORKSPACE_ROOT=1` for automatic Metro workspace detection

### 6. Package.json Scripts

**File:** `package.json` (root)

**Added Scripts:**
- `prepare:build` - Prepares monorepo for EAS builds
- `build:customer-dev`, `build:vendor-dev`, `build:runner-dev` - Development builds
- `clean:cache` - Cleans all cache directories
- `postinstall` - Automatically builds shared packages

**Files Updated:**
- `apps/vendor-app/package.json`
- `apps/runner-app/package.json`

**Added Scripts:**
- `prepare:build` - Runs preparation script from monorepo root
- `postinstall` - Ensures shared packages are built

### 7. Build Preparation Script

**File Created:** `scripts/prepare-eas-build.sh`

**Features:**
- Automated dependency installation with frozen lockfile
- Shared package building
- Configuration verification
- Environment variable setup
- Colored output for better UX

### 8. Documentation

**Files Created:**
- `docs/EAS_BUILD_PNPM_GUIDE.md` - Comprehensive guide for EAS builds
- `PNPM_EAS_BUILD_CONFIGURATION_SUMMARY.md` - This summary document

## How to Use

### For Development Builds

```bash
# From monorepo root
pnpm prepare:build

# From individual app directories
cd apps/customer-app
eas build --platform android --profile development
```

### For Production Builds

```bash
# From monorepo root
pnpm build:android  # Builds all apps for Android
pnpm build:ios      # Builds all apps for iOS
```

### For Individual App Builds

```bash
# Customer App
cd apps/customer-app
pnpm prepare:build
eas build --platform android --profile production

# Vendor App
cd apps/vendor-app
pnpm prepare:build
eas build --platform android --profile production

# Runner App
cd apps/runner-app
pnpm prepare:build
eas build --platform android --profile production
```

## Key Benefits

1. **EAS Build Compatibility**: Full compatibility with EAS Build Service
2. **Package Hoisting**: Proper hoisting ensures React Native compatibility
3. **Optimized Caching**: Custom cache paths improve build performance
4. **Automated Preparation**: Scripts automate the build preparation process
5. **Consistent Versions**: Catalog ensures consistent React versions across apps
6. **Better Error Handling**: Clear error messages and troubleshooting guides

## Troubleshooting

### Common Issues

1. **Lockfile outdated**: Run `pnpm install --no-frozen-lockfile`
2. **Module resolution errors**: Run `pnpm clean:cache` and reinstall
3. **Metro cache issues**: Run `pnpm expo start --clear`

### Environment Variables

Ensure these are set for EAS builds:
- `EXPO_USE_METRO_WORKSPACE_ROOT=1`
- `PNPM_CACHE_FOLDER=.pnpm-cache`
- `CI=1`

## Testing

The configuration has been tested with:
- pnpm 8.15.0
- Expo SDK 53
- React 19.0.0
- React Native 0.79.5
- Node.js 20.11.1

## Next Steps

1. Test EAS builds with the new configuration
2. Monitor build performance and optimize as needed
3. Update documentation based on real-world usage
4. Consider upgrading to pnpm 10+ for additional features

This configuration ensures reliable EAS builds while maintaining the benefits of a pnpm monorepo structure.
