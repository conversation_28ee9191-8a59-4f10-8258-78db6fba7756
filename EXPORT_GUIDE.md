# 🚀 HVPPYPlug+ Apps Export & Deployment Guide

This guide covers all the ways you can export and deploy your HVPPYPlug+ applications (Customer, Vendor, and Runner apps).

## 📋 Prerequisites

Before you can export your apps, make sure you have:

1. **EAS CLI installed globally:**
   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Expo account and login:**
   ```bash
   eas login
   ```

3. **Environment variables properly configured** (already done ✅)

## 🛠️ Export Options

### 1. **EAS Build (Recommended for Production)**

EAS Build is Expo's cloud-based build service that creates production-ready binaries.

#### Quick Start with Build Script:
```bash
# Run the interactive build script
./scripts/build-apps.sh
```

#### Manual EAS Build Commands:

**For Android:**
```bash
# Customer App
cd apps/customer-app && eas build --platform android --profile production

# Vendor App  
cd apps/vendor-app && eas build --platform android --profile production

# Runner App
cd apps/runner-app && eas build --platform android --profile production
```

**For iOS:**
```bash
# Customer App
cd apps/customer-app && eas build --platform ios --profile production

# Vendor App
cd apps/vendor-app && eas build --platform ios --profile production

# Runner App
cd apps/runner-app && eas build --platform ios --profile production
```

**For Both Platforms:**
```bash
# Customer App
cd apps/customer-app && eas build --platform all --profile production
```

### 2. **Web Export (Static Files)**

Export your apps as static web applications:

```bash
# Customer App
cd apps/customer-app && expo export --platform web

# Vendor App
cd apps/vendor-app && expo export --platform web

# Runner App
cd apps/runner-app && expo export --platform web
```

The exported files will be in the `dist/` folder of each app.

### 3. **Development Builds**

For testing and internal distribution:

```bash
# Preview builds (internal testing)
cd apps/customer-app && eas build --platform android --profile preview

# Development builds (with dev client)
cd apps/customer-app && eas build --platform android --profile development
```

## 📱 Build Profiles Explained

Each app has three build profiles configured in `eas.json`:

### **Development Profile**
- Includes Expo Dev Client
- Debug configuration
- For development and testing
- Smaller resource usage

### **Preview Profile**
- Release configuration
- Internal distribution
- For stakeholder testing
- Medium resource usage

### **Production Profile**
- Optimized for app stores
- Release configuration
- Largest resource usage
- Store-ready builds

## 🌐 Deployment Options

### **Mobile App Stores**

1. **Google Play Store (Android):**
   ```bash
   # Build AAB for Play Store
   cd apps/customer-app && eas build --platform android --profile production
   
   # Submit to Play Store (configure in eas.json first)
   eas submit --platform android --profile production
   ```

2. **Apple App Store (iOS):**
   ```bash
   # Build for App Store
   cd apps/customer-app && eas build --platform ios --profile production
   
   # Submit to App Store (configure in eas.json first)
   eas submit --platform ios --profile production
   ```

### **Web Deployment**

Deploy the web exports to any static hosting service:

- **Vercel:** `vercel deploy dist/`
- **Netlify:** Drag and drop `dist/` folder
- **AWS S3:** Upload `dist/` contents
- **GitHub Pages:** Push `dist/` to gh-pages branch

### **Internal Distribution**

1. **EAS Update (OTA Updates):**
   ```bash
   # Push updates without rebuilding
   cd apps/customer-app && eas update --channel production
   ```

2. **Direct APK Distribution:**
   - Download APK from EAS Build dashboard
   - Distribute via email, cloud storage, or internal tools

## 🔧 Configuration Notes

### **Environment Variables**
All apps are configured to use environment variables from `.env`:
- `EXPO_PUBLIC_APPWRITE_PROJECT_ID`
- `EXPO_PUBLIC_APPWRITE_ENDPOINT`
- `EXPO_PUBLIC_APPWRITE_DATABASE_ID`
- And more...

### **App Store Configuration**
Update the following in each app's `eas.json` for store submission:
- Apple ID and Team ID (iOS)
- Service Account Key (Android)
- App Store Connect App ID

### **Bundle Identifiers**
- Customer App: `com.hvppyplug.customer`
- Vendor App: `com.hvppyplug.vendor`
- Runner App: `com.hvppyplug.runner`

## 🚀 Quick Commands Reference

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Login to EAS
eas login

# Run interactive build script
./scripts/build-apps.sh

# Build all apps for Android
cd apps/customer-app && eas build --platform android --profile production &
cd apps/vendor-app && eas build --platform android --profile production &
cd apps/runner-app && eas build --platform android --profile production &

# Export all apps for web
cd apps/customer-app && expo export --platform web &
cd apps/vendor-app && expo export --platform web &
cd apps/runner-app && expo export --platform web &

# Check build status
eas build:list

# Download builds
eas build:download [build-id]
```

## 📊 Build Resources

The EAS configurations use different resource classes:
- **Development:** Medium resources
- **Preview:** Medium resources  
- **Production:** Large resources (for optimal performance)

## 🔍 Troubleshooting

**Common Issues:**
1. **Build fails:** Check environment variables and dependencies
2. **Login issues:** Run `eas logout` then `eas login`
3. **Resource limits:** Upgrade EAS plan if needed
4. **Bundle ID conflicts:** Ensure unique bundle identifiers

**Useful Commands:**
```bash
# Check EAS account
eas whoami

# View build logs
eas build:view [build-id]

# Cancel running build
eas build:cancel [build-id]
```

## 📈 Next Steps

1. **Set up CI/CD:** Integrate EAS builds with GitHub Actions
2. **Configure OTA Updates:** Set up automatic updates for minor changes
3. **App Store Optimization:** Add screenshots, descriptions, and metadata
4. **Analytics:** Integrate crash reporting and analytics services

---

**Need Help?** 
- Check the [Expo Documentation](https://docs.expo.dev/)
- Visit [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- Run `./scripts/build-apps.sh` for an interactive build experience
