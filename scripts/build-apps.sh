#!/bin/bash

# HVPPYPlug+ Apps Build Script
# This script helps you build and export all three apps for different platforms

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if EAS CLI is installed
check_eas_cli() {
    if ! command -v eas &> /dev/null; then
        print_error "EAS CLI is not installed. Installing..."
        npm install -g @expo/eas-cli
    else
        print_success "EAS CLI is already installed"
    fi
}

# Check if user is logged in to EAS
check_eas_login() {
    if ! eas whoami &> /dev/null; then
        print_warning "You are not logged in to EAS. Please run 'eas login' first."
        exit 1
    else
        print_success "You are logged in to EAS"
    fi
}

# Function to build an app
build_app() {
    local app_name=$1
    local build_profile=$2
    local platform=$3
    
    print_status "Building $app_name for $platform ($build_profile profile)..."
    
    cd "apps/$app_name"
    
    if [ "$platform" = "all" ]; then
        eas build --profile $build_profile --platform all --non-interactive
    else
        eas build --profile $build_profile --platform $platform --non-interactive
    fi
    
    cd ../..
    
    print_success "$app_name build completed for $platform"
}

# Function to export web version
export_web() {
    local app_name=$1
    
    print_status "Exporting $app_name for web..."
    
    cd "apps/$app_name"
    
    # Build for web
    expo export --platform web
    
    cd ../..
    
    print_success "$app_name web export completed"
}

# Main menu
show_menu() {
    echo ""
    echo "🚀 HVPPYPlug+ Apps Build & Export Menu"
    echo "======================================"
    echo ""
    echo "Build Options:"
    echo "1. Build Customer App (Android)"
    echo "2. Build Customer App (iOS)"
    echo "3. Build Customer App (Both)"
    echo ""
    echo "4. Build Vendor App (Android)"
    echo "5. Build Vendor App (iOS)"
    echo "6. Build Vendor App (Both)"
    echo ""
    echo "7. Build Runner App (Android)"
    echo "8. Build Runner App (iOS)"
    echo "9. Build Runner App (Both)"
    echo ""
    echo "10. Build All Apps (Android)"
    echo "11. Build All Apps (iOS)"
    echo "12. Build All Apps (Both Platforms)"
    echo ""
    echo "Web Export Options:"
    echo "13. Export Customer App (Web)"
    echo "14. Export Vendor App (Web)"
    echo "15. Export Runner App (Web)"
    echo "16. Export All Apps (Web)"
    echo ""
    echo "Build Profiles:"
    echo "17. Preview Build (All Apps)"
    echo "18. Production Build (All Apps)"
    echo ""
    echo "0. Exit"
    echo ""
}

# Handle user choice
handle_choice() {
    local choice=$1
    
    case $choice in
        1) build_app "customer-app" "production" "android" ;;
        2) build_app "customer-app" "production" "ios" ;;
        3) build_app "customer-app" "production" "all" ;;
        4) build_app "vendor-app" "production" "android" ;;
        5) build_app "vendor-app" "production" "ios" ;;
        6) build_app "vendor-app" "production" "all" ;;
        7) build_app "runner-app" "production" "android" ;;
        8) build_app "runner-app" "production" "ios" ;;
        9) build_app "runner-app" "production" "all" ;;
        10) 
            build_app "customer-app" "production" "android"
            build_app "vendor-app" "production" "android"
            build_app "runner-app" "production" "android"
            ;;
        11)
            build_app "customer-app" "production" "ios"
            build_app "vendor-app" "production" "ios"
            build_app "runner-app" "production" "ios"
            ;;
        12)
            build_app "customer-app" "production" "all"
            build_app "vendor-app" "production" "all"
            build_app "runner-app" "production" "all"
            ;;
        13) export_web "customer-app" ;;
        14) export_web "vendor-app" ;;
        15) export_web "runner-app" ;;
        16)
            export_web "customer-app"
            export_web "vendor-app"
            export_web "runner-app"
            ;;
        17)
            build_app "customer-app" "preview" "all"
            build_app "vendor-app" "preview" "all"
            build_app "runner-app" "preview" "all"
            ;;
        18)
            build_app "customer-app" "production" "all"
            build_app "vendor-app" "production" "all"
            build_app "runner-app" "production" "all"
            ;;
        0) 
            print_status "Exiting..."
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please try again."
            ;;
    esac
}

# Main execution
main() {
    print_status "Checking prerequisites..."
    check_eas_cli
    check_eas_login
    
    while true; do
        show_menu
        read -p "Enter your choice (0-18): " choice
        handle_choice $choice
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main
