#!/usr/bin/env node

/**
 * HVPPYPlug+ Data Seeders
 * 
 * Creates realistic seed data for the HVPPYPlug+ database using Faker.js
 * with South African locale (zu_ZA) for authentic local data
 */

const { faker } = require('@faker-js/faker');
const { Client, Databases, ID } = require('node-appwrite');

// Set South African locale
faker.locale = 'zu_ZA';

// Configuration
const config = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID || 'hvppyplug',
  apiKey: process.env.APPWRITE_API_KEY || '',
  databaseId: 'hvppyplug-main',
};

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);

// South African specific data
const southAfricanData = {
  provinces: [
    'Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape',
    'Limpopo', 'Mpumalanga', 'North West', 'Free State', 'Northern Cape'
  ],
  cities: {
    'Gauteng': ['Johannesburg', 'Pretoria', 'Soweto', 'Sandton', 'Randburg', 'Roodepoort'],
    'Western Cape': ['Cape Town', 'Stellenbosch', 'Paarl', 'George', 'Mossel Bay'],
    'KwaZulu-Natal': ['Durban', 'Pietermaritzburg', 'Newcastle', 'Ladysmith'],
    'Eastern Cape': ['Port Elizabeth', 'East London', 'Grahamstown', 'King Williams Town'],
  },
  townships: [
    'Soweto', 'Alexandra', 'Khayelitsha', 'Gugulethu', 'Nyanga', 'Langa',
    'Mamelodi', 'Soshanguve', 'Atteridgeville', 'Umlazi', 'KwaMashu'
  ],
  businessTypes: [
    'restaurant', 'fast_food', 'grocery', 'pharmacy', 'electronics',
    'clothing', 'hardware', 'beauty', 'automotive', 'services'
  ],
  foodCategories: [
    'Traditional South African', 'Braai & Grills', 'Indian Cuisine', 'Fast Food',
    'Pizza & Italian', 'Chinese', 'Seafood', 'Vegetarian', 'Desserts', 'Beverages'
  ],
  traditionalFoods: [
    'Boerewors', 'Biltong', 'Potjiekos', 'Bobotie', 'Sosaties', 'Koeksisters',
    'Vetkoek', 'Pap en Wors', 'Bunny Chow', 'Gatsby', 'Samoosas', 'Roti'
  ],
  vehicleTypes: ['bicycle', 'motorcycle', 'car', 'scooter', 'walking'],
  phoneProviders: ['082', '083', '084', '072', '073', '074', '076', '078', '079'],
};

// Seeding configuration
const seedConfig = {
  users: 100,
  vendors: 25,
  categories: 15,
  menuItems: 200,
  orders: 150,
  reviews: 300,
  addresses: 80,
  runnerProfiles: 20,
  promotions: 10,
  supportTickets: 30,
};

// Storage for created entities (for relationships)
const createdEntities = {
  users: [],
  vendors: [],
  categories: [],
  menuItems: [],
  orders: [],
  customers: [],
  runners: [],
  admins: [],
};

// Utility functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomElements(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateSouthAfricanPhone() {
  const provider = getRandomElement(southAfricanData.phoneProviders);
  const number = faker.string.numeric(7);
  return `${provider}${number}`;
}

function generateOrderNumber() {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `HVP-${dateStr}-${random}`;
}

function generateTicketNumber() {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `TKT-${dateStr}-${random}`;
}

// Seeder functions
async function seedUsers() {
  console.log('🧑‍🤝‍🧑 Seeding users...');
  
  // Create admin user
  const adminUser = {
    name: 'System Administrator',
    phone: '**********',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    isVerified: true,
    preferences: JSON.stringify({
      notifications: { push: true, email: true, sms: false },
      privacy: { showOnlineStatus: false, allowDirectMessages: true },
      app: { theme: 'dark', language: 'en', currency: 'ZAR' }
    }),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const admin = await databases.createDocument(
    config.databaseId,
    'users',
    ID.unique(),
    adminUser
  );
  createdEntities.admins.push(admin);
  createdEntities.users.push(admin);

  // Create regular users
  for (let i = 0; i < seedConfig.users; i++) {
    const role = faker.helpers.weightedArrayElement([
      { weight: 70, value: 'customer' },
      { weight: 20, value: 'vendor' },
      { weight: 10, value: 'runner' }
    ]);

    const user = {
      name: faker.person.fullName(),
      phone: generateSouthAfricanPhone(),
      email: faker.internet.email(),
      role: role,
      status: faker.helpers.weightedArrayElement([
        { weight: 90, value: 'active' },
        { weight: 8, value: 'inactive' },
        { weight: 2, value: 'suspended' }
      ]),
      isVerified: faker.datatype.boolean(0.85),
      lastActiveAt: faker.date.recent({ days: 30 }).toISOString(),
      pushToken: faker.string.alphanumeric(64),
      preferences: JSON.stringify({
        notifications: {
          push: faker.datatype.boolean(0.8),
          email: faker.datatype.boolean(0.6),
          sms: faker.datatype.boolean(0.3),
          orderUpdates: true,
          promotions: faker.datatype.boolean(0.4),
          newMessages: true,
        },
        privacy: {
          showOnlineStatus: faker.datatype.boolean(0.7),
          allowDirectMessages: faker.datatype.boolean(0.9),
          shareLocation: faker.datatype.boolean(0.5),
        },
        app: {
          theme: getRandomElement(['light', 'dark', 'system']),
          language: getRandomElement(['en', 'af', 'zu', 'xh']),
          currency: 'ZAR',
        },
      }),
      createdAt: faker.date.past({ years: 2 }).toISOString(),
      updatedAt: faker.date.recent({ days: 30 }).toISOString(),
    };

    try {
      const createdUser = await databases.createDocument(
        config.databaseId,
        'users',
        ID.unique(),
        user
      );
      
      createdEntities.users.push(createdUser);
      
      if (role === 'customer') {
        createdEntities.customers.push(createdUser);
      } else if (role === 'runner') {
        createdEntities.runners.push(createdUser);
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 50));
    } catch (error) {
      console.warn(`Failed to create user ${i}:`, error.message);
    }
  }

  console.log(`✅ Created ${createdEntities.users.length} users`);
}

async function seedCategories() {
  console.log('📂 Seeding categories...');

  // Create main categories
  const mainCategories = [
    { name: 'Food & Beverages', icon: '🍔', color: '#FF6B35' },
    { name: 'Grocery & Essentials', icon: '🛒', color: '#4ECDC4' },
    { name: 'Pharmacy & Health', icon: '💊', color: '#45B7D1' },
    { name: 'Electronics', icon: '📱', color: '#96CEB4' },
    { name: 'Fashion & Beauty', icon: '👗', color: '#FFEAA7' },
  ];

  for (let i = 0; i < mainCategories.length; i++) {
    const category = {
      name: mainCategories[i].name,
      slug: mainCategories[i].name.toLowerCase().replace(/\s+/g, '-').replace(/&/g, 'and'),
      description: faker.lorem.sentence(),
      icon: mainCategories[i].icon,
      color: mainCategories[i].color,
      sortOrder: i + 1,
      isActive: true,
      metadata: JSON.stringify({
        featured: faker.datatype.boolean(0.6),
        trending: faker.datatype.boolean(0.3),
      }),
    };

    try {
      const createdCategory = await databases.createDocument(
        config.databaseId,
        'categories',
        ID.unique(),
        category
      );
      createdEntities.categories.push(createdCategory);

      // Create subcategories
      const subcategoryCount = faker.number.int({ min: 2, max: 4 });
      for (let j = 0; j < subcategoryCount; j++) {
        const subcategory = {
          name: faker.commerce.department(),
          slug: faker.helpers.slugify(faker.commerce.department()).toLowerCase(),
          description: faker.lorem.sentence(),
          parentId: createdCategory.$id,
          sortOrder: j + 1,
          isActive: faker.datatype.boolean(0.9),
        };

        await databases.createDocument(
          config.databaseId,
          'categories',
          ID.unique(),
          subcategory
        );
      }

      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`Failed to create category ${i}:`, error.message);
    }
  }

  console.log(`✅ Created ${createdEntities.categories.length} main categories with subcategories`);
}

async function seedVendors() {
  console.log('🏪 Seeding vendors...');

  // Get vendor users
  const vendorUsers = createdEntities.users.filter(user => user.role === 'vendor');
  
  for (const vendorUser of vendorUsers) {
    const province = getRandomElement(southAfricanData.provinces);
    const cities = southAfricanData.cities[province] || ['Johannesburg'];
    const city = getRandomElement(cities);
    
    const businessNames = [
      `${faker.person.lastName()}'s ${faker.company.buzzNoun()}`,
      `${city} ${faker.company.buzzNoun()}`,
      `${getRandomElement(['Mama', 'Uncle', 'Auntie'])} ${faker.person.firstName()}'s Place`,
      `${faker.company.buzzAdjective()} ${faker.company.buzzNoun()}`,
    ];

    const vendor = {
      name: getRandomElement(businessNames),
      slug: faker.helpers.slugify(getRandomElement(businessNames)).toLowerCase(),
      description: faker.company.catchPhrase(),
      ownerId: vendorUser.$id,
      businessType: getRandomElement(southAfricanData.businessTypes),
      categories: getRandomElements(southAfricanData.foodCategories, faker.number.int({ min: 1, max: 3 })),
      location: `${faker.location.streetAddress()}, ${city}, ${province}`,
      coordinates: JSON.stringify({
        lat: faker.location.latitude({ min: -35, max: -22 }),
        lng: faker.location.longitude({ min: 16, max: 33 })
      }),
      deliveryRadius: faker.number.float({ min: 5, max: 25, fractionDigits: 1 }),
      status: faker.helpers.weightedArrayElement([
        { weight: 80, value: 'active' },
        { weight: 15, value: 'pending' },
        { weight: 5, value: 'inactive' }
      ]),
      rating: faker.number.float({ min: 3.0, max: 5.0, fractionDigits: 1 }),
      reviewCount: faker.number.int({ min: 0, max: 500 }),
      imageUrl: faker.image.urlLoremFlickr({ category: 'restaurant' }),
      coverImageUrl: faker.image.urlLoremFlickr({ category: 'food' }),
      phone: generateSouthAfricanPhone(),
      email: faker.internet.email(),
      website: faker.internet.url(),
      businessHours: JSON.stringify({
        monday: { isOpen: true, openTime: '08:00', closeTime: '20:00' },
        tuesday: { isOpen: true, openTime: '08:00', closeTime: '20:00' },
        wednesday: { isOpen: true, openTime: '08:00', closeTime: '20:00' },
        thursday: { isOpen: true, openTime: '08:00', closeTime: '20:00' },
        friday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
        saturday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
        sunday: { isOpen: faker.datatype.boolean(0.7), openTime: '10:00', closeTime: '18:00' },
      }),
      deliveryFee: faker.number.float({ min: 15, max: 50, fractionDigits: 2 }),
      minimumOrder: faker.number.float({ min: 30, max: 100, fractionDigits: 2 }),
      isActive: true,
      isFeatured: faker.datatype.boolean(0.3),
      verifiedAt: faker.datatype.boolean(0.8) ? faker.date.past().toISOString() : null,
      createdAt: faker.date.past({ years: 1 }).toISOString(),
      updatedAt: faker.date.recent({ days: 7 }).toISOString(),
    };

    try {
      const createdVendor = await databases.createDocument(
        config.databaseId,
        'vendors',
        ID.unique(),
        vendor
      );
      createdEntities.vendors.push(createdVendor);
      
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`Failed to create vendor for user ${vendorUser.$id}:`, error.message);
    }
  }

  console.log(`✅ Created ${createdEntities.vendors.length} vendors`);
}

async function seedMenuItems() {
  console.log('🍔 Seeding menu items...');

  for (const vendor of createdEntities.vendors) {
    const itemCount = faker.number.int({ min: 5, max: 15 });
    
    for (let i = 0; i < itemCount; i++) {
      const isTraditional = faker.datatype.boolean(0.4);
      const itemName = isTraditional 
        ? getRandomElement(southAfricanData.traditionalFoods)
        : faker.commerce.productName();

      const menuItem = {
        vendorId: vendor.$id,
        categoryId: createdEntities.categories.length > 0 ? getRandomElement(createdEntities.categories).$id : null,
        name: itemName,
        slug: faker.helpers.slugify(itemName).toLowerCase(),
        description: faker.commerce.productDescription(),
        price: faker.number.float({ min: 25, max: 200, fractionDigits: 2 }),
        compareAtPrice: faker.datatype.boolean(0.3) ? faker.number.float({ min: 250, max: 300, fractionDigits: 2 }) : null,
        cost: faker.number.float({ min: 15, max: 80, fractionDigits: 2 }),
        sku: faker.string.alphanumeric(8).toUpperCase(),
        images: [
          faker.image.urlLoremFlickr({ category: 'food' }),
          faker.image.urlLoremFlickr({ category: 'meal' })
        ],
        category: getRandomElement(southAfricanData.foodCategories),
        tags: getRandomElements(['spicy', 'vegetarian', 'halaal', 'traditional', 'popular', 'new'], faker.number.int({ min: 1, max: 3 })),
        variants: JSON.stringify([
          {
            name: 'Size',
            options: ['Regular', 'Large'],
            prices: { 'Large': faker.number.float({ min: 10, max: 30, fractionDigits: 2 }) }
          }
        ]),
        options: JSON.stringify([
          {
            name: 'Extras',
            type: 'multiple',
            required: false,
            choices: [
              { name: 'Extra Sauce', price: 5 },
              { name: 'Extra Meat', price: 15 },
              { name: 'Extra Cheese', price: 10 }
            ]
          }
        ]),
        nutritionInfo: JSON.stringify({
          calories: faker.number.int({ min: 200, max: 800 }),
          protein: faker.number.int({ min: 10, max: 40 }),
          carbs: faker.number.int({ min: 20, max: 60 }),
          fat: faker.number.int({ min: 5, max: 30 }),
        }),
        allergens: getRandomElements(['nuts', 'dairy', 'gluten', 'eggs'], faker.number.int({ min: 0, max: 2 })),
        preparationTime: faker.number.int({ min: 10, max: 45 }),
        inventory: faker.number.int({ min: 0, max: 100 }),
        trackInventory: faker.datatype.boolean(0.6),
        available: faker.datatype.boolean(0.9),
        isActive: true,
        isFeatured: faker.datatype.boolean(0.2),
        sortOrder: i + 1,
        rating: faker.number.float({ min: 3.5, max: 5.0, fractionDigits: 1 }),
        reviewCount: faker.number.int({ min: 0, max: 100 }),
        salesCount: faker.number.int({ min: 0, max: 500 }),
        createdAt: faker.date.past({ months: 6 }).toISOString(),
        updatedAt: faker.date.recent({ days: 14 }).toISOString(),
      };

      try {
        const createdMenuItem = await databases.createDocument(
          config.databaseId,
          'menu-items',
          ID.unique(),
          menuItem
        );
        createdEntities.menuItems.push(createdMenuItem);
        
        await new Promise(resolve => setTimeout(resolve, 50));
      } catch (error) {
        console.warn(`Failed to create menu item for vendor ${vendor.$id}:`, error.message);
      }
    }
  }

  console.log(`✅ Created ${createdEntities.menuItems.length} menu items`);
}

async function seedAddresses() {
  console.log('🏠 Seeding addresses...');

  for (const customer of createdEntities.customers.slice(0, seedConfig.addresses)) {
    const province = getRandomElement(southAfricanData.provinces);
    const cities = southAfricanData.cities[province] || ['Johannesburg'];
    const city = getRandomElement(cities);

    const addressTypes = ['home', 'work', 'other'];
    const addressCount = faker.number.int({ min: 1, max: 3 });

    for (let i = 0; i < addressCount; i++) {
      const address = {
        userId: customer.$id,
        type: addressTypes[i] || 'other',
        label: faker.helpers.arrayElement(['Home', 'Work', 'Parents House', 'Office', 'Apartment']),
        street: faker.location.streetAddress(),
        city: city,
        province: province,
        postalCode: faker.location.zipCode('####'),
        country: 'South Africa',
        coordinates: JSON.stringify({
          lat: faker.location.latitude({ min: -35, max: -22 }),
          lng: faker.location.longitude({ min: 16, max: 33 })
        }),
        instructions: faker.helpers.arrayElement([
          'Ring the bell twice',
          'Use the side gate',
          'Apartment 4B, second floor',
          'Behind the blue gate',
          'Next to the spaza shop',
          null
        ]),
        isDefault: i === 0,
        isActive: true,
      };

      try {
        await databases.createDocument(
          config.databaseId,
          'addresses',
          ID.unique(),
          address
        );
        await new Promise(resolve => setTimeout(resolve, 50));
      } catch (error) {
        console.warn(`Failed to create address for user ${customer.$id}:`, error.message);
      }
    }
  }

  console.log(`✅ Created addresses for ${Math.min(createdEntities.customers.length, seedConfig.addresses)} customers`);
}

async function seedRunnerProfiles() {
  console.log('🏃‍♂️ Seeding runner profiles...');

  for (const runner of createdEntities.runners.slice(0, seedConfig.runnerProfiles)) {
    const vehicleType = getRandomElement(southAfricanData.vehicleTypes);

    const runnerProfile = {
      userId: runner.$id,
      vehicleType: vehicleType,
      vehicleDetails: JSON.stringify({
        make: vehicleType === 'car' ? faker.vehicle.manufacturer() : null,
        model: vehicleType === 'car' ? faker.vehicle.model() : null,
        year: vehicleType === 'car' ? faker.date.past({ years: 10 }).getFullYear() : null,
        color: faker.vehicle.color(),
        licensePlate: vehicleType !== 'walking' ? faker.vehicle.vrm() : null,
      }),
      licenseNumber: vehicleType !== 'walking' ? faker.string.alphanumeric(10).toUpperCase() : null,
      licenseExpiryDate: vehicleType !== 'walking' ? faker.date.future({ years: 2 }).toISOString() : null,
      workingAreas: getRandomElements(southAfricanData.townships, faker.number.int({ min: 2, max: 5 })),
      maxDeliveryRadius: faker.number.float({ min: 5, max: 20, fractionDigits: 1 }),
      isAvailable: faker.datatype.boolean(0.7),
      isOnline: faker.datatype.boolean(0.4),
      currentLocation: JSON.stringify({
        lat: faker.location.latitude({ min: -35, max: -22 }),
        lng: faker.location.longitude({ min: 16, max: 33 })
      }),
      rating: faker.number.float({ min: 3.5, max: 5.0, fractionDigits: 1 }),
      reviewCount: faker.number.int({ min: 0, max: 200 }),
      totalDeliveries: faker.number.int({ min: 0, max: 1000 }),
      totalEarnings: faker.number.float({ min: 0, max: 50000, fractionDigits: 2 }),
      verificationStatus: faker.helpers.weightedArrayElement([
        { weight: 70, value: 'approved' },
        { weight: 20, value: 'pending' },
        { weight: 10, value: 'rejected' }
      ]),
      verifiedAt: faker.datatype.boolean(0.7) ? faker.date.past().toISOString() : null,
      documentsUploaded: ['id_document', 'drivers_license', 'vehicle_registration'],
      backgroundCheckStatus: faker.helpers.weightedArrayElement([
        { weight: 80, value: 'approved' },
        { weight: 15, value: 'pending' },
        { weight: 5, value: 'rejected' }
      ]),
      backgroundCheckDate: faker.date.past({ months: 6 }).toISOString(),
      createdAt: faker.date.past({ months: 12 }).toISOString(),
      updatedAt: faker.date.recent({ days: 7 }).toISOString(),
    };

    try {
      await databases.createDocument(
        config.databaseId,
        'runner-profiles',
        ID.unique(),
        runnerProfile
      );
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`Failed to create runner profile for user ${runner.$id}:`, error.message);
    }
  }

  console.log(`✅ Created ${Math.min(createdEntities.runners.length, seedConfig.runnerProfiles)} runner profiles`);
}

async function seedOrders() {
  console.log('📦 Seeding orders...');

  for (let i = 0; i < seedConfig.orders; i++) {
    const customer = getRandomElement(createdEntities.customers);
    const vendor = getRandomElement(createdEntities.vendors);
    const runner = faker.datatype.boolean(0.8) ? getRandomElement(createdEntities.runners) : null;

    // Generate order items
    const vendorMenuItems = createdEntities.menuItems.filter(item => item.vendorId === vendor.$id);
    const orderItemCount = faker.number.int({ min: 1, max: 4 });
    const selectedItems = getRandomElements(vendorMenuItems, Math.min(orderItemCount, vendorMenuItems.length));

    const orderItems = selectedItems.map(item => ({
      itemId: item.$id,
      name: item.name,
      quantity: faker.number.int({ min: 1, max: 3 }),
      price: parseFloat(item.price),
      total: parseFloat(item.price) * faker.number.int({ min: 1, max: 3 }),
      options: faker.datatype.boolean(0.3) ? [
        { name: 'Size', choice: 'Large', price: 15 },
        { name: 'Extra', choice: 'Cheese', price: 10 }
      ] : [],
    }));

    const subtotal = orderItems.reduce((sum, item) => sum + item.total, 0);
    const deliveryFee = parseFloat(vendor.deliveryFee) || 25;
    const serviceFee = subtotal * 0.05;
    const tax = 0; // No VAT on food in SA
    const tip = faker.datatype.boolean(0.6) ? faker.number.float({ min: 10, max: 50, fractionDigits: 2 }) : 0;
    const total = subtotal + deliveryFee + serviceFee + tax + tip;

    const order = {
      orderNumber: generateOrderNumber(),
      customerId: customer.$id,
      vendorId: vendor.$id,
      runnerId: runner?.$id || null,
      items: JSON.stringify(orderItems),
      subtotal: subtotal,
      discount: 0,
      deliveryFee: deliveryFee,
      serviceFee: serviceFee,
      tax: tax,
      tip: tip,
      total: total,
      currency: 'ZAR',
      status: faker.helpers.weightedArrayElement([
        { weight: 10, value: 'pending' },
        { weight: 15, value: 'accepted' },
        { weight: 10, value: 'preparing' },
        { weight: 10, value: 'ready' },
        { weight: 15, value: 'picked_up' },
        { weight: 10, value: 'en_route' },
        { weight: 25, value: 'delivered' },
        { weight: 5, value: 'cancelled' }
      ]),
      paymentMethod: faker.helpers.weightedArrayElement([
        { weight: 60, value: 'card' },
        { weight: 25, value: 'mpesa' },
        { weight: 10, value: 'cash' },
        { weight: 5, value: 'voucher' }
      ]),
      paymentStatus: faker.helpers.weightedArrayElement([
        { weight: 80, value: 'paid' },
        { weight: 10, value: 'pending' },
        { weight: 5, value: 'failed' },
        { weight: 5, value: 'refunded' }
      ]),
      deliveryAddress: JSON.stringify({
        street: faker.location.streetAddress(),
        city: getRandomElement(Object.values(southAfricanData.cities).flat()),
        province: getRandomElement(southAfricanData.provinces),
        postalCode: faker.location.zipCode('####'),
        coordinates: {
          lat: faker.location.latitude({ min: -35, max: -22 }),
          lng: faker.location.longitude({ min: 16, max: 33 })
        },
        instructions: faker.helpers.arrayElement([
          'Ring the bell twice',
          'Use the side gate',
          'Call when you arrive',
          null
        ])
      }),
      deliveryInstructions: faker.helpers.arrayElement([
        'Please call when you arrive',
        'Leave at the door',
        'Ring the bell',
        'Contact security first',
        null
      ]),
      estimatedDeliveryTime: faker.date.future({ days: 1 }).toISOString(),
      actualDeliveryTime: faker.datatype.boolean(0.7) ? faker.date.recent({ days: 1 }).toISOString() : null,
      preparationTime: faker.number.int({ min: 15, max: 45 }),
      notes: faker.helpers.arrayElement([
        'Extra spicy please',
        'No onions',
        'Well done',
        'Extra sauce on the side',
        null
      ]),
      createdAt: faker.date.past({ months: 3 }).toISOString(),
      updatedAt: faker.date.recent({ days: 1 }).toISOString(),
      completedAt: faker.datatype.boolean(0.8) ? faker.date.recent({ days: 1 }).toISOString() : null,
    };

    try {
      const createdOrder = await databases.createDocument(
        config.databaseId,
        'orders',
        ID.unique(),
        order
      );
      createdEntities.orders.push(createdOrder);
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`Failed to create order ${i}:`, error.message);
    }
  }

  console.log(`✅ Created ${createdEntities.orders.length} orders`);
}

async function seedPromotions() {
  console.log('🎁 Seeding promotions...');

  const promotionTypes = [
    { code: 'WELCOME10', name: 'Welcome Discount', type: 'percentage', value: 10 },
    { code: 'FIRSTORDER', name: 'First Order Special', type: 'fixed_amount', value: 25 },
    { code: 'WEEKEND20', name: 'Weekend Special', type: 'percentage', value: 20 },
    { code: 'STUDENT15', name: 'Student Discount', type: 'percentage', value: 15 },
    { code: 'FAMILY50', name: 'Family Deal', type: 'fixed_amount', value: 50 },
  ];

  for (let i = 0; i < seedConfig.promotions; i++) {
    const promoTemplate = getRandomElement(promotionTypes);
    const vendor = faker.datatype.boolean(0.6) ? getRandomElement(createdEntities.vendors) : null;

    const promotion = {
      code: `${promoTemplate.code}${faker.string.numeric(2)}`,
      name: promoTemplate.name,
      description: faker.lorem.sentence(),
      type: promoTemplate.type,
      discountType: promoTemplate.type === 'percentage' ? 'percentage' : 'fixed_amount',
      discountValue: promoTemplate.value,
      minimumOrderValue: faker.number.float({ min: 50, max: 200, fractionDigits: 2 }),
      maximumDiscount: promoTemplate.type === 'percentage' ? faker.number.float({ min: 50, max: 100, fractionDigits: 2 }) : null,
      vendorId: vendor?.$id || null,
      categoryIds: faker.datatype.boolean(0.3) ? getRandomElements(createdEntities.categories, 2).map(c => c.$id) : [],
      itemIds: [],
      userIds: [],
      usageLimit: faker.number.int({ min: 100, max: 1000 }),
      usageLimitPerUser: faker.number.int({ min: 1, max: 5 }),
      usageCount: faker.number.int({ min: 0, max: 50 }),
      isActive: faker.datatype.boolean(0.8),
      isPublic: faker.datatype.boolean(0.9),
      startsAt: faker.date.past({ days: 30 }).toISOString(),
      endsAt: faker.date.future({ days: 60 }).toISOString(),
      createdAt: faker.date.past({ days: 45 }).toISOString(),
      updatedAt: faker.date.recent({ days: 7 }).toISOString(),
    };

    try {
      await databases.createDocument(
        config.databaseId,
        'promotions',
        ID.unique(),
        promotion
      );
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`Failed to create promotion ${i}:`, error.message);
    }
  }

  console.log(`✅ Created ${seedConfig.promotions} promotions`);
}

// Main seeding function
async function runSeeders() {
  try {
    console.log('🌱 Starting HVPPYPlug+ Data Seeding...');
    console.log(`📊 Target: ${Object.values(seedConfig).reduce((a, b) => a + b, 0)} total records\n`);

    await seedUsers();
    await seedCategories();
    await seedVendors();
    await seedMenuItems();
    await seedAddresses();
    await seedRunnerProfiles();
    await seedOrders();
    await seedPromotions();

    console.log('\n🎉 Data seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   👥 Users: ${createdEntities.users.length}`);
    console.log(`   🏪 Vendors: ${createdEntities.vendors.length}`);
    console.log(`   📂 Categories: ${createdEntities.categories.length}`);
    console.log(`   🍔 Menu Items: ${createdEntities.menuItems.length}`);
    console.log(`   📦 Orders: ${createdEntities.orders.length}`);
    console.log(`   🎁 Promotions: ${seedConfig.promotions}`);

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run seeders if called directly
if (require.main === module) {
  runSeeders().catch(console.error);
}

module.exports = {
  runSeeders,
  seedConfig,
  southAfricanData,
};
