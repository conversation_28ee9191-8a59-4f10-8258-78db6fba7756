#!/usr/bin/env node

/**
 * HVPP<PERSON>lug+ Collection Creator v2
 * 
 * Creates collections with updated Appwrite v1.7+ permission format
 */

const { Client, Databases, ID } = require('node-appwrite');

// Configuration
const config = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://fra.cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID || '6880d655003a8926a438',
  apiKey: process.env.APPWRITE_API_KEY || '',
  databaseId: 'hvppyplug-main',
};

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);

// Updated permissions for Appwrite v1.7+
const permissions = {
  public: ['read("any")'],
  users: ['read("users")', 'create("users")', 'update("users")', 'delete("users")'],
  admins: ['read("any")', 'create("any")', 'update("any")', 'delete("any")'],
};

// Essential collections to create first
const essentialCollections = [
  {
    id: 'users',
    name: 'Users',
    permissions: permissions.public,
    documentSecurity: true,
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'phone', type: 'string', size: 20, required: true },
      { key: 'email', type: 'string', size: 255, required: false },
      { key: 'role', type: 'string', size: 20, required: true },
      { key: 'status', type: 'string', size: 20, required: true, default: 'active' },
      { key: 'isVerified', type: 'boolean', required: true, default: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'phone_unique', type: 'unique', attributes: ['phone'] },
      { key: 'role_index', type: 'key', attributes: ['role'] },
      { key: 'status_index', type: 'key', attributes: ['status'] },
    ]
  },
  {
    id: 'categories',
    name: 'Categories',
    permissions: permissions.public,
    documentSecurity: true,
    attributes: [
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'slug', type: 'string', size: 100, required: true },
      { key: 'description', type: 'string', size: 500, required: false },
      { key: 'icon', type: 'string', size: 100, required: false },
      { key: 'sortOrder', type: 'integer', required: true, default: 0 },
      { key: 'isActive', type: 'boolean', required: true, default: true },
    ],
    indexes: [
      { key: 'slug_unique', type: 'unique', attributes: ['slug'] },
      { key: 'active_index', type: 'key', attributes: ['isActive'] },
    ]
  },
  {
    id: 'vendors',
    name: 'Vendors',
    permissions: permissions.public,
    documentSecurity: true,
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'slug', type: 'string', size: 255, required: true },
      { key: 'ownerId', type: 'string', size: 255, required: true },
      { key: 'businessType', type: 'string', size: 50, required: true },
      { key: 'location', type: 'string', size: 1000, required: true },
      { key: 'status', type: 'string', size: 20, required: true, default: 'pending' },
      { key: 'rating', type: 'double', required: true, default: 0 },
      { key: 'reviewCount', type: 'integer', required: true, default: 0 },
      { key: 'isActive', type: 'boolean', required: true, default: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'slug_unique', type: 'unique', attributes: ['slug'] },
      { key: 'owner_index', type: 'key', attributes: ['ownerId'] },
      { key: 'status_index', type: 'key', attributes: ['status'] },
    ]
  },
  {
    id: 'menu-items',
    name: 'Menu Items',
    permissions: permissions.public,
    documentSecurity: true,
    attributes: [
      { key: 'vendorId', type: 'string', size: 255, required: true },
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'slug', type: 'string', size: 255, required: true },
      { key: 'price', type: 'double', required: true },
      { key: 'category', type: 'string', size: 50, required: true },
      { key: 'available', type: 'boolean', required: true, default: true },
      { key: 'isActive', type: 'boolean', required: true, default: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'vendor_index', type: 'key', attributes: ['vendorId'] },
      { key: 'available_index', type: 'key', attributes: ['available'] },
    ]
  },
  {
    id: 'orders',
    name: 'Orders',
    permissions: permissions.users,
    documentSecurity: true,
    attributes: [
      { key: 'orderNumber', type: 'string', size: 50, required: true },
      { key: 'customerId', type: 'string', size: 255, required: true },
      { key: 'vendorId', type: 'string', size: 255, required: true },
      { key: 'total', type: 'double', required: true },
      { key: 'status', type: 'string', size: 50, required: true, default: 'pending' },
      { key: 'paymentStatus', type: 'string', size: 50, required: true, default: 'pending' },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'order_number_unique', type: 'unique', attributes: ['orderNumber'] },
      { key: 'customer_index', type: 'key', attributes: ['customerId'] },
      { key: 'vendor_index', type: 'key', attributes: ['vendorId'] },
      { key: 'status_index', type: 'key', attributes: ['status'] },
    ]
  }
];

async function checkDatabase() {
  try {
    console.log('🔍 Checking database...');
    const database = await databases.get(config.databaseId);
    console.log(`✅ Database found: ${database.name}`);
  } catch (error) {
    console.error('❌ Database not found:', error.message);
    throw error;
  }
}

async function createAttribute(collectionId, attr) {
  try {
    switch (attr.type) {
      case 'string':
        await databases.createStringAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.size,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
      case 'integer':
        await databases.createIntegerAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
        break;
      case 'double':
        await databases.createFloatAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
        break;
      case 'boolean':
        await databases.createBooleanAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
      case 'datetime':
        await databases.createDatetimeAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
    }
    console.log(`  ✅ Created attribute: ${attr.key}`);
  } catch (error) {
    if (error.code === 409) {
      console.log(`  ℹ️ Attribute ${attr.key} already exists`);
    } else {
      console.error(`  ❌ Failed to create attribute ${attr.key}:`, error.message);
    }
  }
}

async function createIndex(collectionId, index) {
  try {
    await databases.createIndex(
      config.databaseId,
      collectionId,
      index.key,
      index.type,
      index.attributes,
      index.orders
    );
    console.log(`  ✅ Created index: ${index.key}`);
  } catch (error) {
    if (error.code === 409) {
      console.log(`  ℹ️ Index ${index.key} already exists`);
    } else {
      console.error(`  ❌ Failed to create index ${index.key}:`, error.message);
    }
  }
}

async function createCollections() {
  console.log('🏗️ Creating essential collections...\n');

  for (const collection of essentialCollections) {
    try {
      console.log(`📋 Creating collection: ${collection.name} (${collection.id})`);
      
      // Create collection
      await databases.createCollection(
        config.databaseId,
        collection.id,
        collection.name,
        collection.permissions || [],
        collection.documentSecurity || false,
        true // enabled
      );
      console.log(`✅ Collection ${collection.id} created`);

      // Wait a bit for collection to be ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Add attributes
      console.log(`  📝 Adding attributes...`);
      for (const attr of collection.attributes) {
        await createAttribute(collection.id, attr);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Add indexes
      console.log(`  📊 Adding indexes...`);
      for (const index of collection.indexes) {
        await createIndex(collection.id, index);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      console.log(`✅ Collection ${collection.id} completed\n`);

    } catch (error) {
      if (error.code === 409) {
        console.log(`ℹ️ Collection ${collection.id} already exists\n`);
      } else {
        console.error(`❌ Failed to create collection ${collection.id}:`, error.message);
      }
    }
  }
}

async function main() {
  try {
    console.log('🚀 Starting HVPPYPlug+ Essential Collections Setup');
    console.log('='.repeat(60));

    await checkDatabase();
    await createCollections();
    
    console.log('🎉 Essential collections setup completed!');
    console.log('\n📊 Created Collections:');
    essentialCollections.forEach(col => {
      console.log(`  ✅ ${col.name} (${col.id})`);
    });
    
    console.log('\n🔍 Run verification: node verify-collections.js');
    console.log('🌱 Run seeding: node data-seeders.js');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, essentialCollections };
