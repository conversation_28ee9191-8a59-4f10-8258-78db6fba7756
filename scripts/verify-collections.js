#!/usr/bin/env node

/**
 * Collection Verification Script
 * 
 * Verifies that all required collections exist with proper schema
 */

const { Client, Databases } = require('node-appwrite');

// Configuration
const config = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID || 'hvppyplug',
  apiKey: process.env.APPWRITE_API_KEY || '',
  databaseId: 'hvppyplug-main',
};

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);

// Expected collections with their required attributes
const expectedCollections = {
  'users': {
    name: 'Users',
    requiredAttributes: ['name', 'phone', 'role', 'status', 'isVerified'],
    requiredIndexes: ['phone_unique', 'role_index', 'status_index'],
  },
  'addresses': {
    name: 'Addresses',
    requiredAttributes: ['userId', 'type', 'street', 'city', 'province', 'postalCode'],
    requiredIndexes: ['user_index', 'type_index'],
  },
  'categories': {
    name: 'Categories',
    requiredAttributes: ['name', 'slug', 'sortOrder', 'isActive'],
    requiredIndexes: ['slug_unique', 'active_index'],
  },
  'vendors': {
    name: 'Vendors',
    requiredAttributes: ['name', 'slug', 'ownerId', 'businessType', 'status', 'rating'],
    requiredIndexes: ['slug_unique', 'owner_index', 'status_index'],
  },
  'menu-items': {
    name: 'Menu Items',
    requiredAttributes: ['vendorId', 'name', 'slug', 'price', 'category', 'available'],
    requiredIndexes: ['vendor_index', 'slug_index', 'available_index'],
  },
  'orders': {
    name: 'Orders',
    requiredAttributes: ['orderNumber', 'customerId', 'vendorId', 'items', 'total', 'status'],
    requiredIndexes: ['order_number_unique', 'customer_index', 'vendor_index', 'status_index'],
  },
  'reviews': {
    name: 'Reviews',
    requiredAttributes: ['orderId', 'customerId', 'vendorId', 'type', 'rating'],
    requiredIndexes: ['order_unique', 'customer_index', 'vendor_index', 'rating_index'],
  },
  'messages': {
    name: 'Messages',
    requiredAttributes: ['conversationId', 'senderId', 'receiverId', 'type', 'isRead'],
    requiredIndexes: ['conversation_index', 'sender_index', 'receiver_index'],
  },
  'conversations': {
    name: 'Conversations',
    requiredAttributes: ['participants', 'type', 'isActive'],
    requiredIndexes: ['participants_index', 'active_index'],
  },
  'notifications': {
    name: 'Notifications',
    requiredAttributes: ['userId', 'type', 'title', 'body', 'isRead'],
    requiredIndexes: ['user_index', 'type_index', 'read_index'],
  },
  'promotions': {
    name: 'Promotions',
    requiredAttributes: ['code', 'name', 'type', 'discountType', 'discountValue', 'isActive'],
    requiredIndexes: ['code_unique', 'type_index', 'active_index'],
  },
  'promotion-usage': {
    name: 'Promotion Usage',
    requiredAttributes: ['promotionId', 'userId', 'orderId', 'discountAmount'],
    requiredIndexes: ['promotion_user_unique', 'promotion_index', 'user_index'],
  },
  'analytics-events': {
    name: 'Analytics Events',
    requiredAttributes: ['eventName', 'eventCategory', 'eventAction', 'createdAt'],
    requiredIndexes: ['event_index', 'category_index', 'created_index'],
  },
  'payments': {
    name: 'Payments',
    requiredAttributes: ['orderId', 'userId', 'vendorId', 'paymentMethod', 'amount', 'status'],
    requiredIndexes: ['order_unique', 'user_index', 'vendor_index', 'status_index'],
  },
  'runner-profiles': {
    name: 'Runner Profiles',
    requiredAttributes: ['userId', 'vehicleType', 'isAvailable', 'isOnline', 'verificationStatus'],
    requiredIndexes: ['user_unique', 'available_index', 'online_index', 'verification_index'],
  },
  'support-tickets': {
    name: 'Support Tickets',
    requiredAttributes: ['ticketNumber', 'userId', 'category', 'priority', 'subject', 'status'],
    requiredIndexes: ['ticket_number_unique', 'user_index', 'category_index', 'status_index'],
  },
  'vendor-settings': {
    name: 'Vendor Settings',
    requiredAttributes: ['vendorId', 'autoAcceptOrders', 'preparationTime'],
    requiredIndexes: ['vendor_unique'],
  },
  'otp-codes': {
    name: 'OTP Codes',
    requiredAttributes: ['phone', 'code', 'type', 'isUsed', 'expiresAt'],
    requiredIndexes: ['phone_index', 'code_index', 'expires_index'],
  },
};

// Verification results
const results = {
  collections: {
    found: [],
    missing: [],
    errors: [],
  },
  attributes: {
    valid: [],
    missing: [],
    errors: [],
  },
  indexes: {
    valid: [],
    missing: [],
    errors: [],
  },
  permissions: {
    valid: [],
    invalid: [],
    errors: [],
  },
};

async function verifyCollections() {
  console.log('🔍 Starting collection verification...\n');

  try {
    // Get all collections
    const collectionsResponse = await databases.listCollections(config.databaseId);
    const existingCollections = new Map();
    
    collectionsResponse.collections.forEach(collection => {
      existingCollections.set(collection.$id, collection);
    });

    console.log(`📊 Found ${existingCollections.size} collections in database\n`);

    // Verify each expected collection
    for (const [collectionId, expectedConfig] of Object.entries(expectedCollections)) {
      console.log(`🔍 Verifying collection: ${expectedConfig.name} (${collectionId})`);

      if (!existingCollections.has(collectionId)) {
        results.collections.missing.push(collectionId);
        console.log(`❌ Collection ${collectionId} not found`);
        continue;
      }

      const collection = existingCollections.get(collectionId);
      results.collections.found.push(collectionId);
      console.log(`✅ Collection ${collectionId} found`);

      // Verify attributes
      await verifyAttributes(collectionId, expectedConfig.requiredAttributes);

      // Verify indexes
      await verifyIndexes(collectionId, expectedConfig.requiredIndexes);

      // Verify permissions
      await verifyPermissions(collectionId, collection);

      console.log('');
    }

    // Check for unexpected collections
    const expectedIds = new Set(Object.keys(expectedCollections));
    const unexpectedCollections = [];
    
    existingCollections.forEach((collection, id) => {
      if (!expectedIds.has(id)) {
        unexpectedCollections.push(id);
      }
    });

    if (unexpectedCollections.length > 0) {
      console.log(`⚠️ Found ${unexpectedCollections.length} unexpected collections:`);
      unexpectedCollections.forEach(id => console.log(`   - ${id}`));
      console.log('');
    }

    // Generate report
    generateReport();

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

async function verifyAttributes(collectionId, requiredAttributes) {
  try {
    const attributesResponse = await databases.listAttributes(config.databaseId, collectionId);
    const existingAttributes = new Set(attributesResponse.attributes.map(attr => attr.key));

    const missingAttributes = requiredAttributes.filter(attr => !existingAttributes.has(attr));
    
    if (missingAttributes.length === 0) {
      results.attributes.valid.push(collectionId);
      console.log(`  ✅ All required attributes present (${requiredAttributes.length})`);
    } else {
      results.attributes.missing.push({
        collection: collectionId,
        missing: missingAttributes,
      });
      console.log(`  ❌ Missing attributes: ${missingAttributes.join(', ')}`);
    }

  } catch (error) {
    results.attributes.errors.push({
      collection: collectionId,
      error: error.message,
    });
    console.log(`  ❌ Error checking attributes: ${error.message}`);
  }
}

async function verifyIndexes(collectionId, requiredIndexes) {
  try {
    const indexesResponse = await databases.listIndexes(config.databaseId, collectionId);
    const existingIndexes = new Set(indexesResponse.indexes.map(index => index.key));

    const missingIndexes = requiredIndexes.filter(index => !existingIndexes.has(index));
    
    if (missingIndexes.length === 0) {
      results.indexes.valid.push(collectionId);
      console.log(`  ✅ All required indexes present (${requiredIndexes.length})`);
    } else {
      results.indexes.missing.push({
        collection: collectionId,
        missing: missingIndexes,
      });
      console.log(`  ❌ Missing indexes: ${missingIndexes.join(', ')}`);
    }

  } catch (error) {
    results.indexes.errors.push({
      collection: collectionId,
      error: error.message,
    });
    console.log(`  ❌ Error checking indexes: ${error.message}`);
  }
}

async function verifyPermissions(collectionId, collection) {
  try {
    const hasPermissions = collection.permissions && collection.permissions.length > 0;
    const hasDocumentSecurity = collection.documentSecurity === true;

    if (hasPermissions || hasDocumentSecurity) {
      results.permissions.valid.push(collectionId);
      console.log(`  ✅ Permissions configured (${collection.permissions?.length || 0} rules, documentSecurity: ${hasDocumentSecurity})`);
    } else {
      results.permissions.invalid.push(collectionId);
      console.log(`  ⚠️ No permissions configured`);
    }

  } catch (error) {
    results.permissions.errors.push({
      collection: collectionId,
      error: error.message,
    });
    console.log(`  ❌ Error checking permissions: ${error.message}`);
  }
}

function generateReport() {
  console.log('\n📋 VERIFICATION REPORT');
  console.log('='.repeat(50));

  // Collections summary
  console.log(`\n📦 Collections:`);
  console.log(`  ✅ Found: ${results.collections.found.length}`);
  console.log(`  ❌ Missing: ${results.collections.missing.length}`);
  console.log(`  ⚠️ Errors: ${results.collections.errors.length}`);

  if (results.collections.missing.length > 0) {
    console.log(`\n❌ Missing Collections:`);
    results.collections.missing.forEach(id => console.log(`  - ${id}`));
  }

  // Attributes summary
  console.log(`\n🏷️ Attributes:`);
  console.log(`  ✅ Valid: ${results.attributes.valid.length}`);
  console.log(`  ❌ Missing: ${results.attributes.missing.length}`);
  console.log(`  ⚠️ Errors: ${results.attributes.errors.length}`);

  if (results.attributes.missing.length > 0) {
    console.log(`\n❌ Missing Attributes:`);
    results.attributes.missing.forEach(item => {
      console.log(`  ${item.collection}: ${item.missing.join(', ')}`);
    });
  }

  // Indexes summary
  console.log(`\n📊 Indexes:`);
  console.log(`  ✅ Valid: ${results.indexes.valid.length}`);
  console.log(`  ❌ Missing: ${results.indexes.missing.length}`);
  console.log(`  ⚠️ Errors: ${results.indexes.errors.length}`);

  if (results.indexes.missing.length > 0) {
    console.log(`\n❌ Missing Indexes:`);
    results.indexes.missing.forEach(item => {
      console.log(`  ${item.collection}: ${item.missing.join(', ')}`);
    });
  }

  // Permissions summary
  console.log(`\n🔐 Permissions:`);
  console.log(`  ✅ Valid: ${results.permissions.valid.length}`);
  console.log(`  ⚠️ Invalid: ${results.permissions.invalid.length}`);
  console.log(`  ❌ Errors: ${results.permissions.errors.length}`);

  if (results.permissions.invalid.length > 0) {
    console.log(`\n⚠️ Collections without permissions:`);
    results.permissions.invalid.forEach(id => console.log(`  - ${id}`));
  }

  // Overall status
  const hasErrors = results.collections.missing.length > 0 || 
                   results.attributes.missing.length > 0 || 
                   results.indexes.missing.length > 0;

  console.log(`\n🎯 Overall Status: ${hasErrors ? '❌ ISSUES FOUND' : '✅ ALL CHECKS PASSED'}`);

  if (hasErrors) {
    console.log('\n💡 Recommendations:');
    console.log('  1. Run the migration script to create missing collections');
    console.log('  2. Check Appwrite console for detailed error information');
    console.log('  3. Verify API key permissions');
    console.log('  4. Review deployment logs for any issues');
  }

  console.log('\n📄 Report saved to: logs/verification-report.json');
  
  // Save detailed report
  const fs = require('fs');
  const path = require('path');
  
  const logsDir = path.join(__dirname, '../logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  const reportPath = path.join(logsDir, 'verification-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    status: hasErrors ? 'ISSUES_FOUND' : 'ALL_CHECKS_PASSED',
    results,
    summary: {
      totalCollections: Object.keys(expectedCollections).length,
      foundCollections: results.collections.found.length,
      missingCollections: results.collections.missing.length,
      validAttributes: results.attributes.valid.length,
      missingAttributes: results.attributes.missing.length,
      validIndexes: results.indexes.valid.length,
      missingIndexes: results.indexes.missing.length,
      validPermissions: results.permissions.valid.length,
      invalidPermissions: results.permissions.invalid.length,
    },
  }, null, 2));
}

// Run verification if called directly
if (require.main === module) {
  verifyCollections().catch(console.error);
}

module.exports = { verifyCollections, expectedCollections };
