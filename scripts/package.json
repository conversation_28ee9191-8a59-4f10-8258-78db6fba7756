{"name": "hvppyplug-database-scripts", "version": "1.0.0", "description": "Database migration and seeding scripts for HVPPYPlug+", "main": "database-migration.js", "scripts": {"migrate": "node database-migration.js", "seed": "node data-seeders.js", "verify": "node verify-collections.js", "merge-config": "node merge-appwrite-config.js", "seed:dev": "NODE_ENV=development node data-seeders.js", "seed:staging": "NODE_ENV=staging node data-seeders.js", "backup": "node database-migration.js --step backup", "validate": "node database-migration.js --step validate"}, "dependencies": {"@faker-js/faker": "^8.4.1", "node-appwrite": "^13.0.0", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.11.16"}, "keywords": ["appwrite", "database", "migration", "seeding", "hvppyplug", "south-africa"], "author": "HVPPYPlug+ Team", "license": "MIT"}