#!/usr/bin/env node

/**
 * HVPPYPlug+ Fix Missing Indexes
 * 
 * Creates ALL missing indexes for optimal performance
 */

const { Client, Databases } = require('node-appwrite');

// Configuration
const config = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://fra.cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID || '6880d655003a8926a438',
  apiKey: process.env.APPWRITE_API_KEY || '',
  databaseId: 'hvppyplug-main',
};

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);

// All missing indexes that need to be created
const missingIndexes = [
  // Users collection
  { collection: 'users', key: 'status_index', type: 'key', attributes: ['status'] },
  
  // Addresses collection
  { collection: 'addresses', key: 'type_index', type: 'key', attributes: ['type'] },
  
  // Categories collection
  { collection: 'categories', key: 'active_index', type: 'key', attributes: ['isActive'] },
  
  // Vendors collection
  { collection: 'vendors', key: 'status_index', type: 'key', attributes: ['status'] },
  
  // Menu Items collection
  { collection: 'menu-items', key: 'slug_index', type: 'key', attributes: ['slug'] },
  { collection: 'menu-items', key: 'available_index', type: 'key', attributes: ['available'] },
  
  // Orders collection
  { collection: 'orders', key: 'status_index', type: 'key', attributes: ['status'] },
  
  // Reviews collection
  { collection: 'reviews', key: 'order_unique', type: 'unique', attributes: ['orderId'] },
  { collection: 'reviews', key: 'customer_index', type: 'key', attributes: ['customerId'] },
  { collection: 'reviews', key: 'rating_index', type: 'key', attributes: ['rating'] },
  
  // Messages collection
  { collection: 'messages', key: 'sender_index', type: 'key', attributes: ['senderId'] },
  { collection: 'messages', key: 'receiver_index', type: 'key', attributes: ['receiverId'] },
  
  // Conversations collection
  { collection: 'conversations', key: 'active_index', type: 'key', attributes: ['isActive'] },
  
  // Notifications collection
  { collection: 'notifications', key: 'type_index', type: 'key', attributes: ['type'] },
  { collection: 'notifications', key: 'read_index', type: 'key', attributes: ['isRead'] },
  
  // Promotions collection
  { collection: 'promotions', key: 'type_index', type: 'key', attributes: ['type'] },
  { collection: 'promotions', key: 'active_index', type: 'key', attributes: ['isActive'] },
  
  // Promotion Usage collection
  { collection: 'promotion-usage', key: 'promotion_user_unique', type: 'unique', attributes: ['promotionId', 'userId'] },
  { collection: 'promotion-usage', key: 'user_index', type: 'key', attributes: ['userId'] },
  
  // Analytics Events collection
  { collection: 'analytics-events', key: 'category_index', type: 'key', attributes: ['eventCategory'] },
  { collection: 'analytics-events', key: 'created_index', type: 'key', attributes: ['createdAt'] },
  
  // Payments collection
  { collection: 'payments', key: 'user_index', type: 'key', attributes: ['userId'] },
  { collection: 'payments', key: 'vendor_index', type: 'key', attributes: ['vendorId'] },
  { collection: 'payments', key: 'status_index', type: 'key', attributes: ['status'] },
  
  // Runner Profiles collection
  { collection: 'runner-profiles', key: 'available_index', type: 'key', attributes: ['isAvailable'] },
  { collection: 'runner-profiles', key: 'online_index', type: 'key', attributes: ['isOnline'] },
  { collection: 'runner-profiles', key: 'verification_index', type: 'key', attributes: ['verificationStatus'] },
  
  // Support Tickets collection
  { collection: 'support-tickets', key: 'user_index', type: 'key', attributes: ['userId'] },
  { collection: 'support-tickets', key: 'category_index', type: 'key', attributes: ['category'] },
  { collection: 'support-tickets', key: 'status_index', type: 'key', attributes: ['status'] },
  
  // OTP Codes collection
  { collection: 'otp-codes', key: 'code_index', type: 'key', attributes: ['code'] },
  { collection: 'otp-codes', key: 'expires_index', type: 'key', attributes: ['expiresAt'] },
];

async function createIndex(collectionId, index) {
  try {
    await databases.createIndex(
      config.databaseId,
      collectionId,
      index.key,
      index.type,
      index.attributes,
      index.orders
    );
    console.log(`    ✅ ${index.key} (${index.type})`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`    ℹ️ ${index.key} already exists`);
      return true;
    } else {
      console.log(`    ❌ ${index.key}: ${error.message}`);
      return false;
    }
  }
}

async function addMissingAttributes() {
  console.log('🔧 Adding missing attributes first...\n');

  // Add missing attributes to orders collection
  try {
    await databases.createStringAttribute(config.databaseId, 'orders', 'items', 5000, true);
    console.log('  ✅ Added items to orders');
  } catch (error) {
    console.log('  ℹ️ items attribute already exists in orders');
  }

  try {
    await databases.createStringAttribute(config.databaseId, 'orders', 'status', 50, true);
    console.log('  ✅ Added status to orders');
  } catch (error) {
    console.log('  ℹ️ status attribute already exists in orders');
  }

  // Add missing attributes to promotions collection
  try {
    await databases.createStringAttribute(config.databaseId, 'promotions', 'discountType', 20, false);
    console.log('  ✅ Added discountType to promotions');
  } catch (error) {
    console.log('  ℹ️ discountType attribute already exists in promotions');
  }

  // Add missing attributes to payments collection
  try {
    await databases.createStringAttribute(config.databaseId, 'payments', 'vendorId', 255, false);
    console.log('  ✅ Added vendorId to payments');
  } catch (error) {
    console.log('  ℹ️ vendorId attribute already exists in payments');
  }

  // Add missing attributes to menu-items collection
  try {
    await databases.createStringAttribute(config.databaseId, 'menu-items', 'description', 1000, false);
    console.log('  ✅ Added description to menu-items');
  } catch (error) {
    console.log('  ℹ️ description attribute already exists in menu-items');
  }

  console.log('  ✅ Missing attributes added\n');
  
  // Wait for attributes to be ready
  await new Promise(resolve => setTimeout(resolve, 3000));
}

async function fixAllIndexes() {
  console.log('📊 Creating ALL Missing Indexes...\n');

  let successCount = 0;
  let totalCount = missingIndexes.length;
  
  // Group indexes by collection for better organization
  const indexesByCollection = {};
  missingIndexes.forEach(index => {
    if (!indexesByCollection[index.collection]) {
      indexesByCollection[index.collection] = [];
    }
    indexesByCollection[index.collection].push(index);
  });

  // Create indexes for each collection
  for (const [collectionName, indexes] of Object.entries(indexesByCollection)) {
    console.log(`📋 ${collectionName} collection:`);
    
    for (const index of indexes) {
      const success = await createIndex(index.collection, index);
      if (success) successCount++;
      
      // Small delay between index creations
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(''); // Empty line for readability
  }

  return { successCount, totalCount };
}

async function main() {
  try {
    console.log('🔧 HVPPYPlug+ FIX ALL MISSING INDEXES');
    console.log('📊 Performance Optimization for ALL Collections');
    console.log('=' .repeat(60));
    
    console.log(`🎯 Target: ${missingIndexes.length} missing indexes\n`);

    // Step 1: Add missing attributes first
    await addMissingAttributes();
    
    // Step 2: Create all missing indexes
    const { successCount, totalCount } = await fixAllIndexes();
    
    console.log('🎉 INDEX CREATION COMPLETED!');
    console.log('=' .repeat(60));
    console.log('📊 FINAL SUMMARY:');
    console.log(`   ✅ Indexes Created: ${successCount}/${totalCount}`);
    console.log(`   📈 Performance: Optimized for all queries`);
    console.log(`   🚀 Database: Production-ready with full indexing`);
    
    if (successCount === totalCount) {
      console.log('\n🎯 ALL INDEXES SUCCESSFULLY CREATED!');
      console.log('🔍 Run verification: node verify-collections.js');
      console.log('🚀 Database is now fully optimized for MVP!');
    } else {
      console.log(`\n⚠️ ${totalCount - successCount} indexes had issues`);
      console.log('🔍 Check the output above for details');
    }
    
  } catch (error) {
    console.error('❌ Index creation failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, fixAllIndexes, missingIndexes };
