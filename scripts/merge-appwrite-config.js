#!/usr/bin/env node

/**
 * Merge Appwrite Configuration Files
 * 
 * This script combines the main appwrite configuration with additional
 * collection definitions to create a complete deployment configuration.
 */

const fs = require('fs');
const path = require('path');

// File paths
const mainConfigPath = path.join(__dirname, '../backend/appwrite-enhanced.json');
const part2ConfigPath = path.join(__dirname, '../backend/appwrite-collections-part2.json');
const part3ConfigPath = path.join(__dirname, '../backend/appwrite-collections-part3.json');
const outputPath = path.join(__dirname, '../backend/appwrite-complete.json');

function mergeConfigurations() {
  try {
    console.log('🔄 Merging Appwrite configuration files...');

    // Read main configuration
    const mainConfig = JSON.parse(fs.readFileSync(mainConfigPath, 'utf8'));
    console.log(`✅ Loaded main config with ${mainConfig.collections.length} collections`);

    // Read additional collections
    const part2Config = JSON.parse(fs.readFileSync(part2ConfigPath, 'utf8'));
    console.log(`✅ Loaded part 2 config with ${part2Config.collections.length} collections`);

    const part3Config = JSON.parse(fs.readFileSync(part3ConfigPath, 'utf8'));
    console.log(`✅ Loaded part 3 config with ${part3Config.collections.length} collections`);

    // Merge collections
    const allCollections = [
      ...mainConfig.collections,
      ...part2Config.collections,
      ...part3Config.collections
    ];

    // Create complete configuration
    const completeConfig = {
      ...mainConfig,
      collections: allCollections
    };

    // Validate configuration
    validateConfiguration(completeConfig);

    // Write merged configuration
    fs.writeFileSync(outputPath, JSON.stringify(completeConfig, null, 2));
    
    console.log(`🎉 Successfully merged configuration!`);
    console.log(`📊 Total collections: ${completeConfig.collections.length}`);
    console.log(`📊 Total functions: ${completeConfig.functions.length}`);
    console.log(`📊 Total buckets: ${completeConfig.buckets.length}`);
    console.log(`📄 Output file: ${outputPath}`);

    // Generate collection summary
    generateCollectionSummary(completeConfig);

  } catch (error) {
    console.error('❌ Error merging configurations:', error.message);
    process.exit(1);
  }
}

function validateConfiguration(config) {
  console.log('🔍 Validating configuration...');

  // Check for duplicate collection IDs
  const collectionIds = config.collections.map(c => c.id);
  const duplicateIds = collectionIds.filter((id, index) => collectionIds.indexOf(id) !== index);
  
  if (duplicateIds.length > 0) {
    throw new Error(`Duplicate collection IDs found: ${duplicateIds.join(', ')}`);
  }

  // Validate required fields
  config.collections.forEach(collection => {
    if (!collection.id || !collection.name || !collection.databaseId) {
      throw new Error(`Invalid collection: ${JSON.stringify(collection)}`);
    }

    // Validate attributes
    if (!collection.attributes || !Array.isArray(collection.attributes)) {
      throw new Error(`Collection ${collection.id} missing attributes array`);
    }

    // Validate indexes
    if (!collection.indexes || !Array.isArray(collection.indexes)) {
      throw new Error(`Collection ${collection.id} missing indexes array`);
    }

    // Validate permissions
    if (!collection.permissions || !Array.isArray(collection.permissions)) {
      throw new Error(`Collection ${collection.id} missing permissions array`);
    }
  });

  console.log('✅ Configuration validation passed');
}

function generateCollectionSummary(config) {
  console.log('\n📋 Collection Summary:');
  console.log('='.repeat(50));

  config.collections.forEach((collection, index) => {
    console.log(`${index + 1}. ${collection.name} (${collection.id})`);
    console.log(`   - Attributes: ${collection.attributes.length}`);
    console.log(`   - Indexes: ${collection.indexes.length}`);
    console.log(`   - Permissions: ${collection.permissions.length}`);
    console.log(`   - Document Security: ${collection.documentSecurity ? 'Yes' : 'No'}`);
    console.log('');
  });

  // Generate deployment instructions
  generateDeploymentInstructions(config);
}

function generateDeploymentInstructions(config) {
  const instructions = `
# HVPPYPlug+ Database Deployment Instructions

## Prerequisites
1. Install Appwrite CLI: \`npm install -g appwrite-cli\`
2. Login to Appwrite: \`appwrite login\`
3. Set project context: \`appwrite init project\`

## Deployment Commands

### Option 1: Deploy Complete Configuration
\`\`\`bash
cd backend
appwrite deploy --force
\`\`\`

### Option 2: Deploy Collections Only
\`\`\`bash
cd backend
appwrite deploy collection --force
\`\`\`

### Option 3: Deploy Individual Collections
\`\`\`bash
# Deploy specific collections
appwrite deploy collection users --force
appwrite deploy collection vendors --force
# ... repeat for other collections
\`\`\`

## Post-Deployment Steps

1. **Verify Collections**: Check Appwrite console for all collections
2. **Test Permissions**: Verify role-based access control
3. **Create Initial Data**: Add categories, admin users, etc.
4. **Update Environment Variables**: Ensure all apps have correct database IDs

## Rollback Instructions

If deployment fails:
1. Check Appwrite console for error details
2. Fix configuration issues
3. Re-run deployment with \`--force\` flag
4. For critical issues, restore from backup

## Monitoring

- Monitor collection performance in Appwrite console
- Set up alerts for database errors
- Regular backup of critical data
- Performance optimization based on usage patterns

Generated on: ${new Date().toISOString()}
Total Collections: ${config.collections.length}
`;

  const instructionsPath = path.join(__dirname, '../docs/deployment-instructions.md');
  fs.writeFileSync(instructionsPath, instructions);
  console.log(`📖 Deployment instructions saved to: ${instructionsPath}`);
}

// Run the merge process
if (require.main === module) {
  mergeConfigurations();
}

module.exports = { mergeConfigurations, validateConfiguration };
