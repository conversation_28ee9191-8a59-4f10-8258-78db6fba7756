#!/bin/bash

# HVPPYPlug+ Database Seeding Script
# Runs the data seeders with proper error handling and logging

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/../logs"
LOG_FILE="$LOG_DIR/seeding-$(date +%Y%m%d-%H%M%S).log"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} ✅ $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} ⚠️  $1"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} ❌ $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check if npm/pnpm is available
    if command -v pnpm &> /dev/null; then
        PACKAGE_MANAGER="pnpm"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
    else
        print_error "Neither npm nor pnpm is available. Please install a package manager."
        exit 1
    fi
    
    print_success "Using $PACKAGE_MANAGER as package manager"
    
    # Check if .env file exists
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        print_warning ".env file not found. Make sure environment variables are set."
        print_status "Required variables: APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID, APPWRITE_API_KEY"
    fi
    
    print_success "Prerequisites check completed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    cd "$SCRIPT_DIR"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in scripts directory"
        exit 1
    fi
    
    if [ "$PACKAGE_MANAGER" = "pnpm" ]; then
        pnpm install
    else
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Function to verify database connection
verify_connection() {
    print_status "Verifying database connection..."
    
    # Load environment variables
    if [ -f "$SCRIPT_DIR/.env" ]; then
        export $(cat "$SCRIPT_DIR/.env" | grep -v '^#' | xargs)
    fi
    
    # Check required environment variables
    if [ -z "$APPWRITE_ENDPOINT" ] || [ -z "$APPWRITE_PROJECT_ID" ] || [ -z "$APPWRITE_API_KEY" ]; then
        print_error "Missing required environment variables"
        print_status "Please set: APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID, APPWRITE_API_KEY"
        exit 1
    fi
    
    print_success "Environment variables configured"
}

# Function to run collection verification
verify_collections() {
    print_status "Verifying database collections..."
    
    if node verify-collections.js >> "$LOG_FILE" 2>&1; then
        print_success "Collection verification completed"
    else
        print_warning "Collection verification had issues. Check log file: $LOG_FILE"
        print_status "Continuing with seeding..."
    fi
}

# Function to run the seeders
run_seeders() {
    print_status "Starting data seeding process..."
    print_status "This may take several minutes depending on the data volume..."
    
    # Determine environment
    ENV=${NODE_ENV:-development}
    print_status "Running in $ENV environment"
    
    # Run the seeding script
    if node data-seeders.js >> "$LOG_FILE" 2>&1; then
        print_success "Data seeding completed successfully!"
    else
        print_error "Data seeding failed. Check log file: $LOG_FILE"
        print_status "Last few lines of the log:"
        tail -10 "$LOG_FILE"
        exit 1
    fi
}

# Function to generate summary report
generate_report() {
    print_status "Generating seeding report..."
    
    # Create a simple report
    cat > "$LOG_DIR/seeding-report-$(date +%Y%m%d-%H%M%S).md" << EOF
# HVPPYPlug+ Seeding Report

**Date**: $(date)
**Environment**: ${NODE_ENV:-development}
**Status**: ✅ Completed Successfully

## Summary
- **Log File**: $LOG_FILE
- **Script Directory**: $SCRIPT_DIR
- **Package Manager**: $PACKAGE_MANAGER

## Next Steps
1. Verify data in Appwrite console
2. Test client applications with seeded data
3. Run integration tests
4. Check performance with realistic data volumes

## Verification Commands
\`\`\`bash
# Verify collections
cd scripts && node verify-collections.js

# Check specific collection counts
# (Replace with actual Appwrite CLI commands)
\`\`\`

---
Generated by HVPPYPlug+ seeding script
EOF

    print_success "Report generated: $LOG_DIR/seeding-report-$(date +%Y%m%d-%H%M%S).md"
}

# Function to cleanup on exit
cleanup() {
    if [ $? -ne 0 ]; then
        print_error "Seeding process failed. Check logs for details."
        print_status "Log file: $LOG_FILE"
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    print_status "🌱 HVPPYPlug+ Database Seeding Started"
    print_status "================================================"
    
    check_prerequisites
    install_dependencies
    verify_connection
    verify_collections
    run_seeders
    generate_report
    
    print_success "🎉 Seeding process completed successfully!"
    print_status "================================================"
    print_status "📊 Check Appwrite console to verify seeded data"
    print_status "📄 Log file: $LOG_FILE"
    print_status "🔍 Run 'cd scripts && node verify-collections.js' to verify"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "HVPPYPlug+ Database Seeding Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify-only  Only verify collections, don't seed"
        echo "  --force        Force seeding even if data exists"
        echo ""
        echo "Environment Variables:"
        echo "  NODE_ENV              Environment (development, staging, production)"
        echo "  APPWRITE_ENDPOINT     Appwrite server endpoint"
        echo "  APPWRITE_PROJECT_ID   Appwrite project ID"
        echo "  APPWRITE_API_KEY      Appwrite API key"
        echo ""
        exit 0
        ;;
    --verify-only)
        print_status "Running verification only..."
        check_prerequisites
        install_dependencies
        verify_connection
        verify_collections
        print_success "Verification completed"
        exit 0
        ;;
    --force)
        print_warning "Force mode enabled - will seed even if data exists"
        export FORCE_SEED=true
        main
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        print_status "Use --help for usage information"
        exit 1
        ;;
esac
