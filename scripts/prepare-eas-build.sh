#!/bin/bash

# Prepare EAS Build Script for pnpm Monorepo
# This script ensures proper setup for EAS builds in a pnpm monorepo environment

set -e

echo "🚀 Preparing EAS build for pnpm monorepo..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the monorepo root
if [ ! -f "pnpm-workspace.yaml" ]; then
    print_error "This script must be run from the monorepo root directory"
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    print_error "pnpm is not installed. Please install pnpm first."
    exit 1
fi

# Check pnpm version
PNPM_VERSION=$(pnpm --version)
print_status "Using pnpm version: $PNPM_VERSION"

# Clean previous builds and caches
print_status "Cleaning previous builds and caches..."
rm -rf node_modules/.cache
rm -rf .pnpm-cache
find apps -name "node_modules/.cache" -type d -exec rm -rf {} + 2>/dev/null || true
find packages -name "node_modules/.cache" -type d -exec rm -rf {} + 2>/dev/null || true

# Install dependencies with frozen lockfile
print_status "Installing dependencies with frozen lockfile..."
pnpm install --frozen-lockfile

# Build shared packages
print_status "Building shared packages..."
pnpm --filter @hvppyplug/common build
pnpm --filter @hvppyplug/compound-components build
pnpm --filter @hvppyplug/mobile-services build

# Verify Metro configurations exist
print_status "Verifying Metro configurations..."
for app in customer-app vendor-app runner-app; do
    if [ ! -f "apps/$app/metro.config.js" ]; then
        print_warning "Metro config missing for $app"
    else
        print_success "Metro config found for $app"
    fi
done

# Verify EAS configurations
print_status "Verifying EAS configurations..."
for app in customer-app vendor-app runner-app; do
    if [ ! -f "apps/$app/eas.json" ]; then
        print_warning "EAS config missing for $app"
    else
        print_success "EAS config found for $app"
    fi
done

# Check for .npmrc configuration
if [ -f ".npmrc" ]; then
    if grep -q "node-linker=hoisted" .npmrc; then
        print_success ".npmrc is properly configured for hoisting"
    else
        print_warning ".npmrc may not be properly configured for React Native"
    fi
else
    print_warning ".npmrc file not found"
fi

# Set environment variables for EAS build
export EXPO_USE_METRO_WORKSPACE_ROOT=1
export PNPM_CACHE_FOLDER=.pnpm-cache
export CI=1

print_success "EAS build preparation completed successfully!"
print_status "Environment variables set:"
print_status "  EXPO_USE_METRO_WORKSPACE_ROOT=1"
print_status "  PNPM_CACHE_FOLDER=.pnpm-cache"
print_status "  CI=1"

echo ""
print_status "You can now run EAS build commands from individual app directories:"
print_status "  cd apps/customer-app && eas build --platform android"
print_status "  cd apps/vendor-app && eas build --platform android"
print_status "  cd apps/runner-app && eas build --platform android"
