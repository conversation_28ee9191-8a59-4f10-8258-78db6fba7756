#!/usr/bin/env node

/**
 * HVPPYPlug+ Database Migration Script
 * 
 * This script handles the migration from the current database structure
 * to the enhanced schema with all 18 collections.
 */

const { Client, Databases, Storage, Users } = require('node-appwrite');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID || 'hvppyplug',
  apiKey: process.env.APPWRITE_API_KEY || '',
  databaseId: 'hvppyplug-main',
};

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);
const storage = new Storage(client);
const users = new Users(client);

// Migration state tracking
const migrationState = {
  currentStep: 0,
  totalSteps: 0,
  errors: [],
  warnings: [],
  completed: [],
};

// Logging utilities
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
  
  console.log(logMessage);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }

  // Save to log file
  const logFile = path.join(__dirname, '../logs/migration.log');
  fs.appendFileSync(logFile, logMessage + '\n');
}

function logInfo(message, data) { log('info', message, data); }
function logWarn(message, data) { log('warn', message, data); }
function logError(message, data) { log('error', message, data); }
function logSuccess(message, data) { log('success', message, data); }

// Migration steps
const migrationSteps = [
  {
    name: 'Backup Current Data',
    description: 'Create backup of existing collections',
    execute: backupCurrentData,
  },
  {
    name: 'Validate Environment',
    description: 'Check Appwrite connection and permissions',
    execute: validateEnvironment,
  },
  {
    name: 'Create New Collections',
    description: 'Create missing collections with proper schema',
    execute: createNewCollections,
  },
  {
    name: 'Migrate Existing Data',
    description: 'Migrate data from old schema to new schema',
    execute: migrateExistingData,
  },
  {
    name: 'Update Indexes',
    description: 'Create new indexes for performance optimization',
    execute: updateIndexes,
  },
  {
    name: 'Update Permissions',
    description: 'Apply new security rules and permissions',
    execute: updatePermissions,
  },
  {
    name: 'Validate Migration',
    description: 'Verify data integrity and completeness',
    execute: validateMigration,
  },
  {
    name: 'Cleanup',
    description: 'Remove temporary data and finalize migration',
    execute: cleanup,
  },
];

// Main migration function
async function runMigration() {
  try {
    logInfo('🚀 Starting HVPPYPlug+ Database Migration');
    logInfo(`📊 Total migration steps: ${migrationSteps.length}`);

    // Ensure logs directory exists
    const logsDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    migrationState.totalSteps = migrationSteps.length;

    for (let i = 0; i < migrationSteps.length; i++) {
      const step = migrationSteps[i];
      migrationState.currentStep = i + 1;

      logInfo(`\n📋 Step ${migrationState.currentStep}/${migrationState.totalSteps}: ${step.name}`);
      logInfo(`📝 ${step.description}`);

      try {
        await step.execute();
        migrationState.completed.push(step.name);
        logSuccess(`✅ Step ${migrationState.currentStep} completed: ${step.name}`);
      } catch (error) {
        migrationState.errors.push({
          step: step.name,
          error: error.message,
          stack: error.stack,
        });
        logError(`❌ Step ${migrationState.currentStep} failed: ${step.name}`, error);
        
        // Ask user if they want to continue
        const shouldContinue = await askUserToContinue(step.name, error);
        if (!shouldContinue) {
          throw new Error(`Migration stopped at step: ${step.name}`);
        }
      }
    }

    // Generate migration report
    await generateMigrationReport();
    
    logSuccess('🎉 Database migration completed successfully!');
    logInfo('📄 Check migration report for details');

  } catch (error) {
    logError('💥 Migration failed', error);
    await generateMigrationReport();
    process.exit(1);
  }
}

// Migration step implementations

async function backupCurrentData() {
  logInfo('📦 Creating backup of current database...');
  
  const backupDir = path.join(__dirname, '../backups', new Date().toISOString().split('T')[0]);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // Get list of existing collections
  const database = await databases.get(config.databaseId);
  logInfo(`📊 Found database: ${database.name}`);

  // Backup each collection
  const existingCollections = ['users', 'vendors', 'menu-items', 'orders', 'otp-codes'];
  
  for (const collectionId of existingCollections) {
    try {
      logInfo(`📋 Backing up collection: ${collectionId}`);
      
      // Get all documents from collection
      let allDocuments = [];
      let offset = 0;
      const limit = 100;
      
      while (true) {
        const response = await databases.listDocuments(
          config.databaseId,
          collectionId,
          [`limit(${limit})`, `offset(${offset})`]
        );
        
        allDocuments = allDocuments.concat(response.documents);
        
        if (response.documents.length < limit) {
          break;
        }
        
        offset += limit;
      }
      
      // Save to backup file
      const backupFile = path.join(backupDir, `${collectionId}.json`);
      fs.writeFileSync(backupFile, JSON.stringify(allDocuments, null, 2));
      
      logInfo(`💾 Backed up ${allDocuments.length} documents from ${collectionId}`);
      
    } catch (error) {
      if (error.code === 404) {
        logWarn(`⚠️ Collection ${collectionId} not found, skipping backup`);
      } else {
        throw error;
      }
    }
  }
  
  logSuccess(`📦 Backup completed in: ${backupDir}`);
}

async function validateEnvironment() {
  logInfo('🔍 Validating environment...');
  
  // Check Appwrite connection
  try {
    const health = await client.call('GET', '/health');
    logInfo('✅ Appwrite connection successful');
  } catch (error) {
    throw new Error(`Failed to connect to Appwrite: ${error.message}`);
  }
  
  // Check database exists
  try {
    const database = await databases.get(config.databaseId);
    logInfo(`✅ Database found: ${database.name}`);
  } catch (error) {
    throw new Error(`Database not found: ${config.databaseId}`);
  }
  
  // Check API key permissions
  try {
    await databases.list();
    logInfo('✅ API key has required permissions');
  } catch (error) {
    throw new Error(`Insufficient API key permissions: ${error.message}`);
  }
  
  logSuccess('🔍 Environment validation completed');
}

async function createNewCollections() {
  logInfo('🏗️ Creating new collections...');
  
  // Load complete configuration
  const configPath = path.join(__dirname, '../backend/appwrite-complete.json');
  const completeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  // Get existing collections
  const existingCollections = new Set();
  try {
    const collections = await databases.listCollections(config.databaseId);
    collections.collections.forEach(col => existingCollections.add(col.$id));
  } catch (error) {
    logWarn('Could not list existing collections, proceeding with creation');
  }
  
  // Create missing collections
  for (const collection of completeConfig.collections) {
    if (existingCollections.has(collection.id)) {
      logInfo(`⏭️ Collection ${collection.id} already exists, skipping`);
      continue;
    }
    
    try {
      logInfo(`🔨 Creating collection: ${collection.name} (${collection.id})`);
      
      // Create collection
      await databases.createCollection(
        config.databaseId,
        collection.id,
        collection.name,
        collection.permissions || [],
        collection.documentSecurity || false,
        collection.enabled !== false
      );
      
      // Add attributes
      for (const attr of collection.attributes) {
        await createAttribute(collection.id, attr);
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Add indexes
      for (const index of collection.indexes) {
        await createIndex(collection.id, index);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      logSuccess(`✅ Created collection: ${collection.name}`);
      
    } catch (error) {
      logError(`❌ Failed to create collection ${collection.id}`, error);
      throw error;
    }
  }
  
  logSuccess('🏗️ New collections created successfully');
}

async function createAttribute(collectionId, attr) {
  try {
    switch (attr.type) {
      case 'string':
        await databases.createStringAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.size,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
      case 'integer':
        await databases.createIntegerAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
        break;
      case 'double':
        await databases.createFloatAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.min,
          attr.max,
          attr.default,
          attr.array || false
        );
        break;
      case 'boolean':
        await databases.createBooleanAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
      case 'datetime':
        await databases.createDatetimeAttribute(
          config.databaseId,
          collectionId,
          attr.key,
          attr.required || false,
          attr.default,
          attr.array || false
        );
        break;
      default:
        logWarn(`Unknown attribute type: ${attr.type} for ${attr.key}`);
    }
  } catch (error) {
    if (error.code === 409) {
      logWarn(`Attribute ${attr.key} already exists in ${collectionId}`);
    } else {
      throw error;
    }
  }
}

async function createIndex(collectionId, index) {
  try {
    await databases.createIndex(
      config.databaseId,
      collectionId,
      index.key,
      index.type,
      index.attributes,
      index.orders
    );
  } catch (error) {
    if (error.code === 409) {
      logWarn(`Index ${index.key} already exists in ${collectionId}`);
    } else {
      throw error;
    }
  }
}

async function migrateExistingData() {
  logInfo('🔄 Migrating existing data...');
  
  // Enhance existing users with new fields
  await migrateUsers();
  
  // Enhance existing vendors with new fields
  await migrateVendors();
  
  // Enhance existing menu items with new fields
  await migrateMenuItems();
  
  // Enhance existing orders with new fields
  await migrateOrders();
  
  logSuccess('🔄 Data migration completed');
}

async function migrateUsers() {
  logInfo('👥 Migrating users...');
  
  try {
    const users = await databases.listDocuments(config.databaseId, 'users');
    
    for (const user of users.documents) {
      const updates = {};
      
      // Add new fields with defaults
      if (!user.status) updates.status = 'active';
      if (!user.isVerified) updates.isVerified = false;
      if (!user.createdAt) updates.createdAt = user.$createdAt;
      if (!user.updatedAt) updates.updatedAt = user.$updatedAt;
      
      if (Object.keys(updates).length > 0) {
        await databases.updateDocument(
          config.databaseId,
          'users',
          user.$id,
          updates
        );
      }
    }
    
    logInfo(`✅ Migrated ${users.documents.length} users`);
  } catch (error) {
    logError('Failed to migrate users', error);
  }
}

async function migrateVendors() {
  logInfo('🏪 Migrating vendors...');
  
  try {
    const vendors = await databases.listDocuments(config.databaseId, 'vendors');
    
    for (const vendor of vendors.documents) {
      const updates = {};
      
      // Add new fields with defaults
      if (!vendor.slug) updates.slug = vendor.name.toLowerCase().replace(/\s+/g, '-');
      if (!vendor.businessType) updates.businessType = 'restaurant';
      if (!vendor.rating) updates.rating = 0;
      if (!vendor.reviewCount) updates.reviewCount = 0;
      if (!vendor.deliveryFee) updates.deliveryFee = 0;
      if (!vendor.minimumOrder) updates.minimumOrder = 0;
      if (!vendor.isActive) updates.isActive = true;
      if (!vendor.isFeatured) updates.isFeatured = false;
      if (!vendor.createdAt) updates.createdAt = vendor.$createdAt;
      if (!vendor.updatedAt) updates.updatedAt = vendor.$updatedAt;
      
      if (Object.keys(updates).length > 0) {
        await databases.updateDocument(
          config.databaseId,
          'vendors',
          vendor.$id,
          updates
        );
      }
    }
    
    logInfo(`✅ Migrated ${vendors.documents.length} vendors`);
  } catch (error) {
    logError('Failed to migrate vendors', error);
  }
}

async function migrateMenuItems() {
  logInfo('🍔 Migrating menu items...');
  
  try {
    const menuItems = await databases.listDocuments(config.databaseId, 'menu-items');
    
    for (const item of menuItems.documents) {
      const updates = {};
      
      // Add new fields with defaults
      if (!item.slug) updates.slug = item.name.toLowerCase().replace(/\s+/g, '-');
      if (!item.images) updates.images = item.imageUrl ? [item.imageUrl] : [];
      if (!item.tags) updates.tags = [];
      if (!item.allergens) updates.allergens = [];
      if (!item.trackInventory) updates.trackInventory = false;
      if (!item.isActive) updates.isActive = true;
      if (!item.isFeatured) updates.isFeatured = false;
      if (!item.sortOrder) updates.sortOrder = 0;
      if (!item.rating) updates.rating = 0;
      if (!item.reviewCount) updates.reviewCount = 0;
      if (!item.salesCount) updates.salesCount = 0;
      if (!item.createdAt) updates.createdAt = item.$createdAt;
      if (!item.updatedAt) updates.updatedAt = item.$updatedAt;
      
      if (Object.keys(updates).length > 0) {
        await databases.updateDocument(
          config.databaseId,
          'menu-items',
          item.$id,
          updates
        );
      }
    }
    
    logInfo(`✅ Migrated ${menuItems.documents.length} menu items`);
  } catch (error) {
    logError('Failed to migrate menu items', error);
  }
}

async function migrateOrders() {
  logInfo('📦 Migrating orders...');
  
  try {
    const orders = await databases.listDocuments(config.databaseId, 'orders');
    
    for (const order of orders.documents) {
      const updates = {};
      
      // Generate order number if missing
      if (!order.orderNumber) {
        const date = new Date(order.$createdAt);
        const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        updates.orderNumber = `HVP-${dateStr}-${random}`;
      }
      
      // Add new fields with defaults
      if (!order.currency) updates.currency = 'ZAR';
      if (!order.deliveryFee) updates.deliveryFee = 0;
      if (!order.serviceFee) updates.serviceFee = 0;
      if (!order.tax) updates.tax = 0;
      if (!order.tip) updates.tip = 0;
      if (!order.createdAt) updates.createdAt = order.$createdAt;
      if (!order.updatedAt) updates.updatedAt = order.$updatedAt;
      
      if (Object.keys(updates).length > 0) {
        await databases.updateDocument(
          config.databaseId,
          'orders',
          order.$id,
          updates
        );
      }
    }
    
    logInfo(`✅ Migrated ${orders.documents.length} orders`);
  } catch (error) {
    logError('Failed to migrate orders', error);
  }
}

async function updateIndexes() {
  logInfo('📊 Updating indexes...');
  // Indexes are created during collection creation
  logSuccess('📊 Indexes updated');
}

async function updatePermissions() {
  logInfo('🔐 Updating permissions...');
  // Permissions are set during collection creation
  logSuccess('🔐 Permissions updated');
}

async function validateMigration() {
  logInfo('✅ Validating migration...');
  
  // Check all collections exist
  const collections = await databases.listCollections(config.databaseId);
  const collectionIds = collections.collections.map(c => c.$id);
  
  const expectedCollections = [
    'users', 'addresses', 'categories', 'vendors', 'menu-items', 'orders',
    'reviews', 'messages', 'conversations', 'notifications', 'promotions',
    'promotion-usage', 'analytics-events', 'payments', 'runner-profiles',
    'support-tickets', 'vendor-settings', 'otp-codes'
  ];
  
  for (const expectedCollection of expectedCollections) {
    if (!collectionIds.includes(expectedCollection)) {
      throw new Error(`Missing collection: ${expectedCollection}`);
    }
  }
  
  logSuccess(`✅ All ${expectedCollections.length} collections validated`);
}

async function cleanup() {
  logInfo('🧹 Cleaning up...');
  // No cleanup needed for this migration
  logSuccess('🧹 Cleanup completed');
}

// Utility functions

async function askUserToContinue(stepName, error) {
  // In a real implementation, you might want to prompt the user
  // For now, we'll continue on non-critical errors
  const criticalErrors = ['ECONNREFUSED', 'ENOTFOUND', 'UNAUTHORIZED'];
  return !criticalErrors.some(code => error.message.includes(code));
}

async function generateMigrationReport() {
  const report = {
    timestamp: new Date().toISOString(),
    status: migrationState.errors.length === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',
    totalSteps: migrationState.totalSteps,
    completedSteps: migrationState.completed.length,
    errors: migrationState.errors,
    warnings: migrationState.warnings,
    completed: migrationState.completed,
  };
  
  const reportPath = path.join(__dirname, '../logs/migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  logInfo(`📄 Migration report saved to: ${reportPath}`);
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = {
  runMigration,
  migrationSteps,
  config,
};
