{"name": "hvppyplug-monorepo", "version": "1.0.0", "private": true, "description": "HVPPYPlug+ - Hyper-local on-demand snack delivery and services ecosystem for Soweto", "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"build": "pnpm -r run build", "dev": "pnpm -r --parallel run dev", "clean": "pnpm -r run clean && rm -rf node_modules", "type-check": "pnpm -r run type-check", "lint": "pnpm -r run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "pnpm -r run test", "build:common": "pnpm --filter @hvppyplug/common build", "build:payments": "pnpm --filter @hvppyplug/payments build", "migrate:mobile-services": "./scripts/run-migration.sh", "validate:migration": "node scripts/validate-migration.js", "build:apps": "./scripts/build-apps.sh", "export:web": "pnpm --filter @hvppyplug/customer-app export --platform web && pnpm --filter @hvppyplug/vendor-app export --platform web && pnpm --filter @hvppyplug/runner-app export --platform web", "build:android": "cd apps/customer-app && eas build --platform android --profile production && cd ../vendor-app && eas build --platform android --profile production && cd ../runner-app && eas build --platform android --profile production", "build:ios": "cd apps/customer-app && eas build --platform ios --profile production && cd ../vendor-app && eas build --platform ios --profile production && cd ../runner-app && eas build --platform ios --profile production", "prepare:build": "pnpm install --frozen-lockfile && pnpm build:common && pnpm build:payments", "build:customer-dev": "cd apps/customer-app && pnpm run prepare:build && eas build --platform android --profile development", "build:vendor-dev": "cd apps/vendor-app && pnpm run prepare:build && eas build --platform android --profile development", "build:runner-dev": "cd apps/runner-app && pnpm run prepare:build && eas build --platform android --profile development", "clean:cache": "pnpm -r exec rm -rf node_modules/.cache && rm -rf node_modules/.cache && rm -rf .pnpm-cache", "postinstall": "pnpm build:common && pnpm --filter @hvppyplug/compound-components build && pnpm --filter @hvppyplug/mobile-services build"}, "workspaces": ["packages/*", "apps/*", "backend"], "devDependencies": {"@types/node": "^20.11.24", "prettier": "^3.2.5", "typescript": "^5.3.3", "turbo": "^1.12.4"}, "keywords": ["expo", "react-native", "nextjs", "shadcn", "monorepo", "on-demand", "delivery", "soweto"], "author": "HVPPYPlug+ Team", "license": "MIT", "dependencies": {"expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-camera": "~16.1.11", "expo-image-picker": "~16.1.4", "expo-secure-store": "~14.2.3", "expo-local-authentication": "~16.0.5", "expo-task-manager": "~13.1.6", "expo-background-fetch": "~13.1.6", "expo-device": "~7.1.4", "expo-application": "~6.1.5", "expo-constants": "~17.1.7", "expo-network": "~7.1.5", "expo-file-system": "~18.1.11", "expo-haptics": "~14.1.4", "expo-linking": "~7.1.7"}}