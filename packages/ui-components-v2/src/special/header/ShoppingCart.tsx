import React from "react";
import { 
  Pressable, 
  HStack, 
  Badge, 
  BadgeText, 
  Icon,
  Box
} from "@/ui";
import { ShoppingCartIcon } from "@/ui";

export interface ShoppingCartProps {
  /** Number of items in cart */
  itemCount?: number;
  /** Click handler */
  onPress?: () => void;
  /** Show badge with item count */
  showBadge?: boolean;
  /** Size variant */
  size?: "sm" | "md" | "lg";
  /** Custom cart icon */
  icon?: React.ComponentType;
  /** Badge color */
  badgeColor?: string;
}

const ShoppingCart = React.memo<ShoppingCartProps>(({
  itemCount = 0,
  onPress,
  showBadge = true,
  size = "md",
  icon: CustomIcon,
  badgeColor = "bg-red-500"
}) => {
  // Theme support can be added later
  const colorMode = 'light';
  
  const iconSizes = {
    sm: "w-5 h-5",
    md: "w-6 h-6", 
    lg: "w-7 h-7"
  };

  const CartIcon = CustomIcon || ShoppingCartIcon;

  return (
    <Pressable onPress={onPress} className="relative">
      <Box className="p-2 rounded-full hover:bg-background-100 transition-colors">
        <Icon
          as={CartIcon}
          className={`${iconSizes[size]} text-typography-700`}
          color={colorMode === "light" ? "#374151" : "#D1D5DB"}
        />
        
        {showBadge && itemCount > 0 && (
          <Badge 
            className={`absolute -top-1 -right-1 ${badgeColor} min-w-[20px] h-5 rounded-full flex items-center justify-center`}
          >
            <BadgeText className="text-white text-xs font-medium">
              {itemCount > 99 ? "99+" : itemCount.toString()}
            </BadgeText>
          </Badge>
        )}
      </Box>
    </Pressable>
  );
});

ShoppingCart.displayName = "ShoppingCart";
export default ShoppingCart;