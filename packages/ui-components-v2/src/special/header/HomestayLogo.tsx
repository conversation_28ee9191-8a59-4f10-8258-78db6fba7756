import React from "react";
import { Text, HStack, Pressable } from "@/ui";
import { Image } from "react-native";
// import { ThemeContext } from "@/App";

export interface MarketplaceLogoProps {
    /** Custom logo source */
    src?: string;
    /** Alt text for the logo */
    alt?: string;
    /** Logo width */
    width?: string;
    /** Logo height */
    height?: string;
    /** Compact mode for mobile */
    compact?: boolean;
    /** Show text logo if no image provided */
    text?: string;
    /** Click handler */
    onPress?: () => void;
}

const MarketplaceLogo = React.memo<MarketplaceLogoProps>(({
    src,
    alt = "marketplace logo",
    width,
    height,
    compact = false,
    text = "MarketPlace",
    onPress
}) => {
    // Theme support can be added later
    const colorMode = 'light';

    const defaultSrc = colorMode === "light"
        ? require("../../assets/light-logo.svg")
        : require("../../assets/dark-logo.svg");

    const logoWidth = width || (compact ? "w-[100px]" : "w-[142px]");
    const logoHeight = height || (compact ? "h-[32px]" : "h-[42px]");

    if (src || defaultSrc) {
        return (
            <Pressable onPress={onPress}>
                <Image
                    source={typeof src === 'string' ? { uri: src } : src || defaultSrc}
                    alt={alt}
                    className={`${logoHeight} ${logoWidth}`}
                />
            </Pressable>
        );
    }

    // Fallback text logo
    return (
        <Pressable onPress={onPress}>
            <HStack className="items-center">
                <Text
                    className={`font-bold ${compact ? 'text-lg' : 'text-xl'} text-primary-600`}
                >
                    {text}
                </Text>
            </HStack>
        </Pressable>
    );
});

MarketplaceLogo.displayName = "MarketplaceLogo";
export default MarketplaceLogo;
