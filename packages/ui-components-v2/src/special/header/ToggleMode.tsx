import React from "react";
import { Icon, MoonIcon, SunIcon, Pressable } from "@/ui";
// import { ThemeContext } from "../../App";

const ToggleMode = () => {
  // Theme support can be added later
  const colorMode = 'light';
  const toggleColorMode = () => {};
  return (
    <Pressable onPress={toggleColorMode}>
      <Icon
        as={colorMode ? SunIcon : MoonIcon}
        size="xl"
        className="stroke-background-700 fill-background-700"
      />
    </Pressable>
  );
};

export default ToggleMode;
