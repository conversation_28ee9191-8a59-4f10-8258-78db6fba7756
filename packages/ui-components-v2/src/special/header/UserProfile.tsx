import React, { useState } from "react";
import {
  Avatar,
  AvatarFallbackText,
  AvatarImage,
  AvatarBadge,
  Pressable,
  Menu,
  MenuItem,
  MenuItemLabel,
  Button,
  ButtonText,
  HStack,
  Text,
} from "@/ui";
import LogoutAlertDialog from "../LogoutAlertDialog";

export interface UserProfileProps {
  /** Whether user is logged in */
  isLoggedIn?: boolean;
  /** User avatar URL */
  avatar?: string;
  /** User name */
  name?: string;
  /** Profile click handler */
  onProfileClick?: () => void;
  /** Login click handler */
  onLoginClick?: () => void;
  /** Compact mode for mobile */
  compact?: boolean;
  /** Custom menu items */
  menuItems?: Array<{
    title: string;
    action?: string;
    onClick?: () => void;
    divider?: boolean;
  }>;
  /** Show online status badge */
  showOnlineStatus?: boolean;
}

const defaultMenuItems: Array<{
  title: string;
  action?: string;
  onClick?: () => void;
  divider?: boolean;
}> = [
  { title: "My Account", action: "account" },
  { title: "My Orders", action: "orders" },
  { title: "Wishlist", action: "wishlist" },
  { title: "Address Book", action: "addresses" },
  { title: "Payment Methods", action: "payments" },
  { title: "Notifications", action: "notifications" },
  { title: "Seller Dashboard", action: "seller", divider: true },
  { title: "Become a Seller", action: "become-seller" },
  { title: "Help & Support", action: "help", divider: true },
  { title: "Settings", action: "settings" },
  { title: "Log out", action: "logout" },
];

const UserProfile = React.memo<UserProfileProps>(({
  isLoggedIn = false,
  avatar,
  name = "User",
  onProfileClick,
  onLoginClick,
  compact = false,
  menuItems = defaultMenuItems,
  showOnlineStatus = true
}) => {
  const [openLogoutAlertDialog, setOpenLogoutAlertDialog] = useState(false);

  const handleMenuSelection = (action: string, customHandler?: () => void) => {
    if (customHandler) {
      customHandler();
      return;
    }

    switch (action) {
      case "logout":
        setOpenLogoutAlertDialog(true);
        break;
      case "account":
        onProfileClick?.();
        break;
      default:
        // Handle other actions or pass to parent
        break;
    }
  };

  if (!isLoggedIn) {
    return (
      <Button 
        variant="outline" 
        size={compact ? "sm" : "md"}
        onPress={onLoginClick}
      >
        <ButtonText>Sign In</ButtonText>
      </Button>
    );
  }

  return (
    <>
      <Menu
        offset={10}
        placement="bottom right"
        selectionMode="single"
        onSelectionChange={(e: any) => {
          const selectedItem = menuItems.find(item => item.title === e.currentKey);
          if (selectedItem) {
            handleMenuSelection(selectedItem.action || "", selectedItem.onClick);
          }
        }}
        trigger={({ ...triggerProps }) => {
          return (
            <Pressable {...triggerProps}>
              {compact ? (
                <Avatar size="sm">
                  <AvatarFallbackText>{name}</AvatarFallbackText>
                  {avatar && (
                    <AvatarImage source={{ uri: avatar }} />
                  )}
                  {showOnlineStatus && (
                    <AvatarBadge className="bg-green-500 border-background-0" />
                  )}
                </Avatar>
              ) : (
                <HStack space="sm" className="items-center">
                  <Avatar size="sm">
                    <AvatarFallbackText>{name}</AvatarFallbackText>
                    {avatar && (
                      <AvatarImage source={{ uri: avatar }} />
                    )}
                    {showOnlineStatus && (
                      <AvatarBadge className="bg-green-500 border-background-0" />
                    )}
                  </Avatar>
                  <Text className="text-sm font-medium text-typography-700 hidden lg:block">
                    {name}
                  </Text>
                </HStack>
              )}
            </Pressable>
          );
        }}
      >
        {menuItems.map((item, index) => (
          <React.Fragment key={item.title}>
            {item.divider && index > 0 && (
              <MenuItem key={`divider-${index}`} className="border-t border-outline-100">
                <MenuItemLabel></MenuItemLabel>
              </MenuItem>
            )}
            <MenuItem textValue={item.title}>
              <MenuItemLabel>{item.title}</MenuItemLabel>
            </MenuItem>
          </React.Fragment>
        ))}
      </Menu>
      
      <LogoutAlertDialog
        openLogoutAlertDialog={openLogoutAlertDialog}
        setOpenLogoutAlertDialog={setOpenLogoutAlertDialog}
      />
    </>
  );
});

UserProfile.displayName = "UserProfile";
export default UserProfile;
