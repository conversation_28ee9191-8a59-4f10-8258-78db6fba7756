import React from "react";
import {
  Checkbox,
  CheckboxGroup,
  CheckboxIcon,
  CheckboxIndicator,
  CheckboxLabel,
  Heading,
  VStack,
  CheckIcon,
  Text,
} from "@/ui";
// import { ThemeContext } from "@/App";

export interface CategoryItem {
  label: string;
  value: string;
  count?: number;
  disabled?: boolean;
}

export interface ProductCategorySectionProps {
  /** Section title */
  title?: string;
  /** Array of category items */
  categories?: CategoryItem[];
  /** Selected values */
  selectedValues?: string[];
  /** Change handler */
  onChange?: (values: string[]) => void;
  /** Show product count for each category */
  showCount?: boolean;
  /** Maximum number of categories to show initially */
  maxVisible?: number;
  /** Allow multiple selections */
  multiSelect?: boolean;
}

const defaultCategories: CategoryItem[] = [
  { label: "Electronics", value: "electronics", count: 1250 },
  { label: "Fashion & Clothing", value: "fashion", count: 890 },
  { label: "Home & Garden", value: "home", count: 650 },
  { label: "Sports & Outdoors", value: "sports", count: 420 },
  { label: "Books & Media", value: "books", count: 380 },
  { label: "Health & Beauty", value: "health", count: 290 },
  { label: "Toys & Games", value: "toys", count: 180 },
  { label: "Automotive", value: "automotive", count: 150 },
];

const ProductCategorySection = React.memo<ProductCategorySectionProps>(({
  title = "Categories",
  categories = defaultCategories,
  selectedValues = [],
  onChange,
  showCount = true,
  maxVisible = 6,
  multiSelect = true
}) => {
  // Theme support can be added later
  const colorMode = 'light';
  const [internalValues, setInternalValues] = React.useState<string[]>(selectedValues);
  const [showAll, setShowAll] = React.useState(false);

  const currentValues = onChange ? selectedValues : internalValues;
  const visibleCategories = showAll ? categories : categories.slice(0, maxVisible);
  const hasMore = categories.length > maxVisible;

  const handleChange = (values: string[]) => {
    if (!multiSelect && values.length > 1) {
      // Keep only the last selected value for single select
      values = [values[values.length - 1]];
    }
    
    if (onChange) {
      onChange(values);
    } else {
      setInternalValues(values);
    }
  };

  return (
    <VStack space="sm" className="px-2">
      <Heading size="sm">{title}</Heading>
      
      <CheckboxGroup
        value={currentValues}
        onChange={handleChange}
        accessibilityLabel="product-categories"
      >
        {visibleCategories.map((category) => {
          return (
            <Checkbox
              value={category.value}
              size="sm"
              key={category.value}
              accessibilityLabel={category.value}
              className="my-2 justify-start"
              isDisabled={category.disabled}
            >
              <CheckboxIndicator>
                <CheckboxIcon
                  as={CheckIcon}
                  color={colorMode === "light" ? "#FEFEFF" : "#171717"}
                />
              </CheckboxIndicator>
              <CheckboxLabel className="flex-1">
                <VStack className="flex-1">
                  <Text className="text-sm text-typography-900">
                    {category.label}
                  </Text>
                  {showCount && category.count !== undefined && (
                    <Text className="text-xs text-typography-500">
                      {category.count.toLocaleString()} products
                    </Text>
                  )}
                </VStack>
              </CheckboxLabel>
            </Checkbox>
          );
        })}
      </CheckboxGroup>

      {hasMore && (
        <Text 
          className="text-sm text-primary-600 cursor-pointer hover:text-primary-700 mt-2"
          onPress={() => setShowAll(!showAll)}
        >
          {showAll ? "Show Less" : `Show ${categories.length - maxVisible} More`}
        </Text>
      )}
    </VStack>
  );
});

ProductCategorySection.displayName = "ProductCategorySection";
export default ProductCategorySection;
