import React from "react";
import { HStack, Heading, Switch, Text, VStack } from "@/ui";

export interface ProductOption {
  id: string;
  label: string;
  description: string;
  defaultValue?: boolean;
  disabled?: boolean;
}

export interface ProductOptionsProps {
  /** Section title */
  title?: string;
  /** Array of product options */
  options?: ProductOption[];
  /** Values for each option */
  values?: Record<string, boolean>;
  /** Change handler */
  onValueChange?: (optionId: string, value: boolean) => void;
  /** Custom switch colors */
  switchColors?: {
    trackColorFalse?: string;
    trackColorTrue?: string;
    thumbColor?: string;
    activeThumbColor?: string;
  };
}

const defaultOptions: ProductOption[] = [
  {
    id: "freeShipping",
    label: "Free Shipping",
    description: "Products with free shipping available"
  },
  {
    id: "fastDelivery",
    label: "Fast Delivery",
    description: "Same day or next day delivery options"
  },
  {
    id: "inStock",
    label: "In Stock Only",
    description: "Show only products currently available"
  },
  {
    id: "onSale",
    label: "On Sale",
    description: "Products with discounts and special offers"
  }
];

const ProductOptions = React.memo<ProductOptionsProps>(({
  title = "Product Options",
  options = defaultOptions,
  values = {},
  onValueChange,
  switchColors = {
    trackColorFalse: "#d1d5db",
    trackColorTrue: "#3b82f6",
    thumbColor: "#f9fafb",
    activeThumbColor: "#f9fafb"
  }
}) => {
  const [internalValues, setInternalValues] = React.useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    options.forEach(option => {
      initial[option.id] = values[option.id] ?? option.defaultValue ?? false;
    });
    return initial;
  });

  const handleValueChange = (optionId: string, value: boolean) => {
    setInternalValues(prev => ({ ...prev, [optionId]: value }));
    onValueChange?.(optionId, value);
  };

  const currentValues = onValueChange ? values : internalValues;

  return (
    <VStack space="md" className="px-2">
      <Heading size="sm">{title}</Heading>

      {options.map((option) => (
        <VStack key={option.id} className="w-full">
          <HStack space="lg" className="w-full">
            <VStack className="flex-1">
              <Text size="sm" className="text-typography-900">
                {option.label}
              </Text>
              <Text size="xs" className="text-typography-500">
                {option.description}
              </Text>
            </VStack>
            <Switch
              size="sm"
              value={currentValues[option.id] || false}
              onValueChange={(val: boolean) => handleValueChange(option.id, val)}
              isDisabled={option.disabled}
              trackColor={{ 
                false: switchColors.trackColorFalse, 
                true: switchColors.trackColorTrue 
              }}
              thumbColor={switchColors.thumbColor}
              activeThumbColor={switchColors.activeThumbColor}
              ios_backgroundColor={switchColors.trackColorFalse}
            />
          </HStack>
        </VStack>
      ))}
    </VStack>
  );
});

ProductOptions.displayName = "ProductOptions";
export default ProductOptions;
