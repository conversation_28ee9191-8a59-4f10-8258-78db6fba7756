import React from "react";
// import { ThemeContext } from "../../App";
import {
  Checkbox,
  CheckboxGroup,
  CheckboxIcon,
  CheckboxIndicator,
  CheckboxLabel,
  CheckIcon,
  Heading,
  Icon,
  VStack,
  StarIcon,
  HStack,
  Text,
} from "@/ui";

export interface RatingOption {
  label: string;
  value: string;
  stars: number;
  count?: number;
  disabled?: boolean;
}

export interface ProductRatingSectionProps {
  /** Section title */
  title?: string;
  /** Array of rating options */
  ratings?: RatingOption[];
  /** Selected values */
  selectedValues?: string[];
  /** Change handler */
  onChange?: (values: string[]) => void;
  /** Show review count for each rating */
  showCount?: boolean;
  /** Allow multiple selections */
  multiSelect?: boolean;
  /** Star icon color */
  starColor?: string;
}

const defaultRatings: RatingOption[] = [
  { label: "5 stars", value: "5", stars: 5, count: 1250 },
  { label: "4+ stars", value: "4+", stars: 4, count: 890 },
  { label: "3+ stars", value: "3+", stars: 3, count: 420 },
  { label: "2+ stars", value: "2+", stars: 2, count: 180 },
  { label: "1+ stars", value: "1+", stars: 1, count: 45 },
];

const ProductRatingSection = React.memo<ProductRatingSectionProps>(({
  title = "Customer Ratings",
  ratings = defaultRatings,
  selectedValues = [],
  onChange,
  showCount = true,
  multiSelect = true,
  starColor = "#FCD34D"
}) => {
  const [internalValues, setInternalValues] = React.useState<string[]>(selectedValues);
  // Theme support can be added later
  const colorMode = 'light';

  const currentValues = onChange ? selectedValues : internalValues;

  const handleChange = (values: string[]) => {
    if (!multiSelect && values.length > 1) {
      // Keep only the last selected value for single select
      values = [values[values.length - 1]];
    }
    
    if (onChange) {
      onChange(values);
    } else {
      setInternalValues(values);
    }
  };

  const renderStars = (count: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Icon
        key={index}
        as={StarIcon}
        size="2xs"
        className={index < count ? "fill-current" : "fill-gray-300"}
        color={index < count ? starColor : "#D1D5DB"}
      />
    ));
  };

  return (
    <VStack space="md" className="w-full px-2">
      <Heading size="sm">{title}</Heading>
      <CheckboxGroup
        value={currentValues}
        onChange={handleChange}
        accessibilityLabel="product-ratings"
      >
        {ratings.map((rating, index) => {
          return (
            <Checkbox
              value={rating.value}
              size="sm"
              key={rating.value}
              accessibilityLabel={rating.value}
              className="my-2"
              isDisabled={rating.disabled}
            >
              <CheckboxIndicator>
                <CheckboxIcon
                  as={CheckIcon}
                  color={colorMode === "light" ? "#FEFEFF" : "#171717"}
                />
              </CheckboxIndicator>
              <CheckboxLabel className="flex-1">
                <HStack space="sm" className="items-center flex-1">
                  <HStack space="xs" className="items-center">
                    {renderStars(rating.stars)}
                  </HStack>
                  <Text className="text-sm text-typography-900">
                    {rating.label}
                  </Text>
                  {showCount && rating.count !== undefined && (
                    <Text className="text-xs text-typography-500 ml-auto">
                      ({rating.count.toLocaleString()})
                    </Text>
                  )}
                </HStack>
              </CheckboxLabel>
            </Checkbox>
          );
        })}
      </CheckboxGroup>
    </VStack>
  );
});

ProductRatingSection.displayName = "ProductRatingSection";
export default ProductRatingSection;
