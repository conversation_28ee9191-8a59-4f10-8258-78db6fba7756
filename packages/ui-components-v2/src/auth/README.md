# HVPPYPlug+ Authentication System

A comprehensive authentication system for React Native applications with direct Appwrite SDK integration, designed specifically for the South African market.

## Features

### 🔐 Authentication Methods
- **Phone-based authentication** with South African phone number validation
- **OTP verification** via SMS
- **Password-based login** with secure password requirements
- **Passwordless login** using OTP codes
- **Password reset** with OTP verification

### 🇿🇦 South African Localization
- **Phone number validation** for SA providers (082, 083, 084, 072, 073, 074, 076, 078, 079)
- **Province and city selection** with real SA locations
- **Localized data patterns** and formatting
- **ZAR currency formatting** and validation

### 👥 Multi-Role Support
- **Customer registration** - Basic user profile
- **Vendor registration** - Business details and address
- **Runner registration** - Vehicle details and emergency contact
- **Admin support** - Full platform management

### 🎨 Customizable UI
- **Gradient backgrounds** with customizable colors
- **Role-specific branding** and messaging
- **Responsive design** for various screen sizes
- **Accessibility support** with screen reader compatibility
- **Loading states** and error handling

## Installation

```bash
# Install dependencies
npm install @hvppyplug/ui-components-v2
npm install appwrite zustand @react-native-async-storage/async-storage
npm install @react-navigation/native @react-navigation/native-stack
npm install expo-linear-gradient @expo/vector-icons
npm install react-native-safe-area-context
```

## Quick Start

### 1. Setup Authentication Store

```typescript
import { useAuthStore } from '@hvppyplug/ui-components-v2/auth'

// The store is automatically configured with Appwrite
// Project ID: 6880d655003a8926a438
// Database: hvppyplug-main
```

### 2. Create Authentication Navigator

```typescript
import React from 'react'
import { AuthNavigator } from './screens/auth/AuthNavigator'

export const App = () => {
  const { isAuthenticated, user } = useAuthStore()

  if (!isAuthenticated) {
    return (
      <AuthNavigator
        onAuthComplete={(user) => {
          console.log('User authenticated:', user)
        }}
      />
    )
  }

  return <MainApp user={user} />
}
```

### 3. Use Individual Screens

```typescript
import {
  WelcomeScreen,
  PhoneInputScreen,
  OTPVerificationScreen,
  UserRegistrationScreen,
  LoginScreen,
  PasswordResetScreen
} from '@hvppyplug/ui-components-v2/auth'

// Example: Phone Input Screen
<PhoneInputScreen
  onPhoneSubmit={(phone) => {
    // Navigate to OTP verification
  }}
  role="customer"
  primaryColor="#FF6B6B"
  secondaryColor="#FF8E8E"
/>
```

## Screen Components

### WelcomeScreen
Onboarding screen with role selection and app introduction.

**Props:**
- `onRoleSelect: (role: UserRole) => void` - Called when user selects a role
- `onSkip?: () => void` - Optional skip functionality
- `appName?: string` - Custom app name (default: "HVPPYPlug+")
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

### PhoneInputScreen
Phone number input with South African validation.

**Props:**
- `onPhoneSubmit: (phone: string) => void` - Called when valid phone is submitted
- `onBack?: () => void` - Back navigation handler
- `role?: UserRole` - User role for customized messaging
- `title?: string` - Screen title
- `subtitle?: string` - Screen subtitle
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

### OTPVerificationScreen
SMS code verification with resend functionality.

**Props:**
- `phone: string` - Phone number to verify
- `onVerificationSuccess: (phone: string, code: string) => void` - Success callback
- `onBack?: () => void` - Back navigation handler
- `type?: 'verification' | 'login' | 'password_reset'` - OTP type
- `title?: string` - Screen title
- `subtitle?: string` - Screen subtitle
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

### UserRegistrationScreen
Role-specific user registration with form validation.

**Props:**
- `phone: string` - Verified phone number
- `role: UserRole` - User role (customer, vendor, runner)
- `onRegistrationSuccess: (user: any) => void` - Success callback
- `onBack?: () => void` - Back navigation handler
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

### LoginScreen
Login with phone/password or OTP options.

**Props:**
- `onLoginSuccess: (user: any) => void` - Success callback
- `onForgotPassword?: (phone: string) => void` - Forgot password handler
- `onSignUp?: () => void` - Sign up navigation handler
- `onBack?: () => void` - Back navigation handler
- `initialPhone?: string` - Pre-filled phone number
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

### PasswordResetScreen
Multi-step password reset with OTP verification.

**Props:**
- `onResetSuccess: () => void` - Success callback
- `onBack?: () => void` - Back navigation handler
- `initialPhone?: string` - Pre-filled phone number
- `primaryColor?: string` - Primary brand color
- `secondaryColor?: string` - Secondary brand color

## Authentication Store

### State
```typescript
interface AuthState {
  user: AuthUser | null
  session: AuthSession | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  hasCompletedOnboarding: boolean
}
```

### Actions
```typescript
// Phone-based authentication
sendOTP(phone: string, type?: 'verification' | 'login' | 'password_reset'): Promise<void>
verifyOTP(phone: string, code: string): Promise<AuthUser>

// Registration
registerUser(data: UserRegistrationData): Promise<AuthUser>
registerVendor(data: VendorRegistrationData): Promise<AuthUser>
registerRunner(data: RunnerRegistrationData): Promise<AuthUser>

// Login/Logout
login(data: LoginData): Promise<AuthUser>
logout(): Promise<void>
logoutAll(): Promise<void>

// Password management
resetPassword(data: PasswordResetData): Promise<void>
changePassword(currentPassword: string, newPassword: string): Promise<void>

// Profile management
updateProfile(updates: Partial<AuthUser>): Promise<AuthUser>
getCurrentUser(): Promise<AuthUser | null>

// Utility actions
clearError(): void
completeOnboarding(): void
```

## Utilities

### Phone Number Validation
```typescript
import { validateSAPhoneNumber, formatPhoneNumber } from '@hvppyplug/ui-components-v2/auth'

const validation = validateSAPhoneNumber('**********')
// Returns: { countryCode: '+27', provider: '082', number: '1234567', formatted: '+27 ************', isValid: true }

const formatted = formatPhoneNumber('**********')
// Returns: '+27 ************'
```

### Form Validation
```typescript
import { validateEmail, validatePassword, validateUserRegistration } from '@hvppyplug/ui-components-v2/auth'

const emailValid = validateEmail('<EMAIL>')
const passwordValidation = validatePassword('MyPassword123!')
const registrationErrors = validateUserRegistration(formData, 'customer')
```

## Customization

### App-Specific Branding
Each app can customize colors and messaging:

```typescript
// Customer App
const customerColors = {
  primary: '#FF6B6B',
  secondary: '#FF8E8E'
}

// Vendor App
const vendorColors = {
  primary: '#4ECDC4',
  secondary: '#6ED5CE'
}

// Runner App
const runnerColors = {
  primary: '#45B7D1',
  secondary: '#6BC5D8'
}
```

### Role-Specific Content
The system automatically adapts content based on user role:

- **Customer**: "Order from your favorite restaurants"
- **Vendor**: "Register your business and start selling"
- **Runner**: "Join our delivery team and earn money"

## Database Integration

The system integrates directly with Appwrite collections:

- **users** - User profiles and authentication data
- **vendors** - Business profiles and settings
- **runner-profiles** - Delivery personnel information
- **addresses** - User and business addresses
- **otp-codes** - SMS verification codes

## Security Features

- **Phone number verification** via SMS OTP
- **Password strength validation** with requirements
- **Session management** with automatic refresh
- **Role-based access control** 
- **Input sanitization** and validation
- **Error handling** with user-friendly messages

## South African Compliance

- **POPIA compliance** with privacy policy integration
- **Local phone number formats** and validation
- **ZAR currency** formatting and display
- **Provincial address** validation
- **Local business types** and categories

## Support

For issues or questions:
- 📧 Email: <EMAIL>
- 📞 Phone: +27 11 123 4567
- 🌐 Website: https://hvppyplug.co.za

## License

Copyright © 2025 HVPPYPlug+. All rights reserved.
