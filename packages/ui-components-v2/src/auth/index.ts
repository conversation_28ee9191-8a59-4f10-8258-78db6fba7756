/**
 * HVPPYPlug+ Authentication System
 * Export all authentication components, types, and utilities
 */

// Types
export * from './types'

// Utilities
export * from './utils'

// Service
export { authService, AuthService } from './service'

// Store
export { useAuthStore, useAuth, useAuthActions } from './store'

// Screens
export { default as WelcomeScreen } from './screens/WelcomeScreen'
export { default as PhoneInputScreen } from './screens/PhoneInputScreen'
export { default as OTPVerificationScreen } from './screens/OTPVerificationScreen'
export { default as UserRegistrationScreen } from './screens/UserRegistrationScreen'
export { default as LoginScreen } from './screens/LoginScreen'
export { default as PasswordResetScreen } from './screens/PasswordResetScreen'

// Re-export individual components for convenience
export { WelcomeScreen } from './screens/WelcomeScreen'
export { PhoneInputScreen } from './screens/PhoneInputScreen'
export { OTPVerificationScreen } from './screens/OTPVerificationScreen'
export { UserRegistrationScreen } from './screens/UserRegistrationScreen'
export { LoginScreen } from './screens/LoginScreen'
export { PasswordResetScreen } from './screens/PasswordResetScreen'
