/**
 * Authentication Store using Zustand
 * HVPPYPlug+ Authentication State Management
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import {
  AuthUser,
  AuthSession,
  AuthState,
  AuthActions,
  UserRegistrationData,
  VendorRegistrationData,
  RunnerRegistrationData,
  LoginData,
  PasswordResetData,
  UserRole
} from './types'
import { authService } from './service'

interface AuthStore extends AuthState, AuthActions {}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial State
      user: null,
      session: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      hasCompletedOnboarding: false,

      // Phone-based authentication
      sendOTP: async (phone: string, type = 'verification') => {
        set({ isLoading: true, error: null })
        try {
          await authService.sendOTP(phone, type)
          set({ isLoading: false })
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Failed to send OTP' 
          })
          throw error
        }
      },

      verifyOTP: async (phone: string, code: string) => {
        set({ isLoading: true, error: null })
        try {
          await authService.verifyOTP(phone, code)
          set({ isLoading: false })
          
          // Check if user exists
          const userExists = await authService.userExistsByPhone(phone)
          if (userExists) {
            // User exists, proceed with login
            const { user, session } = await authService.loginWithOTP(phone, code)
            set({
              user,
              session,
              isAuthenticated: true,
              isLoading: false
            })
            return user
          } else {
            // New user, needs registration
            set({ isLoading: false })
            throw new Error('USER_NOT_FOUND')
          }
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message === 'USER_NOT_FOUND' ? null : error.message || 'OTP verification failed' 
          })
          throw error
        }
      },

      // Registration
      registerUser: async (data: UserRegistrationData) => {
        set({ isLoading: true, error: null })
        try {
          const user = await authService.registerUser(data)
          
          // Auto-login after registration
          const { session } = await authService.loginWithOTP(data.phone, '')
          
          set({
            user,
            session,
            isAuthenticated: true,
            isLoading: false
          })
          
          return user
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Registration failed' 
          })
          throw error
        }
      },

      registerVendor: async (data: VendorRegistrationData) => {
        set({ isLoading: true, error: null })
        try {
          const user = await authService.registerVendor(data)
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false
          })
          
          return user
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Vendor registration failed' 
          })
          throw error
        }
      },

      registerRunner: async (data: RunnerRegistrationData) => {
        set({ isLoading: true, error: null })
        try {
          const user = await authService.registerRunner(data)
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false
          })
          
          return user
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Runner registration failed' 
          })
          throw error
        }
      },

      // Login/Logout
      login: async (data: LoginData) => {
        set({ isLoading: true, error: null })
        try {
          const { user, session } = await authService.login(data)
          
          set({
            user,
            session,
            isAuthenticated: true,
            isLoading: false
          })
          
          return user
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Login failed' 
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          await authService.logout()
          
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Logout failed' 
          })
        }
      },

      logoutAll: async () => {
        set({ isLoading: true })
        try {
          await authService.logoutAll()
          
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Logout failed' 
          })
        }
      },

      // Password management
      resetPassword: async (data: PasswordResetData) => {
        set({ isLoading: true, error: null })
        try {
          await authService.resetPassword(data)
          set({ isLoading: false })
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Password reset failed' 
          })
          throw error
        }
      },

      changePassword: async (currentPassword: string, newPassword: string) => {
        set({ isLoading: true, error: null })
        try {
          // This would need to be implemented in the service
          // await authService.changePassword(currentPassword, newPassword)
          set({ isLoading: false })
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Password change failed' 
          })
          throw error
        }
      },

      // Profile management
      updateProfile: async (updates: Partial<AuthUser>) => {
        set({ isLoading: true, error: null })
        try {
          const { user } = get()
          if (!user) throw new Error('No user logged in')
          
          const updatedUser = await authService.updateProfile(user.$id, updates)
          
          set({
            user: updatedUser,
            isLoading: false
          })
          
          return updatedUser
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Profile update failed' 
          })
          throw error
        }
      },

      uploadAvatar: async (file: File | Blob) => {
        set({ isLoading: true, error: null })
        try {
          // This would need to be implemented with Appwrite Storage
          // const avatarUrl = await authService.uploadAvatar(file)
          const avatarUrl = 'placeholder-url'
          
          set({ isLoading: false })
          return avatarUrl
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Avatar upload failed' 
          })
          throw error
        }
      },

      // Session management
      refreshSession: async () => {
        set({ isLoading: true, error: null })
        try {
          const session = await authService.refreshSession()
          
          set({
            session,
            isLoading: false
          })
          
          return session
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || 'Session refresh failed' 
          })
          throw error
        }
      },

      getCurrentUser: async () => {
        try {
          const user = await authService.getCurrentUser()
          
          if (user) {
            set({ user, isAuthenticated: true })
          }
          
          return user
        } catch (error: any) {
          console.error('Get current user error:', error)
          return null
        }
      },

      // Utility actions
      clearError: () => {
        set({ error: null })
      },

      completeOnboarding: () => {
        set({ hasCompletedOnboarding: true })
      }
    }),
    {
      name: 'hvppyplug-auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
        hasCompletedOnboarding: state.hasCompletedOnboarding
      })
    }
  )
)

// Helper hooks
export const useAuth = () => {
  const store = useAuthStore()
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    hasCompletedOnboarding: store.hasCompletedOnboarding
  }
}

export const useAuthActions = () => {
  const store = useAuthStore()
  return {
    sendOTP: store.sendOTP,
    verifyOTP: store.verifyOTP,
    registerUser: store.registerUser,
    registerVendor: store.registerVendor,
    registerRunner: store.registerRunner,
    login: store.login,
    logout: store.logout,
    logoutAll: store.logoutAll,
    resetPassword: store.resetPassword,
    changePassword: store.changePassword,
    updateProfile: store.updateProfile,
    uploadAvatar: store.uploadAvatar,
    refreshSession: store.refreshSession,
    getCurrentUser: store.getCurrentUser,
    clearError: store.clearError,
    completeOnboarding: store.completeOnboarding
  }
}
