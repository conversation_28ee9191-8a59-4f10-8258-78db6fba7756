/**
 * Shared Authentication Types for HVPPYPlug+ Apps
 */

export type UserRole = 'customer' | 'vendor' | 'runner' | 'admin'

export interface AuthUser {
  $id: string
  name: string
  email: string
  phone: string
  role: UserRole
  status: 'active' | 'inactive' | 'suspended'
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface AuthSession {
  $id: string
  userId: string
  expire: string
  provider: string
  providerUid: string
  providerAccessToken: string
  providerAccessTokenExpiry: string
  providerRefreshToken: string
  ip: string
  osCode: string
  osName: string
  osVersion: string
  clientType: string
  clientCode: string
  clientName: string
  clientVersion: string
  clientEngine: string
  clientEngineVersion: string
  deviceName: string
  deviceBrand: string
  deviceModel: string
  countryCode: string
  countryName: string
  current: boolean
}

export interface OTPCode {
  $id: string
  phone: string
  code: string
  type: 'verification' | 'login' | 'password_reset'
  isUsed: boolean
  expiresAt: string
  createdAt: string
}

export interface UserProfile {
  $id: string
  name: string
  email: string
  phone: string
  role: UserRole
  status: string
  isVerified: boolean
  avatar?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  language: string
  timezone: string
  createdAt: string
  updatedAt: string
}

export interface VendorProfile extends UserProfile {
  businessName: string
  businessType: string
  businessRegistration?: string
  taxNumber?: string
  bankDetails?: {
    accountName: string
    accountNumber: string
    bankName: string
    branchCode: string
  }
  operatingHours: {
    [key: string]: {
      open: string
      close: string
      isOpen: boolean
    }
  }
  deliveryRadius: number
  minimumOrder: number
  deliveryFee: number
  estimatedDeliveryTime: number
}

export interface RunnerProfile extends UserProfile {
  vehicleType: 'bicycle' | 'motorcycle' | 'car' | 'scooter'
  vehicleDetails: {
    make?: string
    model?: string
    year?: number
    color?: string
    licensePlate?: string
  }
  licenseNumber?: string
  licenseExpiry?: string
  isAvailable: boolean
  isOnline: boolean
  rating: number
  totalDeliveries: number
  verificationStatus: 'pending' | 'approved' | 'rejected'
  workingAreas: string[]
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
}

export interface Address {
  $id: string
  userId: string
  type: 'home' | 'work' | 'other'
  street: string
  city: string
  province: string
  postalCode: string
  country: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  isDefault: boolean
  instructions?: string
}

// Authentication form data types
export interface PhoneAuthData {
  phone: string
  countryCode: string
}

export interface OTPVerificationData {
  phone: string
  code: string
}

export interface UserRegistrationData {
  name: string
  email: string
  phone: string
  role: UserRole
  password?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
}

export interface VendorRegistrationData extends UserRegistrationData {
  businessName: string
  businessType: string
  businessRegistration?: string
  address: Omit<Address, '$id' | 'userId'>
}

export interface RunnerRegistrationData extends UserRegistrationData {
  vehicleType: 'bicycle' | 'motorcycle' | 'car' | 'scooter'
  vehicleDetails: {
    make?: string
    model?: string
    year?: number
    color?: string
    licensePlate?: string
  }
  licenseNumber?: string
  workingAreas: string[]
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
}

export interface LoginData {
  phone: string
  password?: string
  rememberMe?: boolean
}

export interface PasswordResetData {
  phone: string
  code: string
  newPassword: string
  confirmPassword: string
}

// Authentication state types
export interface AuthState {
  user: AuthUser | null
  session: AuthSession | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  hasCompletedOnboarding: boolean
}

// Authentication action types
export interface AuthActions {
  // Phone-based authentication
  sendOTP: (phone: string, type?: 'verification' | 'login' | 'password_reset') => Promise<void>
  verifyOTP: (phone: string, code: string) => Promise<AuthUser>
  
  // Registration
  registerUser: (data: UserRegistrationData) => Promise<AuthUser>
  registerVendor: (data: VendorRegistrationData) => Promise<AuthUser>
  registerRunner: (data: RunnerRegistrationData) => Promise<AuthUser>
  
  // Login/Logout
  login: (data: LoginData) => Promise<AuthUser>
  logout: () => Promise<void>
  logoutAll: () => Promise<void>
  
  // Password management
  resetPassword: (data: PasswordResetData) => Promise<void>
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>
  
  // Profile management
  updateProfile: (updates: Partial<UserProfile>) => Promise<AuthUser>
  uploadAvatar: (file: File | Blob) => Promise<string>
  
  // Session management
  refreshSession: () => Promise<AuthSession>
  getCurrentUser: () => Promise<AuthUser | null>
  
  // Utility actions
  clearError: () => void
  completeOnboarding: () => void
}

// Screen navigation types
export type AuthStackParamList = {
  Welcome: undefined
  PhoneInput: { role?: UserRole }
  OTPVerification: { phone: string; type?: 'verification' | 'login' | 'password_reset' }
  Registration: { phone: string; role: UserRole }
  Login: { phone?: string }
  PasswordReset: { phone?: string }
  OnboardingComplete: { user: AuthUser }
}

// Form validation types
export interface ValidationError {
  field: string
  message: string
}

export interface FormState<T> {
  data: T
  errors: ValidationError[]
  isValid: boolean
  isSubmitting: boolean
}

// South African specific types
export interface SAPhoneNumber {
  countryCode: '+27'
  provider: '082' | '083' | '084' | '072' | '073' | '074' | '076' | '078' | '079'
  number: string
  formatted: string
  isValid: boolean
}

export interface SAProvince {
  code: string
  name: string
  cities: string[]
}

export const SA_PROVINCES: SAProvince[] = [
  {
    code: 'GP',
    name: 'Gauteng',
    cities: ['Johannesburg', 'Pretoria', 'Soweto', 'Sandton', 'Randburg', 'Roodepoort']
  },
  {
    code: 'WC',
    name: 'Western Cape',
    cities: ['Cape Town', 'Stellenbosch', 'Paarl', 'George', 'Mossel Bay']
  },
  {
    code: 'KZN',
    name: 'KwaZulu-Natal',
    cities: ['Durban', 'Pietermaritzburg', 'Newcastle', 'Richards Bay']
  },
  {
    code: 'EC',
    name: 'Eastern Cape',
    cities: ['Port Elizabeth', 'East London', 'Grahamstown', 'King Williams Town']
  },
  {
    code: 'LP',
    name: 'Limpopo',
    cities: ['Polokwane', 'Tzaneen', 'Thohoyandou', 'Giyani']
  },
  {
    code: 'MP',
    name: 'Mpumalanga',
    cities: ['Nelspruit', 'Witbank', 'Secunda', 'Middelburg']
  }
]

export const SA_PHONE_PROVIDERS = ['082', '083', '084', '072', '073', '074', '076', '078', '079'] as const
