/**
 * HVPPYPlug+ Authentication System Example
 * Complete integration example showing all authentication flows
 */

import React, { useEffect, useState } from 'react'
import { View, Text, StyleSheet, Alert } from 'react-native'
import { NavigationContainer } from '@react-navigation/native'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {
  useAuthStore,
  useAuth,
  useAuthActions,
  WelcomeScreen,
  PhoneInputScreen,
  OTPVerificationScreen,
  UserRegistrationScreen,
  LoginScreen,
  PasswordResetScreen,
  AuthStackParamList,
  UserRole,
  AuthUser
} from '@hvppyplug/ui-components-v2/auth'

const Stack = createNativeStackNavigator<AuthStackParamList>()

// Main App Component (shown after authentication)
const MainApp: React.FC<{ user: AuthUser }> = ({ user }) => {
  const { logout } = useAuthActions()

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: async () => {
            try {
              await logout()
            } catch (error) {
              console.error('Logout error:', error)
            }
          }
        }
      ]
    )
  }

  return (
    <View style={styles.mainApp}>
      <Text style={styles.welcomeText}>
        Welcome, {user.name}! 🎉
      </Text>
      <Text style={styles.roleText}>
        Role: {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
      </Text>
      <Text style={styles.phoneText}>
        Phone: {user.phone}
      </Text>
      <Text style={styles.emailText}>
        Email: {user.email}
      </Text>
      
      {/* Add your main app content here */}
      <Text style={styles.instructionText}>
        Your main app content goes here...
      </Text>
    </View>
  )
}

// Authentication Navigator
const AuthNavigator: React.FC<{ onAuthComplete: (user: AuthUser) => void }> = ({ 
  onAuthComplete 
}) => {
  // App-specific configuration
  const appConfig = {
    name: 'HVPPYPlug+ Demo',
    role: 'customer' as UserRole, // Change this for different apps
    primaryColor: '#FF6B6B',
    secondaryColor: '#FF8E8E'
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right'
      }}
      initialRouteName="Welcome"
    >
      {/* Welcome/Onboarding Screen */}
      <Stack.Screen name="Welcome">
        {({ navigation }) => (
          <WelcomeScreen
            onRoleSelect={(role) => {
              navigation.navigate('PhoneInput', { role })
            }}
            onSkip={() => {
              navigation.navigate('Login')
            }}
            appName={appConfig.name}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>

      {/* Phone Number Input */}
      <Stack.Screen name="PhoneInput">
        {({ navigation, route }) => (
          <PhoneInputScreen
            onPhoneSubmit={(phone) => {
              navigation.navigate('OTPVerification', { 
                phone, 
                type: 'verification' 
              })
            }}
            onBack={() => navigation.goBack()}
            role={route.params?.role || appConfig.role}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>

      {/* OTP Verification */}
      <Stack.Screen name="OTPVerification">
        {({ navigation, route }) => (
          <OTPVerificationScreen
            phone={route.params.phone}
            type={route.params.type || 'verification'}
            onVerificationSuccess={(phone, code) => {
              // In a real app, you'd check if user exists
              // For demo, always go to registration
              navigation.navigate('Registration', { 
                phone, 
                role: appConfig.role 
              })
            }}
            onBack={() => navigation.goBack()}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>

      {/* User Registration */}
      <Stack.Screen name="Registration">
        {({ navigation, route }) => (
          <UserRegistrationScreen
            phone={route.params.phone}
            role={route.params.role}
            onRegistrationSuccess={(user) => {
              Alert.alert(
                'Registration Successful!',
                `Welcome to ${appConfig.name}, ${user.name}!`,
                [
                  {
                    text: 'Continue',
                    onPress: () => onAuthComplete(user)
                  }
                ]
              )
            }}
            onBack={() => navigation.goBack()}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>

      {/* Login Screen */}
      <Stack.Screen name="Login">
        {({ navigation, route }) => (
          <LoginScreen
            onLoginSuccess={(user) => {
              onAuthComplete(user)
            }}
            onForgotPassword={(phone) => {
              navigation.navigate('PasswordReset', { phone })
            }}
            onSignUp={() => {
              navigation.navigate('PhoneInput', { role: appConfig.role })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>

      {/* Password Reset */}
      <Stack.Screen name="PasswordReset">
        {({ navigation, route }) => (
          <PasswordResetScreen
            onResetSuccess={() => {
              Alert.alert(
                'Password Reset Successful',
                'You can now login with your new password.',
                [
                  {
                    text: 'Login',
                    onPress: () => navigation.navigate('Login', { 
                      phone: route.params?.phone 
                    })
                  }
                ]
              )
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={appConfig.primaryColor}
            secondaryColor={appConfig.secondaryColor}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
}

// Main App Component
export const AuthExample: React.FC = () => {
  const { isAuthenticated, user, isLoading } = useAuth()
  const { getCurrentUser } = useAuthActions()
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await getCurrentUser()
      } catch (error) {
        console.error('Auth initialization error:', error)
      } finally {
        setIsInitialized(true)
      }
    }

    initializeAuth()
  }, [getCurrentUser])

  // Show loading screen while initializing
  if (!isInitialized || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    )
  }

  return (
    <NavigationContainer>
      {isAuthenticated && user ? (
        <MainApp user={user} />
      ) : (
        <AuthNavigator
          onAuthComplete={(user) => {
            console.log('Authentication completed for user:', user)
          }}
        />
      )}
    </NavigationContainer>
  )
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'
  },
  loadingText: {
    fontSize: 18,
    color: '#666'
  },
  mainApp: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5'
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center'
  },
  roleText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 8
  },
  phoneText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8
  },
  emailText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24
  },
  instructionText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic'
  }
})

export default AuthExample

// Usage Example:
// 
// import { AuthExample } from '@hvppyplug/ui-components-v2/auth/examples'
// 
// export default function App() {
//   return <AuthExample />
// }
//
// Or for app-specific implementation:
//
// import { AuthNavigator } from './screens/auth/AuthNavigator'
// import { useAuth } from '@hvppyplug/ui-components-v2/auth'
//
// export default function App() {
//   const { isAuthenticated, user } = useAuth()
//
//   if (!isAuthenticated) {
//     return (
//       <AuthNavigator
//         onAuthComplete={(user) => {
//           // Handle successful authentication
//         }}
//       />
//     )
//   }
//
//   return <YourMainApp user={user} />
// }
