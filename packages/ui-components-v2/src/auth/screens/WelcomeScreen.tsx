/**
 * Welcome/Onboarding Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Image,
  StatusBar
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { LinearGradient } from 'expo-linear-gradient'
import { Ionicons } from '@expo/vector-icons'
import { UserRole, getRoleDisplayName, getRoleDescription } from '../types'

const { width, height } = Dimensions.get('window')

interface WelcomeScreenProps {
  onRoleSelect: (role: UserRole) => void
  onSkip?: () => void
  appName?: string
  appLogo?: any
  primaryColor?: string
  secondaryColor?: string
}

interface RoleOption {
  role: UserRole
  icon: keyof typeof Ionicons.glyphMap
  color: string
  gradient: string[]
}

const roleOptions: RoleOption[] = [
  {
    role: 'customer',
    icon: 'restaurant-outline',
    color: '#FF6B6B',
    gradient: ['#FF6B6B', '#FF8E8E']
  },
  {
    role: 'vendor',
    icon: 'storefront-outline',
    color: '#4ECDC4',
    gradient: ['#4ECDC4', '#6ED5CE']
  },
  {
    role: 'runner',
    icon: 'bicycle-outline',
    color: '#45B7D1',
    gradient: ['#45B7D1', '#6BC5D8']
  }
]

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onRoleSelect,
  onSkip,
  appName = 'HVPPYPlug+',
  appLogo,
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null)

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role)
    // Add a small delay for visual feedback
    setTimeout(() => {
      onRoleSelect(role)
    }, 200)
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={primaryColor} />
      
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            {appLogo ? (
              <Image source={appLogo} style={styles.logo} resizeMode="contain" />
            ) : (
              <View style={styles.logoPlaceholder}>
                <Ionicons name="restaurant" size={60} color="white" />
              </View>
            )}
            
            <Text style={styles.appName}>{appName}</Text>
            <Text style={styles.tagline}>
              🇿🇦 South Africa's Premier Food Delivery Platform
            </Text>
          </View>

          {/* Welcome Message */}
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeTitle}>Welcome to {appName}!</Text>
            <Text style={styles.welcomeSubtitle}>
              Choose how you'd like to get started with our platform
            </Text>
          </View>

          {/* Role Selection */}
          <View style={styles.roleSection}>
            <Text style={styles.sectionTitle}>I want to...</Text>
            
            {roleOptions.map((option) => (
              <TouchableOpacity
                key={option.role}
                style={[
                  styles.roleCard,
                  selectedRole === option.role && styles.selectedRoleCard
                ]}
                onPress={() => handleRoleSelect(option.role)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={option.gradient}
                  style={styles.roleCardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <View style={styles.roleCardContent}>
                    <View style={styles.roleIconContainer}>
                      <Ionicons
                        name={option.icon}
                        size={32}
                        color="white"
                      />
                    </View>
                    
                    <View style={styles.roleTextContainer}>
                      <Text style={styles.roleTitle}>
                        {getRoleDisplayName(option.role)}
                      </Text>
                      <Text style={styles.roleDescription}>
                        {getRoleDescription(option.role)}
                      </Text>
                    </View>
                    
                    <View style={styles.roleArrow}>
                      <Ionicons
                        name="chevron-forward"
                        size={24}
                        color="white"
                      />
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>

          {/* Features Preview */}
          <View style={styles.featuresSection}>
            <Text style={styles.sectionTitle}>Why Choose {appName}?</Text>
            
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <Ionicons name="flash" size={20} color="white" />
                <Text style={styles.featureText}>Fast & Reliable Delivery</Text>
              </View>
              
              <View style={styles.featureItem}>
                <Ionicons name="shield-checkmark" size={20} color="white" />
                <Text style={styles.featureText}>Secure Payments</Text>
              </View>
              
              <View style={styles.featureItem}>
                <Ionicons name="location" size={20} color="white" />
                <Text style={styles.featureText}>Real-time Tracking</Text>
              </View>
              
              <View style={styles.featureItem}>
                <Ionicons name="star" size={20} color="white" />
                <Text style={styles.featureText}>Quality Guaranteed</Text>
              </View>
            </View>
          </View>

          {/* Skip Button */}
          {onSkip && (
            <TouchableOpacity
              style={styles.skipButton}
              onPress={onSkip}
              activeOpacity={0.7}
            >
              <Text style={styles.skipButtonText}>Skip for now</Text>
            </TouchableOpacity>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 40
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 20
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16
  },
  logoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center'
  },
  welcomeSection: {
    alignItems: 'center',
    paddingVertical: 30
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center'
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22
  },
  roleSection: {
    marginBottom: 30
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: 'white',
    marginBottom: 20,
    textAlign: 'center'
  },
  roleCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4
  },
  selectedRoleCard: {
    transform: [{ scale: 0.98 }]
  },
  roleCardGradient: {
    padding: 20
  },
  roleCardContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  roleIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16
  },
  roleTextContainer: {
    flex: 1
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginBottom: 4
  },
  roleDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18
  },
  roleArrow: {
    marginLeft: 12
  },
  featuresSection: {
    marginBottom: 30
  },
  featuresList: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 20
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  featureText: {
    fontSize: 16,
    color: 'white',
    marginLeft: 12
  },
  skipButton: {
    alignItems: 'center',
    paddingVertical: 16
  },
  skipButtonText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textDecorationLine: 'underline'
  }
})

export default WelcomeScreen
