/**
 * Phone Number Input Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { UserRole } from '../types'
import { validateSAPhoneNumber, formatPhoneNumber, SA_PHONE_PROVIDERS } from '../utils'
import { useAuthStore } from '../store'

interface PhoneInputScreenProps {
  onPhoneSubmit: (phone: string) => void
  onBack?: () => void
  role?: UserRole
  title?: string
  subtitle?: string
  primaryColor?: string
  secondaryColor?: string
}

export const PhoneInputScreen: React.FC<PhoneInputScreenProps> = ({
  onPhoneSubmit,
  onBack,
  role,
  title = 'Enter Your Phone Number',
  subtitle = 'We\'ll send you a verification code',
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [phone, setPhone] = useState('')
  const [formattedPhone, setFormattedPhone] = useState('')
  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState('')
  const phoneInputRef = useRef<TextInput>(null)
  
  const { sendOTP, isLoading, error: authError, clearError } = useAuthStore()

  useEffect(() => {
    // Focus on phone input when screen loads
    const timer = setTimeout(() => {
      phoneInputRef.current?.focus()
    }, 500)
    
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Clear auth errors when component mounts
    if (authError) {
      clearError()
    }
  }, [authError, clearError])

  const handlePhoneChange = (text: string) => {
    // Remove all non-digit characters for processing
    const cleanText = text.replace(/\D/g, '')
    
    // Limit input length
    if (cleanText.length > 10) return
    
    setPhone(cleanText)
    setError('')
    
    // Format and validate phone number
    if (cleanText.length > 0) {
      const validation = validateSAPhoneNumber(cleanText)
      setFormattedPhone(validation.formatted)
      setIsValid(validation.isValid && cleanText.length >= 9)
    } else {
      setFormattedPhone('')
      setIsValid(false)
    }
  }

  const handleSubmit = async () => {
    if (!isValid) {
      setError('Please enter a valid South African phone number')
      return
    }

    try {
      await sendOTP(phone, 'verification')
      onPhoneSubmit(phone)
    } catch (error: any) {
      setError(error.message || 'Failed to send verification code')
    }
  }

  const getPlaceholderText = () => {
    const randomProvider = SA_PHONE_PROVIDERS[Math.floor(Math.random() * SA_PHONE_PROVIDERS.length)]
    return `${randomProvider} 123 4567`
  }

  const getRoleSpecificContent = () => {
    switch (role) {
      case 'vendor':
        return {
          icon: 'storefront-outline' as keyof typeof Ionicons.glyphMap,
          color: '#4ECDC4',
          message: 'Register your business and start selling'
        }
      case 'runner':
        return {
          icon: 'bicycle-outline' as keyof typeof Ionicons.glyphMap,
          color: '#45B7D1',
          message: 'Join our delivery team and earn money'
        }
      default:
        return {
          icon: 'restaurant-outline' as keyof typeof Ionicons.glyphMap,
          color: '#FF6B6B',
          message: 'Order from your favorite restaurants'
        }
    }
  }

  const roleContent = getRoleSpecificContent()

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            {onBack && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                activeOpacity={0.7}
              >
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>
            )}
            
            <View style={styles.headerContent}>
              <View style={[styles.iconContainer, { backgroundColor: roleContent.color }]}>
                <Ionicons name={roleContent.icon} size={32} color="white" />
              </View>
              
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.subtitle}>{subtitle}</Text>
              
              {role && (
                <Text style={styles.roleMessage}>{roleContent.message}</Text>
              )}
            </View>
          </View>

          {/* Phone Input Form */}
          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <View style={styles.countryCode}>
                <Text style={styles.countryCodeText}>🇿🇦 +27</Text>
              </View>
              
              <TextInput
                ref={phoneInputRef}
                style={[
                  styles.phoneInput,
                  isValid && styles.validInput,
                  error && styles.errorInput
                ]}
                value={phone}
                onChangeText={handlePhoneChange}
                placeholder={getPlaceholderText()}
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                keyboardType="phone-pad"
                maxLength={10}
                autoComplete="tel"
                textContentType="telephoneNumber"
              />
            </View>
            
            {formattedPhone && (
              <Text style={styles.formattedPhone}>
                Formatted: {formattedPhone}
              </Text>
            )}
            
            {error && (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={16} color="#FF4757" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Provider Info */}
            <View style={styles.providerInfo}>
              <Text style={styles.providerTitle}>Supported Networks:</Text>
              <View style={styles.providerList}>
                {SA_PHONE_PROVIDERS.map((provider, index) => (
                  <View key={provider} style={styles.providerItem}>
                    <Text style={styles.providerText}>{provider}</Text>
                    {index < SA_PHONE_PROVIDERS.length - 1 && (
                      <Text style={styles.providerSeparator}>•</Text>
                    )}
                  </View>
                ))}
              </View>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              style={[
                styles.submitButton,
                isValid && styles.submitButtonActive,
                isLoading && styles.submitButtonLoading
              ]}
              onPress={handleSubmit}
              disabled={!isValid || isLoading}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Text style={styles.submitButtonText}>
                    Send Verification Code
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color="white" />
                </>
              )}
            </TouchableOpacity>

            {/* Terms and Privacy */}
            <Text style={styles.termsText}>
              By continuing, you agree to our{' '}
              <Text style={styles.termsLink}>Terms of Service</Text>
              {' '}and{' '}
              <Text style={styles.termsLink}>Privacy Policy</Text>
            </Text>
          </View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardAvoid: {
    flex: 1
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  headerContent: {
    alignItems: 'center'
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 8
  },
  roleMessage: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontStyle: 'italic'
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 20
  },
  inputContainer: {
    flexDirection: 'row',
    marginBottom: 12
  },
  countryCode: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    justifyContent: 'center'
  },
  countryCodeText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600'
  },
  phoneInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    fontSize: 16,
    color: 'white',
    borderWidth: 2,
    borderColor: 'transparent'
  },
  validInput: {
    borderColor: '#4CAF50'
  },
  errorInput: {
    borderColor: '#FF4757'
  },
  formattedPhone: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 12
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16
  },
  errorText: {
    fontSize: 14,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1
  },
  providerInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24
  },
  providerTitle: {
    fontSize: 14,
    color: 'white',
    fontWeight: '600',
    marginBottom: 8
  },
  providerList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center'
  },
  providerItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  providerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'monospace'
  },
  providerSeparator: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 8
  },
  submitButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 18,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24
  },
  submitButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)'
  },
  submitButtonLoading: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)'
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8
  },
  termsText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 18
  },
  termsLink: {
    textDecorationLine: 'underline',
    fontWeight: '600'
  }
})

export default PhoneInputScreen
