/**
 * Password Reset Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { validateSAPhoneNumber, validatePassword, validateOTP, formatPhoneNumber } from '../utils'
import { useAuthStore } from '../store'

interface PasswordResetScreenProps {
  onResetSuccess: () => void
  onBack?: () => void
  initialPhone?: string
  primaryColor?: string
  secondaryColor?: string
}

type ResetStep = 'phone' | 'otp' | 'password'

export const PasswordResetScreen: React.FC<PasswordResetScreenProps> = ({
  onResetSuccess,
  onBack,
  initialPhone = '',
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [currentStep, setCurrentStep] = useState<ResetStep>('phone')
  const [phone, setPhone] = useState(initialPhone)
  const [otpCode, setOtpCode] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isPhoneValid, setIsPhoneValid] = useState(false)
  const [error, setError] = useState('')
  const [passwordErrors, setPasswordErrors] = useState<string[]>([])

  const phoneInputRef = useRef<TextInput>(null)
  const otpInputRef = useRef<TextInput>(null)
  const passwordInputRef = useRef<TextInput>(null)

  const { sendOTP, resetPassword, isLoading, error: authError, clearError } = useAuthStore()

  useEffect(() => {
    // Focus on appropriate input when step changes
    const timer = setTimeout(() => {
      switch (currentStep) {
        case 'phone':
          phoneInputRef.current?.focus()
          break
        case 'otp':
          otpInputRef.current?.focus()
          break
        case 'password':
          passwordInputRef.current?.focus()
          break
      }
    }, 500)
    
    return () => clearTimeout(timer)
  }, [currentStep])

  useEffect(() => {
    // Clear auth errors when component mounts
    if (authError) {
      clearError()
    }
  }, [authError, clearError])

  const handlePhoneChange = (text: string) => {
    const cleanText = text.replace(/\D/g, '')
    if (cleanText.length > 10) return
    
    setPhone(cleanText)
    setError('')
    
    // Validate phone number
    if (cleanText.length > 0) {
      const validation = validateSAPhoneNumber(cleanText)
      setIsPhoneValid(validation.isValid && cleanText.length >= 9)
    } else {
      setIsPhoneValid(false)
    }
  }

  const handlePasswordChange = (text: string) => {
    setNewPassword(text)
    setError('')
    
    // Validate password strength
    const validation = validatePassword(text)
    setPasswordErrors(validation.errors)
  }

  const handleSendOTP = async () => {
    if (!isPhoneValid) {
      setError('Please enter a valid South African phone number')
      return
    }

    try {
      await sendOTP(phone, 'password_reset')
      setCurrentStep('otp')
      Alert.alert(
        'Reset Code Sent',
        `A password reset code has been sent to ${formatPhoneNumber(phone)}`
      )
    } catch (error: any) {
      setError(error.message || 'Failed to send reset code')
    }
  }

  const handleVerifyOTP = () => {
    if (!validateOTP(otpCode)) {
      setError('Please enter a valid 6-digit code')
      return
    }

    setCurrentStep('password')
    setError('')
  }

  const handleResetPassword = async () => {
    if (passwordErrors.length > 0) {
      setError('Please fix password requirements')
      return
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    try {
      await resetPassword({
        phone,
        code: otpCode,
        newPassword,
        confirmPassword
      })
      
      Alert.alert(
        'Password Reset Successful',
        'Your password has been reset successfully. You can now log in with your new password.',
        [{ text: 'OK', onPress: onResetSuccess }]
      )
    } catch (error: any) {
      setError(error.message || 'Password reset failed')
    }
  }

  const renderPhoneStep = () => (
    <>
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="key-outline" size={32} color="white" />
        </View>
        <Text style={styles.title}>Reset Password</Text>
        <Text style={styles.subtitle}>
          Enter your phone number to receive a reset code
        </Text>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Phone Number</Text>
        <View style={styles.phoneInputContainer}>
          <View style={styles.countryCode}>
            <Text style={styles.countryCodeText}>🇿🇦 +27</Text>
          </View>
          <TextInput
            ref={phoneInputRef}
            style={[
              styles.phoneInput,
              isPhoneValid && styles.validInput,
              error && styles.errorInput
            ]}
            value={phone}
            onChangeText={handlePhoneChange}
            placeholder="************"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
            keyboardType="phone-pad"
            maxLength={10}
            autoComplete="tel"
            textContentType="telephoneNumber"
            returnKeyType="done"
            onSubmitEditing={handleSendOTP}
          />
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.actionButton,
          isPhoneValid && styles.actionButtonActive,
          isLoading && styles.actionButtonLoading
        ]}
        onPress={handleSendOTP}
        disabled={!isPhoneValid || isLoading}
        activeOpacity={0.8}
      >
        {isLoading ? (
          <ActivityIndicator color="white" size="small" />
        ) : (
          <>
            <Text style={styles.actionButtonText}>Send Reset Code</Text>
            <Ionicons name="arrow-forward" size={20} color="white" />
          </>
        )}
      </TouchableOpacity>
    </>
  )

  const renderOTPStep = () => (
    <>
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="chatbubble-outline" size={32} color="white" />
        </View>
        <Text style={styles.title}>Enter Reset Code</Text>
        <Text style={styles.subtitle}>
          Enter the 6-digit code sent to {formatPhoneNumber(phone)}
        </Text>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Reset Code</Text>
        <TextInput
          ref={otpInputRef}
          style={[
            styles.otpInput,
            validateOTP(otpCode) && styles.validInput,
            error && styles.errorInput
          ]}
          value={otpCode}
          onChangeText={setOtpCode}
          placeholder="123456"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
          keyboardType="number-pad"
          maxLength={6}
          textAlign="center"
          fontSize={24}
          fontWeight="bold"
          returnKeyType="done"
          onSubmitEditing={handleVerifyOTP}
        />
      </View>

      <TouchableOpacity
        style={styles.resendButton}
        onPress={handleSendOTP}
        disabled={isLoading}
        activeOpacity={0.7}
      >
        <Ionicons name="refresh" size={16} color="rgba(255, 255, 255, 0.8)" />
        <Text style={styles.resendButtonText}>Resend Code</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.actionButton,
          validateOTP(otpCode) && styles.actionButtonActive
        ]}
        onPress={handleVerifyOTP}
        disabled={!validateOTP(otpCode)}
        activeOpacity={0.8}
      >
        <Text style={styles.actionButtonText}>Verify Code</Text>
        <Ionicons name="checkmark" size={20} color="white" />
      </TouchableOpacity>
    </>
  )

  const renderPasswordStep = () => (
    <>
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="lock-closed-outline" size={32} color="white" />
        </View>
        <Text style={styles.title}>New Password</Text>
        <Text style={styles.subtitle}>
          Create a strong password for your account
        </Text>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>New Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            ref={passwordInputRef}
            style={[styles.passwordInput, error && styles.errorInput]}
            value={newPassword}
            onChangeText={handlePasswordChange}
            placeholder="Enter new password"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
            secureTextEntry={!showPassword}
            autoComplete="password-new"
            textContentType="newPassword"
            returnKeyType="next"
            onSubmitEditing={() => {
              // Focus on confirm password input
            }}
          />
          <TouchableOpacity
            style={styles.passwordToggle}
            onPress={() => setShowPassword(!showPassword)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={showPassword ? 'eye-off-outline' : 'eye-outline'}
              size={20}
              color="rgba(255, 255, 255, 0.7)"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Confirm Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            style={[styles.passwordInput, error && styles.errorInput]}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm new password"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
            secureTextEntry={!showConfirmPassword}
            autoComplete="password-new"
            textContentType="newPassword"
            returnKeyType="done"
            onSubmitEditing={handleResetPassword}
          />
          <TouchableOpacity
            style={styles.passwordToggle}
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
              size={20}
              color="rgba(255, 255, 255, 0.7)"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Password Requirements */}
      {passwordErrors.length > 0 && (
        <View style={styles.requirementsContainer}>
          <Text style={styles.requirementsTitle}>Password Requirements:</Text>
          {passwordErrors.map((error, index) => (
            <View key={index} style={styles.requirementItem}>
              <Ionicons name="close-circle" size={16} color="#FF4757" />
              <Text style={styles.requirementText}>{error}</Text>
            </View>
          ))}
        </View>
      )}

      <TouchableOpacity
        style={[
          styles.actionButton,
          passwordErrors.length === 0 && newPassword === confirmPassword && newPassword.length > 0 && styles.actionButtonActive,
          isLoading && styles.actionButtonLoading
        ]}
        onPress={handleResetPassword}
        disabled={passwordErrors.length > 0 || newPassword !== confirmPassword || !newPassword || isLoading}
        activeOpacity={0.8}
      >
        {isLoading ? (
          <ActivityIndicator color="white" size="small" />
        ) : (
          <>
            <Text style={styles.actionButtonText}>Reset Password</Text>
            <Ionicons name="checkmark-done" size={20} color="white" />
          </>
        )}
      </TouchableOpacity>
    </>
  )

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            {onBack && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                activeOpacity={0.7}
              >
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>
            )}

            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              {['phone', 'otp', 'password'].map((step, index) => (
                <View key={step} style={styles.progressStep}>
                  <View style={[
                    styles.progressDot,
                    (currentStep === step || 
                     (currentStep === 'otp' && step === 'phone') ||
                     (currentStep === 'password' && (step === 'phone' || step === 'otp'))
                    ) && styles.progressDotActive
                  ]} />
                  {index < 2 && <View style={styles.progressLine} />}
                </View>
              ))}
            </View>
          </View>

          {/* Form Content */}
          <View style={styles.formContainer}>
            {currentStep === 'phone' && renderPhoneStep()}
            {currentStep === 'otp' && renderOTPStep()}
            {currentStep === 'password' && renderPasswordStep()}

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={16} color="#FF4757" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardAvoid: {
    flex: 1
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 30
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)'
  },
  progressDotActive: {
    backgroundColor: 'white'
  },
  progressLine: {
    width: 40,
    height: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 8
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 20
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 40
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center'
  },
  inputGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
    fontWeight: '500'
  },
  phoneInputContainer: {
    flexDirection: 'row'
  },
  countryCode: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    justifyContent: 'center'
  },
  countryCodeText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600'
  },
  phoneInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    fontSize: 16,
    color: 'white',
    borderWidth: 2,
    borderColor: 'transparent'
  },
  otpInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderRadius: 12,
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    borderWidth: 2,
    borderColor: 'transparent',
    textAlign: 'center'
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent'
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 18,
    fontSize: 16,
    color: 'white'
  },
  passwordToggle: {
    paddingHorizontal: 16,
    paddingVertical: 18
  },
  validInput: {
    borderColor: '#4CAF50'
  },
  errorInput: {
    borderColor: '#FF4757'
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginBottom: 24
  },
  resendButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 8,
    textDecorationLine: 'underline'
  },
  requirementsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24
  },
  requirementsTitle: {
    fontSize: 14,
    color: 'white',
    fontWeight: '600',
    marginBottom: 8
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4
  },
  requirementText: {
    fontSize: 12,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 18,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24
  },
  actionButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)'
  },
  actionButtonLoading: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)'
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16
  },
  errorText: {
    fontSize: 14,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1
  }
})

export default PasswordResetScreen
