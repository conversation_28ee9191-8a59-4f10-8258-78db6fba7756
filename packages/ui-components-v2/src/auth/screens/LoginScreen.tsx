/**
 * Login Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { validateSAPhoneNumber, formatPhoneNumber } from '../utils'
import { useAuthStore } from '../store'

interface LoginScreenProps {
  onLoginSuccess: (user: any) => void
  onForgotPassword?: (phone: string) => void
  onSignUp?: () => void
  onBack?: () => void
  initialPhone?: string
  primaryColor?: string
  secondaryColor?: string
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onLoginSuccess,
  onForgotPassword,
  onSignUp,
  onBack,
  initialPhone = '',
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [phone, setPhone] = useState(initialPhone)
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [useOTP, setUseOTP] = useState(false)
  const [isPhoneValid, setIsPhoneValid] = useState(false)
  const [error, setError] = useState('')

  const phoneInputRef = useRef<TextInput>(null)
  const passwordInputRef = useRef<TextInput>(null)

  const { login, sendOTP, isLoading, error: authError, clearError } = useAuthStore()

  useEffect(() => {
    // Focus on phone input when screen loads
    const timer = setTimeout(() => {
      phoneInputRef.current?.focus()
    }, 500)
    
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Clear auth errors when component mounts
    if (authError) {
      clearError()
    }
  }, [authError, clearError])

  const handlePhoneChange = (text: string) => {
    const cleanText = text.replace(/\D/g, '')
    if (cleanText.length > 10) return
    
    setPhone(cleanText)
    setError('')
    
    // Validate phone number
    if (cleanText.length > 0) {
      const validation = validateSAPhoneNumber(cleanText)
      setIsPhoneValid(validation.isValid && cleanText.length >= 9)
    } else {
      setIsPhoneValid(false)
    }
  }

  const handlePasswordLogin = async () => {
    if (!isPhoneValid) {
      setError('Please enter a valid South African phone number')
      return
    }

    if (!password.trim()) {
      setError('Please enter your password')
      return
    }

    try {
      const user = await login({ phone, password })
      onLoginSuccess(user)
    } catch (error: any) {
      setError(error.message || 'Login failed')
    }
  }

  const handleOTPLogin = async () => {
    if (!isPhoneValid) {
      setError('Please enter a valid South African phone number')
      return
    }

    try {
      await sendOTP(phone, 'login')
      Alert.alert(
        'OTP Sent',
        `A verification code has been sent to ${formatPhoneNumber(phone)}`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to OTP verification screen
              // This would be handled by the parent component
            }
          }
        ]
      )
    } catch (error: any) {
      setError(error.message || 'Failed to send OTP')
    }
  }

  const handleForgotPassword = () => {
    if (!isPhoneValid) {
      setError('Please enter a valid phone number first')
      return
    }
    
    onForgotPassword?.(phone)
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            {onBack && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                activeOpacity={0.7}
              >
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>
            )}
            
            <View style={styles.headerContent}>
              <View style={styles.iconContainer}>
                <Ionicons name="log-in-outline" size={32} color="white" />
              </View>
              
              <Text style={styles.title}>Welcome Back!</Text>
              <Text style={styles.subtitle}>
                Sign in to your HVPPYPlug+ account
              </Text>
            </View>
          </View>

          {/* Login Form */}
          <View style={styles.formContainer}>
            {/* Phone Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone Number</Text>
              <View style={styles.phoneInputContainer}>
                <View style={styles.countryCode}>
                  <Text style={styles.countryCodeText}>🇿🇦 +27</Text>
                </View>
                <TextInput
                  ref={phoneInputRef}
                  style={[
                    styles.phoneInput,
                    isPhoneValid && styles.validInput,
                    error && styles.errorInput
                  ]}
                  value={phone}
                  onChangeText={handlePhoneChange}
                  placeholder="************"
                  placeholderTextColor="rgba(255, 255, 255, 0.6)"
                  keyboardType="phone-pad"
                  maxLength={10}
                  autoComplete="tel"
                  textContentType="telephoneNumber"
                  returnKeyType="next"
                  onSubmitEditing={() => {
                    if (!useOTP) {
                      passwordInputRef.current?.focus()
                    }
                  }}
                />
              </View>
            </View>

            {/* Login Method Toggle */}
            <View style={styles.toggleContainer}>
              <TouchableOpacity
                style={[styles.toggleButton, !useOTP && styles.toggleButtonActive]}
                onPress={() => setUseOTP(false)}
                activeOpacity={0.7}
              >
                <Ionicons 
                  name="key-outline" 
                  size={16} 
                  color={!useOTP ? primaryColor : 'rgba(255, 255, 255, 0.7)'} 
                />
                <Text style={[
                  styles.toggleText,
                  !useOTP && styles.toggleTextActive
                ]}>
                  Password
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.toggleButton, useOTP && styles.toggleButtonActive]}
                onPress={() => setUseOTP(true)}
                activeOpacity={0.7}
              >
                <Ionicons 
                  name="chatbubble-outline" 
                  size={16} 
                  color={useOTP ? primaryColor : 'rgba(255, 255, 255, 0.7)'} 
                />
                <Text style={[
                  styles.toggleText,
                  useOTP && styles.toggleTextActive
                ]}>
                  SMS Code
                </Text>
              </TouchableOpacity>
            </View>

            {/* Password Input (conditional) */}
            {!useOTP && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Password</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    ref={passwordInputRef}
                    style={[styles.passwordInput, error && styles.errorInput]}
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Enter your password"
                    placeholderTextColor="rgba(255, 255, 255, 0.6)"
                    secureTextEntry={!showPassword}
                    autoComplete="password"
                    textContentType="password"
                    returnKeyType="done"
                    onSubmitEditing={handlePasswordLogin}
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowPassword(!showPassword)}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                      size={20}
                      color="rgba(255, 255, 255, 0.7)"
                    />
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={16} color="#FF4757" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Login Button */}
            <TouchableOpacity
              style={[
                styles.loginButton,
                isPhoneValid && styles.loginButtonActive,
                isLoading && styles.loginButtonLoading
              ]}
              onPress={useOTP ? handleOTPLogin : handlePasswordLogin}
              disabled={!isPhoneValid || isLoading || (!useOTP && !password.trim())}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Text style={styles.loginButtonText}>
                    {useOTP ? 'Send SMS Code' : 'Sign In'}
                  </Text>
                  <Ionicons 
                    name={useOTP ? 'chatbubble' : 'log-in'} 
                    size={20} 
                    color="white" 
                  />
                </>
              )}
            </TouchableOpacity>

            {/* Forgot Password */}
            {!useOTP && onForgotPassword && (
              <TouchableOpacity
                style={styles.forgotButton}
                onPress={handleForgotPassword}
                activeOpacity={0.7}
              >
                <Text style={styles.forgotButtonText}>Forgot Password?</Text>
              </TouchableOpacity>
            )}

            {/* Divider */}
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Sign Up */}
            {onSignUp && (
              <TouchableOpacity
                style={styles.signUpButton}
                onPress={onSignUp}
                activeOpacity={0.7}
              >
                <Text style={styles.signUpButtonText}>
                  Don't have an account?{' '}
                  <Text style={styles.signUpButtonLink}>Sign Up</Text>
                </Text>
              </TouchableOpacity>
            )}

            {/* Help */}
            <TouchableOpacity
              style={styles.helpButton}
              onPress={() => Alert.alert(
                'Need Help?',
                'Contact our support team:\n\n📧 <EMAIL>\n📞 +27 11 123 4567\n\nWe\'re here to help 24/7!'
              )}
              activeOpacity={0.7}
            >
              <Ionicons name="help-circle-outline" size={16} color="rgba(255, 255, 255, 0.7)" />
              <Text style={styles.helpButtonText}>Need Help?</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardAvoid: {
    flex: 1
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  headerContent: {
    alignItems: 'center'
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center'
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 20
  },
  inputGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
    fontWeight: '500'
  },
  phoneInputContainer: {
    flexDirection: 'row'
  },
  countryCode: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    justifyContent: 'center'
  },
  countryCodeText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600'
  },
  phoneInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 18,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    fontSize: 16,
    color: 'white',
    borderWidth: 2,
    borderColor: 'transparent'
  },
  validInput: {
    borderColor: '#4CAF50'
  },
  errorInput: {
    borderColor: '#FF4757'
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 4,
    marginBottom: 16
  },
  toggleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8
  },
  toggleButtonActive: {
    backgroundColor: 'white'
  },
  toggleText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: 6,
    fontWeight: '500'
  },
  toggleTextActive: {
    color: '#333'
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent'
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 18,
    fontSize: 16,
    color: 'white'
  },
  passwordToggle: {
    paddingHorizontal: 16,
    paddingVertical: 18
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16
  },
  errorText: {
    fontSize: 14,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1
  },
  loginButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 18,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16
  },
  loginButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)'
  },
  loginButtonLoading: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)'
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8
  },
  forgotButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24
  },
  forgotButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textDecorationLine: 'underline',
    fontWeight: '500'
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)'
  },
  dividerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginHorizontal: 16
  },
  signUpButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 16
  },
  signUpButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)'
  },
  signUpButtonLink: {
    fontWeight: '600',
    textDecorationLine: 'underline'
  },
  helpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12
  },
  helpButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: 6
  }
})

export default LoginScreen
