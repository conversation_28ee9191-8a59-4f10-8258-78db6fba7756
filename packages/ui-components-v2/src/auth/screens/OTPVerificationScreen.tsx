/**
 * OTP Verification Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { validateOTP, maskPhoneNumber, formatOTPTimeRemaining } from '../utils'
import { useAuthStore } from '../store'

interface OTPVerificationScreenProps {
  phone: string
  onVerificationSuccess: (phone: string, code: string) => void
  onBack?: () => void
  onResendOTP?: () => void
  type?: 'verification' | 'login' | 'password_reset'
  title?: string
  subtitle?: string
  primaryColor?: string
  secondaryColor?: string
}

export const OTPVerificationScreen: React.FC<OTPVerificationScreenProps> = ({
  phone,
  onVerificationSuccess,
  onBack,
  onResendOTP,
  type = 'verification',
  title = 'Verify Your Phone',
  subtitle = 'Enter the 6-digit code sent to your phone',
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [code, setCode] = useState(['', '', '', '', '', ''])
  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState('')
  const [timeRemaining, setTimeRemaining] = useState(600) // 10 minutes
  const [canResend, setCanResend] = useState(false)
  
  const inputRefs = useRef<(TextInput | null)[]>([])
  const { verifyOTP, sendOTP, isLoading, error: authError, clearError } = useAuthStore()

  useEffect(() => {
    // Focus on first input when screen loads
    const timer = setTimeout(() => {
      inputRefs.current[0]?.focus()
    }, 500)
    
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Clear auth errors when component mounts
    if (authError) {
      clearError()
    }
  }, [authError, clearError])

  useEffect(() => {
    // Countdown timer
    const timer = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    // Validate OTP when code changes
    const fullCode = code.join('')
    const valid = validateOTP(fullCode)
    setIsValid(valid)
    setError('')
    
    // Auto-submit when code is complete and valid
    if (valid && fullCode.length === 6) {
      handleVerification(fullCode)
    }
  }, [code])

  const handleCodeChange = (value: string, index: number) => {
    // Only allow digits
    const digit = value.replace(/\D/g, '').slice(-1)
    
    const newCode = [...code]
    newCode[index] = digit
    setCode(newCode)
    
    // Move to next input if digit entered
    if (digit && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyPress = (key: string, index: number) => {
    // Handle backspace
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handleVerification = async (otpCode?: string) => {
    const fullCode = otpCode || code.join('')
    
    if (!validateOTP(fullCode)) {
      setError('Please enter a valid 6-digit code')
      return
    }

    try {
      await verifyOTP(phone, fullCode)
      onVerificationSuccess(phone, fullCode)
    } catch (error: any) {
      if (error.message === 'USER_NOT_FOUND') {
        // User doesn't exist, proceed to registration
        onVerificationSuccess(phone, fullCode)
      } else {
        setError(error.message || 'Verification failed')
        // Clear the code on error
        setCode(['', '', '', '', '', ''])
        inputRefs.current[0]?.focus()
      }
    }
  }

  const handleResendOTP = async () => {
    if (!canResend) return
    
    try {
      await sendOTP(phone, type)
      setTimeRemaining(600) // Reset to 10 minutes
      setCanResend(false)
      setError('')
      Alert.alert('Code Sent', 'A new verification code has been sent to your phone.')
    } catch (error: any) {
      setError(error.message || 'Failed to resend code')
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getTypeSpecificContent = () => {
    switch (type) {
      case 'login':
        return {
          icon: 'log-in-outline' as keyof typeof Ionicons.glyphMap,
          message: 'Logging you in securely'
        }
      case 'password_reset':
        return {
          icon: 'key-outline' as keyof typeof Ionicons.glyphMap,
          message: 'Resetting your password'
        }
      default:
        return {
          icon: 'shield-checkmark-outline' as keyof typeof Ionicons.glyphMap,
          message: 'Verifying your phone number'
        }
    }
  }

  const typeContent = getTypeSpecificContent()

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            {onBack && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                activeOpacity={0.7}
              >
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>
            )}
            
            <View style={styles.headerContent}>
              <View style={styles.iconContainer}>
                <Ionicons name={typeContent.icon} size={32} color="white" />
              </View>
              
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.subtitle}>{subtitle}</Text>
              
              <View style={styles.phoneContainer}>
                <Text style={styles.phoneText}>
                  Code sent to {maskPhoneNumber(phone)}
                </Text>
              </View>
            </View>
          </View>

          {/* OTP Input Form */}
          <View style={styles.formContainer}>
            <View style={styles.otpContainer}>
              {code.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  style={[
                    styles.otpInput,
                    digit && styles.otpInputFilled,
                    error && styles.otpInputError
                  ]}
                  value={digit}
                  onChangeText={(value) => handleCodeChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="number-pad"
                  maxLength={1}
                  selectTextOnFocus
                  textAlign="center"
                />
              ))}
            </View>
            
            {error && (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={16} color="#FF4757" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Timer and Resend */}
            <View style={styles.timerContainer}>
              {timeRemaining > 0 ? (
                <Text style={styles.timerText}>
                  Code expires in {formatTime(timeRemaining)}
                </Text>
              ) : (
                <Text style={styles.expiredText}>
                  Code has expired
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.resendButton,
                canResend && styles.resendButtonActive
              ]}
              onPress={handleResendOTP}
              disabled={!canResend || isLoading}
              activeOpacity={0.7}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Ionicons name="refresh" size={16} color="white" />
                  <Text style={styles.resendButtonText}>
                    {canResend ? 'Resend Code' : `Resend in ${formatTime(timeRemaining)}`}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {/* Manual Verify Button */}
            <TouchableOpacity
              style={[
                styles.verifyButton,
                isValid && styles.verifyButtonActive
              ]}
              onPress={() => handleVerification()}
              disabled={!isValid || isLoading}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Text style={styles.verifyButtonText}>Verify Code</Text>
                  <Ionicons name="checkmark" size={20} color="white" />
                </>
              )}
            </TouchableOpacity>

            {/* Help Text */}
            <View style={styles.helpContainer}>
              <Text style={styles.helpText}>
                Didn't receive the code? Check your SMS messages or try resending.
              </Text>
              
              <TouchableOpacity
                style={styles.helpButton}
                onPress={() => Alert.alert(
                  'Need Help?',
                  'If you\'re not receiving SMS codes, please check:\n\n• Your phone has signal\n• SMS is not blocked\n• The number is correct\n\nContact support if the problem persists.'
                )}
                activeOpacity={0.7}
              >
                <Text style={styles.helpButtonText}>Need Help?</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardAvoid: {
    flex: 1
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  headerContent: {
    alignItems: 'center'
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 16
  },
  phoneContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20
  },
  phoneText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500'
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 20
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24
  },
  otpInput: {
    width: 50,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    borderWidth: 2,
    borderColor: 'transparent'
  },
  otpInputFilled: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderColor: 'rgba(255, 255, 255, 0.5)'
  },
  otpInputError: {
    borderColor: '#FF4757'
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16
  },
  errorText: {
    fontSize: 14,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 16
  },
  timerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: 'monospace'
  },
  expiredText: {
    fontSize: 14,
    color: '#FF4757',
    fontWeight: '600'
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 24
  },
  resendButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)'
  },
  resendButtonText: {
    fontSize: 14,
    color: 'white',
    marginLeft: 8,
    fontWeight: '500'
  },
  verifyButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 18,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24
  },
  verifyButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)'
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8
  },
  helpContainer: {
    alignItems: 'center'
  },
  helpText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 12
  },
  helpButton: {
    paddingVertical: 8,
    paddingHorizontal: 16
  },
  helpButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textDecorationLine: 'underline',
    fontWeight: '500'
  }
})

export default OTPVerificationScreen
