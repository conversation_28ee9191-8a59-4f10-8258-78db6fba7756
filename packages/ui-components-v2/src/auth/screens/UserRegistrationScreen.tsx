/**
 * User Registration Screen Component
 * HVPPYPlug+ Authentication System
 */

import React, { useState, useRef } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import { Picker } from '@react-native-picker/picker'
import {
  UserRole,
  UserRegistrationData,
  VendorRegistrationData,
  RunnerRegistrationData,
  ValidationError
} from '../types'
import { validateUserRegistration, validateEmail, validatePassword, SA_PROVINCES } from '../utils'
import { useAuthStore } from '../store'

interface UserRegistrationScreenProps {
  phone: string
  role: UserRole
  onRegistrationSuccess: (user: any) => void
  onBack?: () => void
  primaryColor?: string
  secondaryColor?: string
}

export const UserRegistrationScreen: React.FC<UserRegistrationScreenProps> = ({
  phone,
  role,
  onRegistrationSuccess,
  onBack,
  primaryColor = '#FF6B6B',
  secondaryColor = '#4ECDC4'
}) => {
  const [formData, setFormData] = useState<any>({
    name: '',
    email: '',
    phone,
    role,
    dateOfBirth: '',
    gender: '',
    // Vendor specific
    businessName: '',
    businessType: 'restaurant',
    address: {
      street: '',
      city: '',
      province: 'Gauteng',
      postalCode: '',
      country: 'South Africa'
    },
    // Runner specific
    vehicleType: 'bicycle',
    vehicleDetails: {
      make: '',
      model: '',
      year: '',
      color: '',
      licensePlate: ''
    },
    licenseNumber: '',
    workingAreas: [],
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  })

  const [errors, setErrors] = useState<ValidationError[]>([])
  const [showPassword, setShowPassword] = useState(false)
  const scrollViewRef = useRef<ScrollView>(null)

  const { registerUser, registerVendor, registerRunner, isLoading, error: authError, clearError } = useAuthStore()

  const updateFormData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }))
    
    // Clear field-specific errors
    setErrors(prev => prev.filter(error => error.field !== field))
  }

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
    
    // Clear field-specific errors
    setErrors(prev => prev.filter(error => error.field !== `${parent}.${field}` && error.field !== parent))
  }

  const validateForm = (): boolean => {
    const validationErrors = validateUserRegistration(formData, role)
    setErrors(validationErrors)
    
    if (validationErrors.length > 0) {
      // Scroll to first error
      scrollViewRef.current?.scrollTo({ y: 0, animated: true })
      return false
    }
    
    return true
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      let user
      
      switch (role) {
        case 'vendor':
          user = await registerVendor(formData as VendorRegistrationData)
          break
        case 'runner':
          user = await registerRunner(formData as RunnerRegistrationData)
          break
        default:
          user = await registerUser(formData as UserRegistrationData)
          break
      }
      
      onRegistrationSuccess(user)
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message || 'Please try again')
    }
  }

  const getFieldError = (field: string): string | undefined => {
    return errors.find(error => error.field === field)?.message
  }

  const renderBasicFields = () => (
    <>
      {/* Name */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Full Name *</Text>
        <TextInput
          style={[styles.input, getFieldError('name') && styles.inputError]}
          value={formData.name}
          onChangeText={(value) => updateFormData('name', value)}
          placeholder="Enter your full name"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
          autoComplete="name"
          textContentType="name"
        />
        {getFieldError('name') && (
          <Text style={styles.errorText}>{getFieldError('name')}</Text>
        )}
      </View>

      {/* Email */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Email Address *</Text>
        <TextInput
          style={[styles.input, getFieldError('email') && styles.inputError]}
          value={formData.email}
          onChangeText={(value) => updateFormData('email', value)}
          placeholder="Enter your email address"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
          keyboardType="email-address"
          autoComplete="email"
          textContentType="emailAddress"
          autoCapitalize="none"
        />
        {getFieldError('email') && (
          <Text style={styles.errorText}>{getFieldError('email')}</Text>
        )}
      </View>

      {/* Gender */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Gender</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={formData.gender}
            onValueChange={(value) => updateFormData('gender', value)}
            style={styles.picker}
            dropdownIconColor="white"
          >
            <Picker.Item label="Select Gender" value="" />
            <Picker.Item label="Male" value="male" />
            <Picker.Item label="Female" value="female" />
            <Picker.Item label="Other" value="other" />
          </Picker>
        </View>
      </View>
    </>
  )

  const renderVendorFields = () => (
    <>
      {/* Business Name */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Business Name *</Text>
        <TextInput
          style={[styles.input, getFieldError('businessName') && styles.inputError]}
          value={formData.businessName}
          onChangeText={(value) => updateFormData('businessName', value)}
          placeholder="Enter your business name"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
        />
        {getFieldError('businessName') && (
          <Text style={styles.errorText}>{getFieldError('businessName')}</Text>
        )}
      </View>

      {/* Business Type */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Business Type *</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={formData.businessType}
            onValueChange={(value) => updateFormData('businessType', value)}
            style={styles.picker}
            dropdownIconColor="white"
          >
            <Picker.Item label="Restaurant" value="restaurant" />
            <Picker.Item label="Fast Food" value="fast_food" />
            <Picker.Item label="Grocery Store" value="grocery" />
            <Picker.Item label="Pharmacy" value="pharmacy" />
            <Picker.Item label="Other" value="other" />
          </Picker>
        </View>
      </View>

      {/* Business Address */}
      <Text style={styles.sectionTitle}>Business Address *</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Street Address</Text>
        <TextInput
          style={[styles.input, getFieldError('address') && styles.inputError]}
          value={formData.address.street}
          onChangeText={(value) => updateNestedFormData('address', 'street', value)}
          placeholder="Enter street address"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, styles.flex1]}>
          <Text style={styles.label}>City</Text>
          <TextInput
            style={styles.input}
            value={formData.address.city}
            onChangeText={(value) => updateNestedFormData('address', 'city', value)}
            placeholder="City"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
          />
        </View>

        <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
          <Text style={styles.label}>Postal Code</Text>
          <TextInput
            style={styles.input}
            value={formData.address.postalCode}
            onChangeText={(value) => updateNestedFormData('address', 'postalCode', value)}
            placeholder="0000"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
            keyboardType="numeric"
            maxLength={4}
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Province</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={formData.address.province}
            onValueChange={(value) => updateNestedFormData('address', 'province', value)}
            style={styles.picker}
            dropdownIconColor="white"
          >
            {SA_PROVINCES.map((province) => (
              <Picker.Item
                key={province.code}
                label={province.name}
                value={province.name}
              />
            ))}
          </Picker>
        </View>
      </View>
    </>
  )

  const renderRunnerFields = () => (
    <>
      {/* Vehicle Type */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Vehicle Type *</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={formData.vehicleType}
            onValueChange={(value) => updateFormData('vehicleType', value)}
            style={styles.picker}
            dropdownIconColor="white"
          >
            <Picker.Item label="Bicycle" value="bicycle" />
            <Picker.Item label="Motorcycle" value="motorcycle" />
            <Picker.Item label="Car" value="car" />
            <Picker.Item label="Scooter" value="scooter" />
          </Picker>
        </View>
      </View>

      {/* Vehicle Details */}
      <Text style={styles.sectionTitle}>Vehicle Details</Text>
      
      <View style={styles.row}>
        <View style={[styles.inputGroup, styles.flex1]}>
          <Text style={styles.label}>Make</Text>
          <TextInput
            style={styles.input}
            value={formData.vehicleDetails.make}
            onChangeText={(value) => updateNestedFormData('vehicleDetails', 'make', value)}
            placeholder="Toyota"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
          />
        </View>

        <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
          <Text style={styles.label}>Model</Text>
          <TextInput
            style={styles.input}
            value={formData.vehicleDetails.model}
            onChangeText={(value) => updateNestedFormData('vehicleDetails', 'model', value)}
            placeholder="Corolla"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
          />
        </View>
      </View>

      {/* Emergency Contact */}
      <Text style={styles.sectionTitle}>Emergency Contact *</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Contact Name</Text>
        <TextInput
          style={[styles.input, getFieldError('emergencyContact') && styles.inputError]}
          value={formData.emergencyContact.name}
          onChangeText={(value) => updateNestedFormData('emergencyContact', 'name', value)}
          placeholder="Emergency contact name"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Contact Phone</Text>
        <TextInput
          style={styles.input}
          value={formData.emergencyContact.phone}
          onChangeText={(value) => updateNestedFormData('emergencyContact', 'phone', value)}
          placeholder="************"
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
          keyboardType="phone-pad"
        />
      </View>
    </>
  )

  const getRoleTitle = () => {
    switch (role) {
      case 'vendor': return 'Vendor Registration'
      case 'runner': return 'Runner Registration'
      default: return 'Complete Your Profile'
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[primaryColor, secondaryColor]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            {onBack && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                activeOpacity={0.7}
              >
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>
            )}
            
            <Text style={styles.title}>{getRoleTitle()}</Text>
            <Text style={styles.subtitle}>
              Please fill in your details to complete registration
            </Text>
          </View>

          {/* Form */}
          <ScrollView
            ref={scrollViewRef}
            style={styles.formContainer}
            contentContainerStyle={styles.formContent}
            showsVerticalScrollIndicator={false}
          >
            {renderBasicFields()}
            
            {role === 'vendor' && renderVendorFields()}
            {role === 'runner' && renderRunnerFields()}

            {/* Submit Button */}
            <TouchableOpacity
              style={[styles.submitButton, isLoading && styles.submitButtonLoading]}
              onPress={handleSubmit}
              disabled={isLoading}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Text style={styles.submitButtonText}>Complete Registration</Text>
                  <Ionicons name="checkmark" size={20} color="white" />
                </>
              )}
            </TouchableOpacity>

            {/* Terms */}
            <Text style={styles.termsText}>
              By registering, you agree to our{' '}
              <Text style={styles.termsLink}>Terms of Service</Text>
              {' '}and{' '}
              <Text style={styles.termsLink}>Privacy Policy</Text>
            </Text>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardAvoid: {
    flex: 1
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 20
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center'
  },
  formContainer: {
    flex: 1
  },
  formContent: {
    paddingHorizontal: 20,
    paddingBottom: 40
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginTop: 24,
    marginBottom: 16
  },
  inputGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
    fontWeight: '500'
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 12,
    fontSize: 16,
    color: 'white',
    borderWidth: 1,
    borderColor: 'transparent'
  },
  inputError: {
    borderColor: '#FF4757'
  },
  errorText: {
    fontSize: 12,
    color: '#FF4757',
    marginTop: 4
  },
  pickerContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    overflow: 'hidden'
  },
  picker: {
    color: 'white',
    backgroundColor: 'transparent'
  },
  row: {
    flexDirection: 'row'
  },
  flex1: {
    flex: 1
  },
  marginLeft: {
    marginLeft: 12
  },
  submitButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 18,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 32,
    marginBottom: 24
  },
  submitButtonLoading: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)'
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8
  },
  termsText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 18
  },
  termsLink: {
    textDecorationLine: 'underline',
    fontWeight: '600'
  }
})

export default UserRegistrationScreen
