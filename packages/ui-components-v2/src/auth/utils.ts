/**
 * Authentication Utilities for HVPPYPlug+ Apps
 */

import { SAPhoneNumber, SA_PHONE_PROVIDERS, ValidationError, UserRole } from './types'

/**
 * Validates South African phone numbers
 */
export const validateSAPhoneNumber = (phone: string): SAPhoneNumber => {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '')
  
  let formattedPhone = cleanPhone
  let isValid = false
  let provider = ''
  let number = ''

  // Handle different input formats
  if (cleanPhone.startsWith('27')) {
    // International format: 27821234567
    const localPart = cleanPhone.substring(2)
    if (localPart.length === 9) {
      provider = localPart.substring(0, 3)
      number = localPart.substring(3)
      formattedPhone = `+27 ${provider} ${number.substring(0, 3)} ${number.substring(3)}`
      isValid = SA_PHONE_PROVIDERS.includes(provider as any)
    }
  } else if (cleanPhone.startsWith('0')) {
    // National format: **********
    if (cleanPhone.length === 10) {
      provider = cleanPhone.substring(1, 4)
      number = cleanPhone.substring(4)
      formattedPhone = `+27 ${provider} ${number.substring(0, 3)} ${number.substring(3)}`
      isValid = SA_PHONE_PROVIDERS.includes(provider as any)
    }
  } else if (cleanPhone.length === 9) {
    // Local format: 821234567
    provider = cleanPhone.substring(0, 3)
    number = cleanPhone.substring(3)
    formattedPhone = `+27 ${provider} ${number.substring(0, 3)} ${number.substring(3)}`
    isValid = SA_PHONE_PROVIDERS.includes(provider as any)
  }

  return {
    countryCode: '+27',
    provider: provider as any,
    number,
    formatted: formattedPhone,
    isValid
  }
}

/**
 * Formats phone number for display
 */
export const formatPhoneNumber = (phone: string): string => {
  const validated = validateSAPhoneNumber(phone)
  return validated.formatted
}

/**
 * Converts phone number to international format for API calls
 */
export const toInternationalFormat = (phone: string): string => {
  const validated = validateSAPhoneNumber(phone)
  if (!validated.isValid) return phone
  
  return `+27${validated.provider}${validated.number}`
}

/**
 * Validates email address
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validates password strength
 */
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates OTP code
 */
export const validateOTP = (code: string): boolean => {
  const cleanCode = code.replace(/\D/g, '')
  return cleanCode.length === 6 && /^\d{6}$/.test(cleanCode)
}

/**
 * Validates user registration data
 */
export const validateUserRegistration = (data: any, role: UserRole): ValidationError[] => {
  const errors: ValidationError[] = []
  
  // Name validation
  if (!data.name || data.name.trim().length < 2) {
    errors.push({ field: 'name', message: 'Name must be at least 2 characters long' })
  }
  
  // Email validation
  if (!data.email || !validateEmail(data.email)) {
    errors.push({ field: 'email', message: 'Please enter a valid email address' })
  }
  
  // Phone validation
  const phoneValidation = validateSAPhoneNumber(data.phone || '')
  if (!phoneValidation.isValid) {
    errors.push({ field: 'phone', message: 'Please enter a valid South African phone number' })
  }
  
  // Password validation (if provided)
  if (data.password) {
    const passwordValidation = validatePassword(data.password)
    if (!passwordValidation.isValid) {
      passwordValidation.errors.forEach(error => {
        errors.push({ field: 'password', message: error })
      })
    }
  }
  
  // Role-specific validations
  if (role === 'vendor') {
    if (!data.businessName || data.businessName.trim().length < 2) {
      errors.push({ field: 'businessName', message: 'Business name is required' })
    }
    
    if (!data.businessType) {
      errors.push({ field: 'businessType', message: 'Business type is required' })
    }
    
    if (!data.address || !data.address.street) {
      errors.push({ field: 'address', message: 'Business address is required' })
    }
  }
  
  if (role === 'runner') {
    if (!data.vehicleType) {
      errors.push({ field: 'vehicleType', message: 'Vehicle type is required' })
    }
    
    if (!data.workingAreas || data.workingAreas.length === 0) {
      errors.push({ field: 'workingAreas', message: 'At least one working area is required' })
    }
    
    if (!data.emergencyContact || !data.emergencyContact.name || !data.emergencyContact.phone) {
      errors.push({ field: 'emergencyContact', message: 'Emergency contact is required' })
    }
  }
  
  return errors
}

/**
 * Generates a random OTP code
 */
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

/**
 * Checks if OTP is expired
 */
export const isOTPExpired = (expiresAt: string): boolean => {
  return new Date() > new Date(expiresAt)
}

/**
 * Formats time remaining for OTP
 */
export const formatOTPTimeRemaining = (expiresAt: string): string => {
  const now = new Date()
  const expiry = new Date(expiresAt)
  const diff = expiry.getTime() - now.getTime()
  
  if (diff <= 0) return '00:00'
  
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * Masks phone number for display
 */
export const maskPhoneNumber = (phone: string): string => {
  const validated = validateSAPhoneNumber(phone)
  if (!validated.isValid) return phone
  
  const { provider, number } = validated
  const maskedNumber = number.substring(0, 2) + '***' + number.substring(5)
  return `+27 ${provider} ${maskedNumber}`
}

/**
 * Gets user role display name
 */
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames = {
    customer: 'Customer',
    vendor: 'Vendor',
    runner: 'Delivery Runner',
    admin: 'Administrator'
  }
  
  return roleNames[role] || role
}

/**
 * Gets user role description
 */
export const getRoleDescription = (role: UserRole): string => {
  const descriptions = {
    customer: 'Order food and services from local vendors',
    vendor: 'Sell your products and services to customers',
    runner: 'Deliver orders and earn money on your schedule',
    admin: 'Manage the platform and users'
  }
  
  return descriptions[role] || ''
}

/**
 * Validates South African ID number (basic validation)
 */
export const validateSAIDNumber = (idNumber: string): boolean => {
  const cleanId = idNumber.replace(/\D/g, '')
  
  // Must be 13 digits
  if (cleanId.length !== 13) return false
  
  // Basic format validation (YYMMDD + 4 digits + citizenship + A + checksum)
  const year = parseInt(cleanId.substring(0, 2))
  const month = parseInt(cleanId.substring(2, 4))
  const day = parseInt(cleanId.substring(4, 6))
  
  // Validate date components
  if (month < 1 || month > 12) return false
  if (day < 1 || day > 31) return false
  
  return true
}

/**
 * Formats currency for South African Rand
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount)
}

/**
 * Debounce function for form validation
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Creates a delay for better UX
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Sanitizes user input
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '')
}

/**
 * Generates a secure random string
 */
export const generateSecureId = (length: number = 16): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}
