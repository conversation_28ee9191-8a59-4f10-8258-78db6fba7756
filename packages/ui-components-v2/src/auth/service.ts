/**
 * Authentication Service with Direct Appwrite SDK Integration
 * HVPPYPlug+ Authentication System
 */

import { Client, Account, Databases, ID, Query } from 'appwrite'
import {
  AuthUser,
  AuthSession,
  OTPCode,
  UserRegistrationData,
  VendorRegistrationData,
  RunnerRegistrationData,
  LoginData,
  PasswordResetData,
  UserRole
} from './types'
import { toInternationalFormat, generateOTP, validateSAPhoneNumber } from './utils'

// Appwrite configuration
const APPWRITE_CONFIG = {
  endpoint: 'https://fra.cloud.appwrite.io/v1',
  projectId: '6880d655003a8926a438',
  databaseId: 'hvppyplug-main'
}

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(APPWRITE_CONFIG.endpoint)
  .setProject(APPWRITE_CONFIG.projectId)

const account = new Account(client)
const databases = new Databases(client)

// Collection IDs
const COLLECTIONS = {
  USERS: 'users',
  VENDORS: 'vendors',
  RUNNERS: 'runner-profiles',
  ADDRESSES: 'addresses',
  OTP_CODES: 'otp-codes'
} as const

/**
 * Authentication Service Class
 */
export class AuthService {
  /**
   * Send OTP to phone number
   */
  async sendOTP(phone: string, type: 'verification' | 'login' | 'password_reset' = 'verification'): Promise<void> {
    try {
      const phoneValidation = validateSAPhoneNumber(phone)
      if (!phoneValidation.isValid) {
        throw new Error('Invalid phone number format')
      }

      const internationalPhone = toInternationalFormat(phone)
      const otpCode = generateOTP()
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

      // Store OTP in database
      await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.OTP_CODES,
        ID.unique(),
        {
          phone: internationalPhone,
          code: otpCode,
          type,
          isUsed: false,
          expiresAt: expiresAt.toISOString(),
          createdAt: new Date().toISOString()
        }
      )

      // In production, integrate with SMS service (Twilio, AWS SNS, etc.)
      console.log(`OTP sent to ${internationalPhone}: ${otpCode}`)
      
      // For development, you might want to show the OTP in console
      if (__DEV__) {
        console.log(`🔐 OTP for ${internationalPhone}: ${otpCode}`)
      }
    } catch (error: any) {
      console.error('Send OTP error:', error)
      throw new Error(error.message || 'Failed to send OTP')
    }
  }

  /**
   * Verify OTP code
   */
  async verifyOTP(phone: string, code: string): Promise<OTPCode> {
    try {
      const internationalPhone = toInternationalFormat(phone)
      
      // Find valid OTP
      const otpQuery = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.OTP_CODES,
        [
          Query.equal('phone', internationalPhone),
          Query.equal('code', code),
          Query.equal('isUsed', false),
          Query.orderDesc('createdAt'),
          Query.limit(1)
        ]
      )

      if (otpQuery.documents.length === 0) {
        throw new Error('Invalid or expired OTP code')
      }

      const otpDoc = otpQuery.documents[0]
      
      // Check if OTP is expired
      if (new Date() > new Date(otpDoc.expiresAt)) {
        throw new Error('OTP code has expired')
      }

      // Mark OTP as used
      await databases.updateDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.OTP_CODES,
        otpDoc.$id,
        { isUsed: true }
      )

      return {
        $id: otpDoc.$id,
        phone: otpDoc.phone,
        code: otpDoc.code,
        type: otpDoc.type,
        isUsed: otpDoc.isUsed,
        expiresAt: otpDoc.expiresAt,
        createdAt: otpDoc.createdAt
      } as OTPCode
    } catch (error: any) {
      console.error('Verify OTP error:', error)
      throw new Error(error.message || 'OTP verification failed')
    }
  }

  /**
   * Register new user
   */
  async registerUser(data: UserRegistrationData): Promise<AuthUser> {
    try {
      const internationalPhone = toInternationalFormat(data.phone)
      
      // Create Appwrite account
      const appwriteUser = await account.create(
        ID.unique(),
        data.email,
        data.password || generateOTP() + 'Temp!', // Temporary password if not provided
        data.name
      )

      // Create user document in database
      const userDoc = await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        appwriteUser.$id,
        {
          name: data.name,
          email: data.email,
          phone: internationalPhone,
          role: data.role,
          status: 'active',
          isVerified: true, // Since they verified via OTP
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      )

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        phone: userDoc.phone,
        role: userDoc.role,
        status: userDoc.status,
        isVerified: userDoc.isVerified,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt
      } as AuthUser
    } catch (error: any) {
      console.error('Register user error:', error)
      throw new Error(error.message || 'Registration failed')
    }
  }

  /**
   * Register vendor with business details
   */
  async registerVendor(data: VendorRegistrationData): Promise<AuthUser> {
    try {
      // First create the user
      const user = await this.registerUser(data)

      // Create vendor profile
      await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.VENDORS,
        ID.unique(),
        {
          name: data.businessName,
          slug: data.businessName.toLowerCase().replace(/\s+/g, '-'),
          ownerId: user.$id,
          businessType: data.businessType,
          location: `${data.address.street}, ${data.address.city}, ${data.address.province}`,
          status: 'pending', // Requires approval
          rating: 0,
          reviewCount: 0,
          deliveryFee: 25.00, // Default delivery fee
          isActive: false, // Inactive until approved
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      )

      // Create business address
      await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.ADDRESSES,
        ID.unique(),
        {
          userId: user.$id,
          type: 'work',
          street: data.address.street,
          city: data.address.city,
          province: data.address.province,
          postalCode: data.address.postalCode,
          isDefault: true
        }
      )

      return user
    } catch (error: any) {
      console.error('Register vendor error:', error)
      throw new Error(error.message || 'Vendor registration failed')
    }
  }

  /**
   * Register runner with vehicle details
   */
  async registerRunner(data: RunnerRegistrationData): Promise<AuthUser> {
    try {
      // First create the user
      const user = await this.registerUser(data)

      // Create runner profile
      await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.RUNNERS,
        ID.unique(),
        {
          userId: user.$id,
          vehicleType: data.vehicleType,
          isAvailable: false, // Inactive until verified
          isOnline: false,
          rating: 0,
          totalDeliveries: 0,
          verificationStatus: 'pending',
          createdAt: new Date().toISOString()
        }
      )

      return user
    } catch (error: any) {
      console.error('Register runner error:', error)
      throw new Error(error.message || 'Runner registration failed')
    }
  }

  /**
   * Login user with phone and password
   */
  async login(data: LoginData): Promise<{ user: AuthUser; session: AuthSession }> {
    try {
      const internationalPhone = toInternationalFormat(data.phone)
      
      // Find user by phone
      const userQuery = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        [
          Query.equal('phone', internationalPhone),
          Query.limit(1)
        ]
      )

      if (userQuery.documents.length === 0) {
        throw new Error('User not found')
      }

      const userDoc = userQuery.documents[0]

      const user: AuthUser = {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        phone: userDoc.phone,
        role: userDoc.role,
        status: userDoc.status,
        isVerified: userDoc.isVerified,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt
      }

      // Create session with Appwrite
      const session = await account.createSession(user.email, data.password || '')

      return {
        user,
        session: session as AuthSession
      }
    } catch (error: any) {
      console.error('Login error:', error)
      throw new Error(error.message || 'Login failed')
    }
  }

  /**
   * Login with OTP (passwordless)
   */
  async loginWithOTP(phone: string, code: string): Promise<{ user: AuthUser; session: AuthSession }> {
    try {
      // Verify OTP first
      await this.verifyOTP(phone, code)
      
      const internationalPhone = toInternationalFormat(phone)
      
      // Find user by phone
      const userQuery = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        [
          Query.equal('phone', internationalPhone),
          Query.limit(1)
        ]
      )

      if (userQuery.documents.length === 0) {
        throw new Error('User not found')
      }

      const userDoc = userQuery.documents[0]
      
      const user: AuthUser = {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        phone: userDoc.phone,
        role: userDoc.role,
        status: userDoc.status,
        isVerified: userDoc.isVerified,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt
      }

      // Create anonymous session and then convert to user session
      // This is a workaround for OTP-based login
      const session = await account.createAnonymousSession()

      return {
        user,
        session: session as AuthSession
      }
    } catch (error: any) {
      console.error('OTP login error:', error)
      throw new Error(error.message || 'OTP login failed')
    }
  }

  /**
   * Logout current session
   */
  async logout(): Promise<void> {
    try {
      await account.deleteSession('current')
    } catch (error: any) {
      console.error('Logout error:', error)
      throw new Error(error.message || 'Logout failed')
    }
  }

  /**
   * Logout all sessions
   */
  async logoutAll(): Promise<void> {
    try {
      await account.deleteSessions()
    } catch (error: any) {
      console.error('Logout all error:', error)
      throw new Error(error.message || 'Logout failed')
    }
  }

  /**
   * Reset password with OTP
   */
  async resetPassword(data: PasswordResetData): Promise<void> {
    try {
      // Verify OTP first
      await this.verifyOTP(data.phone, data.code)
      
      const internationalPhone = toInternationalFormat(data.phone)
      
      // Find user by phone
      const userQuery = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        [
          Query.equal('phone', internationalPhone),
          Query.limit(1)
        ]
      )

      if (userQuery.documents.length === 0) {
        throw new Error('User not found')
      }

      const userDoc = userQuery.documents[0]
      
      const user: AuthUser = {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        phone: userDoc.phone,
        role: userDoc.role,
        status: userDoc.status,
        isVerified: userDoc.isVerified,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt
      }

      // Update password (this requires the user to be logged in)
      // In a real implementation, you'd need to handle this differently
      await account.updatePassword(data.newPassword)
    } catch (error: any) {
      console.error('Reset password error:', error)
      throw new Error(error.message || 'Password reset failed')
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const appwriteUser = await account.get()
      
      // Get user document from database
      const userDoc = await databases.getDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        appwriteUser.$id
      )

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        phone: userDoc.phone,
        role: userDoc.role,
        status: userDoc.status,
        isVerified: userDoc.isVerified,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt
      } as AuthUser
    } catch (error: any) {
      console.error('Get current user error:', error)
      return null
    }
  }

  /**
   * Get current session
   */
  async getCurrentSession(): Promise<AuthSession | null> {
    try {
      const session = await account.getSession('current')
      return session as AuthSession
    } catch (error: any) {
      console.error('Get current session error:', error)
      return null
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, updates: Partial<AuthUser>): Promise<AuthUser> {
    try {
      const updatedDoc = await databases.updateDocument(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        userId,
        {
          ...updates,
          updatedAt: new Date().toISOString()
        }
      )

      return {
        $id: updatedDoc.$id,
        name: updatedDoc.name,
        email: updatedDoc.email,
        phone: updatedDoc.phone,
        role: updatedDoc.role,
        status: updatedDoc.status,
        isVerified: updatedDoc.isVerified,
        createdAt: updatedDoc.createdAt,
        updatedAt: updatedDoc.updatedAt
      } as AuthUser
    } catch (error: any) {
      console.error('Update profile error:', error)
      throw new Error(error.message || 'Profile update failed')
    }
  }

  /**
   * Check if user exists by phone
   */
  async userExistsByPhone(phone: string): Promise<boolean> {
    try {
      const internationalPhone = toInternationalFormat(phone)

      const userQuery = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        COLLECTIONS.USERS,
        [
          Query.equal('phone', internationalPhone),
          Query.limit(1)
        ]
      )

      return userQuery.documents.length > 0
    } catch (error: any) {
      console.error('Check user exists error:', error)
      return false
    }
  }

  /**
   * Refresh user session
   */
  async refreshSession(): Promise<AuthSession> {
    try {
      const session = await account.getSession('current')
      return session as AuthSession
    } catch (error: any) {
      console.error('Refresh session error:', error)
      throw new Error(error.message || 'Session refresh failed')
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      await account.get()
      return true
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const authService = new AuthService()
export default authService
