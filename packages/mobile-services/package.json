{"name": "@hvppyplug/mobile-services", "version": "1.0.0", "description": "Mobile services and hooks for HVPPYPlug+ Expo applications", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "tsup", "dev": "tsup --watch", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "clean": "rm -rf dist"}, "dependencies": {"expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-camera": "~16.1.11", "expo-image-picker": "~16.1.3", "expo-secure-store": "~14.1.1", "expo-local-authentication": "~16.0.5", "expo-task-manager": "~13.1.6", "expo-background-fetch": "~13.1.1", "expo-device": "~7.1.1", "expo-application": "~6.1.1", "expo-constants": "~17.1.2", "expo-network": "~7.1.1", "expo-file-system": "~18.1.1", "expo-haptics": "~14.1.1", "expo-linking": "~7.1.2", "@react-native-async-storage/async-storage": "^2.1.0", "appwrite": "^16.0.2", "react": "19.0.0", "react-native": "0.79.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "tsup": "^8.0.1"}, "peerDependencies": {"react": ">=18.0.0", "react-native": ">=0.70.0"}, "keywords": ["expo", "react-native", "mobile-services", "location", "notifications", "camera", "appwrite", "hvppyplug"], "author": "HVPPYPlug Team", "license": "MIT", "files": ["dist", "src", "README.md"], "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}}