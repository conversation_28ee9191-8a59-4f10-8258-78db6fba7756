"use client"

import * as Notifications from 'expo-notifications'
import * as Device from 'expo-device'
import { Platform } from 'react-native'

// Types
export interface NotificationData {
  title: string
  body: string
  data?: Record<string, any>
  sound?: string
  badge?: number
  categoryId?: string
  priority?: 'low' | 'normal' | 'high' | 'max'
}

export interface NotificationServiceConfig {
  onNotificationReceived?: (notification: Notifications.Notification) => void
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void
  onTokenReceived?: (token: string) => void
  onError?: (error: Error) => void
}

/**
 * NotificationService - Wrapper for expo-notifications with Appwrite integration
 * 
 * Features:
 * - Push notification handling
 * - Local notification scheduling
 * - Token management for Appwrite
 * - Notification categories and actions
 * - Background notification handling
 * - Deep linking from notifications
 */
export class NotificationService {
  private static instance: NotificationService
  private config: NotificationServiceConfig = {}
  private expoPushToken: string | null = null

  private constructor() {
    this.setupNotificationHandlers()
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * Initialize the notification service
   */
  async initialize(config: NotificationServiceConfig): Promise<void> {
    this.config = { ...this.config, ...config }

    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    })

    // Setup notification categories
    await this.setupNotificationCategories()

    // Request permissions and get token
    await this.requestPermissions()
    await this.registerForPushNotifications()
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices')
        return false
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync()
        finalStatus = status
      }

      if (finalStatus !== 'granted') {
        throw new Error('Notification permission not granted')
      }

      return true
    } catch (error) {
      this.config.onError?.(error as Error)
      return false
    }
  }

  /**
   * Register for push notifications and get token
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        return null
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
      })

      this.expoPushToken = token.data
      this.config.onTokenReceived?.(token.data)

      return token.data
    } catch (error) {
      this.config.onError?.(error as Error)
      return null
    }
  }

  /**
   * Get the current push token
   */
  getPushToken(): string | null {
    return this.expoPushToken
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    notification: NotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound || 'default',
          badge: notification.badge,
          categoryIdentifier: notification.categoryId,
          priority: this.mapPriority(notification.priority),
        },
        trigger: trigger || null,
      })

      return identifier
    } catch (error) {
      this.config.onError?.(error as Error)
      return null
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier)
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync()
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync()
    } catch (error) {
      this.config.onError?.(error as Error)
      return []
    }
  }

  /**
   * Set notification badge count
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count)
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Get notification badge count
   */
  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync()
    } catch (error) {
      this.config.onError?.(error as Error)
      return 0
    }
  }

  /**
   * Clear all delivered notifications
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync()
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Setup notification handlers
   */
  private setupNotificationHandlers(): void {
    // Handle notifications received while app is foregrounded
    Notifications.addNotificationReceivedListener((notification) => {
      this.config.onNotificationReceived?.(notification)
    })

    // Handle notification responses (user tapped notification)
    Notifications.addNotificationResponseReceivedListener((response) => {
      this.config.onNotificationResponse?.(response)
    })
  }

  /**
   * Setup notification categories for different types of notifications
   */
  private async setupNotificationCategories(): Promise<void> {
    try {
      await Notifications.setNotificationCategoryAsync('order', [
        {
          identifier: 'accept',
          buttonTitle: 'Accept Order',
          options: {
            opensAppToForeground: true,
          },
        },
        {
          identifier: 'decline',
          buttonTitle: 'Decline',
          options: {
            opensAppToForeground: false,
          },
        },
      ])

      await Notifications.setNotificationCategoryAsync('delivery', [
        {
          identifier: 'track',
          buttonTitle: 'Track Order',
          options: {
            opensAppToForeground: true,
          },
        },
        {
          identifier: 'call',
          buttonTitle: 'Call Runner',
          options: {
            opensAppToForeground: false,
          },
        },
      ])

      await Notifications.setNotificationCategoryAsync('promotion', [
        {
          identifier: 'view',
          buttonTitle: 'View Offer',
          options: {
            opensAppToForeground: true,
          },
        },
        {
          identifier: 'dismiss',
          buttonTitle: 'Dismiss',
          options: {
            opensAppToForeground: false,
          },
        },
      ])
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Map priority to platform-specific values
   */
  private mapPriority(priority?: string): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'low':
        return Notifications.AndroidNotificationPriority.LOW
      case 'normal':
        return Notifications.AndroidNotificationPriority.DEFAULT
      case 'high':
        return Notifications.AndroidNotificationPriority.HIGH
      case 'max':
        return Notifications.AndroidNotificationPriority.MAX
      default:
        return Notifications.AndroidNotificationPriority.DEFAULT
    }
  }

  /**
   * Create notification for order updates
   */
  async sendOrderNotification(
    orderId: string,
    status: string,
    message: string,
    data?: Record<string, any>
  ): Promise<string | null> {
    const statusMessages = {
      accepted: '✅ Order Accepted',
      preparing: '👨‍🍳 Order Being Prepared',
      ready: '📦 Order Ready for Pickup',
      picked_up: '🚚 Order Picked Up',
      en_route: '🛣️ Order On the Way',
      delivered: '🎉 Order Delivered',
      cancelled: '❌ Order Cancelled',
    }

    return this.scheduleLocalNotification({
      title: statusMessages[status as keyof typeof statusMessages] || 'Order Update',
      body: message,
      data: {
        type: 'order_update',
        orderId,
        status,
        ...data,
      },
      categoryId: 'order',
      priority: 'high',
      sound: 'order-notification.wav',
    })
  }

  /**
   * Create notification for delivery updates
   */
  async sendDeliveryNotification(
    title: string,
    message: string,
    data?: Record<string, any>
  ): Promise<string | null> {
    return this.scheduleLocalNotification({
      title,
      body: message,
      data: {
        type: 'delivery_update',
        ...data,
      },
      categoryId: 'delivery',
      priority: 'high',
    })
  }

  /**
   * Create notification for promotions
   */
  async sendPromotionNotification(
    title: string,
    message: string,
    data?: Record<string, any>
  ): Promise<string | null> {
    return this.scheduleLocalNotification({
      title,
      body: message,
      data: {
        type: 'promotion',
        ...data,
      },
      categoryId: 'promotion',
      priority: 'normal',
    })
  }
}
