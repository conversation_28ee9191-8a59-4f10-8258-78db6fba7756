"use client"

import { Client, Account, Databases, Storage, Functions } from 'appwrite'
import { LocationData } from '../LocationService'
import { ImageResult } from '../CameraService'

// Types
export interface AppwriteConfig {
  endpoint: string
  projectId: string
  databaseId: string
  storageId: string
}

export interface UserLocation {
  userId: string
  latitude: number
  longitude: number
  timestamp: number
  address?: string
  accuracy?: number
  heading?: number
  speed?: number
}

export interface NotificationToken {
  userId: string
  token: string
  platform: 'ios' | 'android' | 'web'
  deviceId: string
}

/**
 * AppwriteService - Integration service for Appwrite with Expo plugins
 * 
 * Features:
 * - Real-time location tracking with Appwrite Realtime
 * - Push notification token management
 * - Image upload to Appwrite Storage
 * - User authentication integration
 * - Real-time order updates
 * - Background sync capabilities
 */
export class AppwriteService {
  private static instance: AppwriteService
  private client: Client
  private account: Account
  private databases: Databases
  private storage: Storage
  // private realtime: Realtime
  private functions: Functions
  private config: AppwriteConfig

  private constructor(config: AppwriteConfig) {
    this.config = config
    this.client = new Client()
      .setEndpoint(config.endpoint)
      .setProject(config.projectId)

    this.account = new Account(this.client)
    this.databases = new Databases(this.client)
    this.storage = new Storage(this.client)
    // this.realtime = new Realtime(this.client)
    this.functions = new Functions(this.client)
  }

  static getInstance(config?: AppwriteConfig): AppwriteService {
    if (!AppwriteService.instance) {
      if (!config) {
        throw new Error('AppwriteService must be initialized with config')
      }
      AppwriteService.instance = new AppwriteService(config)
    }
    return AppwriteService.instance
  }

  /**
   * Initialize Appwrite service
   */
  async initialize(): Promise<void> {
    try {
      // Check if user is authenticated
      await this.getCurrentUser()
    } catch (error) {
      console.log('User not authenticated')
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser() {
    return await this.account.get()
  }

  /**
   * Update user location in real-time
   */
  async updateUserLocation(locationData: LocationData): Promise<void> {
    try {
      const user = await this.getCurrentUser()
      
      const userLocation: UserLocation = {
        userId: user.$id,
        latitude: locationData.coords.latitude,
        longitude: locationData.coords.longitude,
        timestamp: locationData.timestamp,
        address: locationData.address,
        accuracy: locationData.coords.accuracy,
        heading: locationData.coords.heading,
        speed: locationData.coords.speed,
      }

      // Update location in database
      await this.databases.createDocument(
        this.config.databaseId,
        'user_locations',
        'unique()',
        userLocation
      )

      // Broadcast location update via Realtime
      await this.broadcastLocationUpdate(userLocation)
    } catch (error) {
      console.error('Failed to update user location:', error)
      throw error
    }
  }

  /**
   * Subscribe to real-time location updates
   * Note: Realtime functionality requires additional setup
   */
  subscribeToLocationUpdates(
    userId: string,
    callback: (location: UserLocation) => void
  ): () => void {
    // TODO: Implement realtime subscription when Realtime is properly configured
    console.warn('Realtime subscriptions not yet implemented')
    return () => {}
  }

  /**
   * Store push notification token
   */
  async storePushToken(token: string, platform: 'ios' | 'android' | 'web'): Promise<void> {
    try {
      const user = await this.getCurrentUser()
      
      const tokenData: NotificationToken = {
        userId: user.$id,
        token,
        platform,
        deviceId: `${platform}_${user.$id}_${Date.now()}`,
      }

      // Store token in database
      await this.databases.createDocument(
        this.config.databaseId,
        'notification_tokens',
        'unique()',
        tokenData
      )
    } catch (error) {
      console.error('Failed to store push token:', error)
      throw error
    }
  }

  /**
   * Upload image to Appwrite Storage
   */
  async uploadImage(
    imageResult: ImageResult,
    bucketId: string = this.config.storageId,
    fileId?: string
  ): Promise<string> {
    try {
      // Convert image to File object for upload
      const response = await fetch(imageResult.uri)
      const blob = await response.blob()
      
      const file = new File([blob], imageResult.fileName || 'image.jpg', {
        type: imageResult.type || 'image/jpeg',
      })

      // Upload to Appwrite Storage
      const uploadResult = await this.storage.createFile(
        bucketId,
        fileId || 'unique()',
        file
      )

      // Return the file URL
      return this.storage.getFileView(bucketId, uploadResult.$id)
    } catch (error) {
      console.error('Failed to upload image:', error)
      throw error
    }
  }

  /**
   * Upload multiple images
   */
  async uploadImages(
    images: ImageResult[],
    bucketId: string = this.config.storageId
  ): Promise<string[]> {
    const uploadPromises = images.map((image) =>
      this.uploadImage(image, bucketId)
    )

    return await Promise.all(uploadPromises)
  }

  /**
   * Delete image from storage
   */
  async deleteImage(fileId: string, bucketId: string = this.config.storageId): Promise<void> {
    try {
      await this.storage.deleteFile(bucketId, fileId)
    } catch (error) {
      console.error('Failed to delete image:', error)
      throw error
    }
  }

  /**
   * Send push notification via Appwrite Functions
   */
  async sendPushNotification(
    userId: string,
    title: string,
    body: string,
    data?: Record<string, any>
  ): Promise<void> {
    try {
      await this.functions.createExecution(
        'send-push-notification',
        JSON.stringify({
          userId,
          title,
          body,
          data,
        })
      )
    } catch (error) {
      console.error('Failed to send push notification:', error)
      throw error
    }
  }

  /**
   * Subscribe to order updates
   * Note: Realtime functionality requires additional setup
   */
  subscribeToOrderUpdates(
    orderId: string,
    callback: (order: any) => void
  ): () => void {
    // TODO: Implement realtime subscription when Realtime is properly configured
    console.warn('Realtime subscriptions not yet implemented')
    return () => {}
  }

  /**
   * Subscribe to runner location updates for order tracking
   */
  subscribeToRunnerLocation(
    runnerId: string,
    callback: (location: UserLocation) => void
  ): () => void {
    return this.subscribeToLocationUpdates(runnerId, callback)
  }

  /**
   * Get user's notification tokens
   */
  async getUserNotificationTokens(userId: string): Promise<NotificationToken[]> {
    try {
      const response = await this.databases.listDocuments(
        this.config.databaseId,
        'notification_tokens',
        [
          `userId=${userId}`,
        ]
      )

      return response.documents.map((doc: any) => ({
        userId: doc.userId,
        token: doc.token,
        platform: doc.platform,
        deviceId: doc.deviceId,
      })) as NotificationToken[]
    } catch (error) {
      console.error('Failed to get notification tokens:', error)
      return []
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(preferences: Record<string, any>): Promise<void> {
    try {
      const user = await this.getCurrentUser()
      
      await this.databases.updateDocument(
        this.config.databaseId,
        'users',
        user.$id,
        { preferences }
      )
    } catch (error) {
      console.error('Failed to update user preferences:', error)
      throw error
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(): Promise<Record<string, any>> {
    try {
      const user = await this.getCurrentUser()
      
      const userDoc = await this.databases.getDocument(
        this.config.databaseId,
        'users',
        user.$id
      )

      return userDoc.preferences || {}
    } catch (error) {
      console.error('Failed to get user preferences:', error)
      return {}
    }
  }

  /**
   * Broadcast location update via Realtime
   */
  private async broadcastLocationUpdate(location: UserLocation): Promise<void> {
    // This would typically be handled by Appwrite Functions
    // For now, we'll just log the location update
    console.log('Broadcasting location update:', location)
  }

  /**
   * Get Appwrite client instance
   */
  getClient(): Client {
    return this.client
  }

  /**
   * Get Appwrite services
   */
  getServices() {
    return {
      account: this.account,
      databases: this.databases,
      storage: this.storage,
      // realtime: this.realtime,
      functions: this.functions,
    }
  }

  /**
   * Get Appwrite configuration
   */
  getConfig() {
    return this.config
  }
}
