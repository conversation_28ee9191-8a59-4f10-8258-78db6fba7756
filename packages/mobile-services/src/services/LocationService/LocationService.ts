"use client"

import * as Location from 'expo-location'
import * as TaskManager from 'expo-task-manager'
import { Platform } from 'react-native'

// Types
export interface LocationCoordinates {
  latitude: number
  longitude: number
  altitude?: number
  accuracy?: number
  heading?: number
  speed?: number
}

export interface LocationData {
  coords: LocationCoordinates
  timestamp: number
  address?: string
}

export interface LocationServiceConfig {
  enableBackgroundLocation?: boolean
  distanceInterval?: number
  timeInterval?: number
  accuracy?: Location.LocationAccuracy
  onLocationUpdate?: (location: LocationData) => void
  onError?: (error: Error) => void
}

// Background location task name
const BACKGROUND_LOCATION_TASK = 'background-location'

/**
 * LocationService - Wrapper for expo-location with Appwrite integration
 * 
 * Features:
 * - Foreground and background location tracking
 * - Permission management
 * - Real-time location updates
 * - Integration with Appwrite Realtime for live tracking
 * - Geofencing support
 * - Address geocoding
 */
export class LocationService {
  private static instance: LocationService
  private config: LocationServiceConfig = {}
  private isTracking = false
  private watchSubscription: Location.LocationSubscription | null = null

  private constructor() {
    this.setupBackgroundLocationTask()
  }

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService()
    }
    return LocationService.instance
  }

  /**
   * Initialize the location service with configuration
   */
  async initialize(config: LocationServiceConfig): Promise<void> {
    this.config = { ...this.config, ...config }
    
    // Request permissions
    await this.requestPermissions()
  }

  /**
   * Request location permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      // Request foreground location permission
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync()
      
      if (foregroundStatus !== 'granted') {
        throw new Error('Foreground location permission not granted')
      }

      // Request background location permission if needed
      if (this.config.enableBackgroundLocation) {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync()
        
        if (backgroundStatus !== 'granted') {
          console.warn('Background location permission not granted')
          return false
        }
      }

      return true
    } catch (error) {
      this.config.onError?.(error as Error)
      return false
    }
  }

  /**
   * Get current location
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const hasPermission = await this.checkPermissions()
      if (!hasPermission) {
        throw new Error('Location permission not granted')
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: this.config.accuracy || Location.Accuracy.High,
      })

      const locationData: LocationData = {
        coords: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          altitude: location.coords.altitude || undefined,
          accuracy: location.coords.accuracy || undefined,
          heading: location.coords.heading || undefined,
          speed: location.coords.speed || undefined,
        },
        timestamp: location.timestamp,
      }

      // Get address if possible
      try {
        const address = await this.reverseGeocode(
          location.coords.latitude,
          location.coords.longitude
        )
        locationData.address = address || undefined
      } catch (error) {
        console.warn('Failed to get address:', error)
      }

      return locationData
    } catch (error) {
      this.config.onError?.(error as Error)
      return null
    }
  }

  /**
   * Start location tracking
   */
  async startTracking(): Promise<boolean> {
    try {
      if (this.isTracking) {
        return true
      }

      const hasPermission = await this.checkPermissions()
      if (!hasPermission) {
        throw new Error('Location permission not granted')
      }

      // Start foreground location tracking
      this.watchSubscription = await Location.watchPositionAsync(
        {
          accuracy: this.config.accuracy || Location.Accuracy.High,
          timeInterval: this.config.timeInterval || 5000,
          distanceInterval: this.config.distanceInterval || 10,
        },
        (location) => {
          const locationData: LocationData = {
            coords: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              altitude: location.coords.altitude || undefined,
              accuracy: location.coords.accuracy || undefined,
              heading: location.coords.heading || undefined,
              speed: location.coords.speed || undefined,
            },
            timestamp: location.timestamp,
          }

          this.config.onLocationUpdate?.(locationData)
        }
      )

      // Start background location tracking if enabled
      if (this.config.enableBackgroundLocation) {
        await this.startBackgroundLocationUpdates()
      }

      this.isTracking = true
      return true
    } catch (error) {
      this.config.onError?.(error as Error)
      return false
    }
  }

  /**
   * Stop location tracking
   */
  async stopTracking(): Promise<void> {
    try {
      if (this.watchSubscription) {
        this.watchSubscription.remove()
        this.watchSubscription = null
      }

      if (this.config.enableBackgroundLocation) {
        await this.stopBackgroundLocationUpdates()
      }

      this.isTracking = false
    } catch (error) {
      this.config.onError?.(error as Error)
    }
  }

  /**
   * Check if location permissions are granted
   */
  async checkPermissions(): Promise<boolean> {
    const { status } = await Location.getForegroundPermissionsAsync()
    return status === 'granted'
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    try {
      const addresses = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      })

      if (addresses.length > 0) {
        const address = addresses[0]
        return [
          address.streetNumber,
          address.street,
          address.city,
          address.region,
          address.postalCode,
        ]
          .filter(Boolean)
          .join(', ')
      }

      return null
    } catch (error) {
      console.warn('Reverse geocoding failed:', error)
      return null
    }
  }

  /**
   * Forward geocode address to coordinates
   */
  async geocode(address: string): Promise<LocationCoordinates | null> {
    try {
      const locations = await Location.geocodeAsync(address)
      
      if (locations.length > 0) {
        const location = locations[0]
        return {
          latitude: location.latitude,
          longitude: location.longitude,
        }
      }

      return null
    } catch (error) {
      console.warn('Geocoding failed:', error)
      return null
    }
  }

  /**
   * Calculate distance between two coordinates (in meters)
   */
  calculateDistance(
    coord1: LocationCoordinates,
    coord2: LocationCoordinates
  ): number {
    const R = 6371e3 // Earth's radius in meters
    const φ1 = (coord1.latitude * Math.PI) / 180
    const φ2 = (coord2.latitude * Math.PI) / 180
    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180
    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  /**
   * Setup background location task
   */
  private setupBackgroundLocationTask(): void {
    TaskManager.defineTask(BACKGROUND_LOCATION_TASK, async ({ data, error }) => {
      if (error) {
        console.error('Background location task error:', error)
        this.config.onError?.(new Error(error.message || 'Background location task error'))
        return
      }

      if (data) {
        const { locations } = data as any
        const location = locations[0]

        if (location) {
          const locationData: LocationData = {
            coords: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              altitude: location.coords.altitude || undefined,
              accuracy: location.coords.accuracy || undefined,
              heading: location.coords.heading || undefined,
              speed: location.coords.speed || undefined,
            },
            timestamp: location.timestamp,
          }

          this.config.onLocationUpdate?.(locationData)
        }
      }
    })
  }

  /**
   * Start background location updates
   */
  private async startBackgroundLocationUpdates(): Promise<void> {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK)
    
    if (!isRegistered) {
      await Location.startLocationUpdatesAsync(BACKGROUND_LOCATION_TASK, {
        accuracy: this.config.accuracy || Location.Accuracy.High,
        timeInterval: this.config.timeInterval || 10000,
        distanceInterval: this.config.distanceInterval || 50,
        deferredUpdatesInterval: 60000,
        foregroundService: {
          notificationTitle: 'HVPPYPlug+ is tracking your location',
          notificationBody: 'To provide accurate delivery updates',
          notificationColor: '#FF6B35',
        },
      })
    }
  }

  /**
   * Stop background location updates
   */
  private async stopBackgroundLocationUpdates(): Promise<void> {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK)
    
    if (isRegistered) {
      await Location.stopLocationUpdatesAsync(BACKGROUND_LOCATION_TASK)
    }
  }

  /**
   * Get tracking status
   */
  isLocationTracking(): boolean {
    return this.isTracking
  }
}
