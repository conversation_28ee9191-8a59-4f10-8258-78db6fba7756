{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "composite": true, "noEmit": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}