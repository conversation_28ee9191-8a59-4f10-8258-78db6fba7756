/**
 * HVPPYPlug+ Database Type Definitions
 * 
 * Comprehensive TypeScript interfaces for all Appwrite database collections
 * Generated from the enhanced database schema design
 */

// Base types
export type UserRole = 'customer' | 'vendor' | 'runner' | 'admin' | 'support';
export type OrderStatus = 'pending' | 'accepted' | 'preparing' | 'ready' | 'picked_up' | 'en_route' | 'delivered' | 'cancelled';
export type PaymentStatus = 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
export type PaymentMethod = 'card' | 'mpesa' | 'voucher' | 'payfast' | 'cash';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type PromotionType = 'percentage' | 'fixed_amount' | 'free_shipping' | 'buy_one_get_one';
export type DiscountType = 'percentage' | 'fixed_amount';
export type MessageType = 'text' | 'image' | 'file' | 'location' | 'system';
export type ReviewType = 'vendor' | 'runner' | 'item' | 'order';
export type TicketStatus = 'open' | 'in_progress' | 'resolved' | 'closed' | 'cancelled';
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';
export type VehicleType = 'bicycle' | 'motorcycle' | 'car' | 'scooter' | 'walking';
export type VerificationStatus = 'pending' | 'in_review' | 'approved' | 'rejected';

// Core User Management Types

export interface User {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  name: string;
  phone: string;
  email?: string;
  role: UserRole;
  avatarUrl?: string;
  status: 'active' | 'inactive' | 'suspended' | 'banned';
  isVerified: boolean;
  lastActiveAt?: string;
  pushToken?: string;
  preferences?: string; // JSON string
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  userId: string;
  type: 'home' | 'work' | 'other';
  label?: string;
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  coordinates?: string; // JSON string with lat/lng
  instructions?: string;
  isDefault: boolean;
  isActive: boolean;
}

// Business Management Types

export interface Category {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  imageUrl?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  metadata?: string; // JSON string
}

export interface Vendor {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  name: string;
  slug: string;
  description?: string;
  ownerId: string;
  businessType: string;
  categories: string[];
  location: string;
  coordinates?: string; // JSON string with lat/lng
  deliveryRadius?: number;
  status: 'pending' | 'active' | 'inactive' | 'suspended' | 'rejected';
  rating: number;
  reviewCount: number;
  imageUrl?: string;
  coverImageUrl?: string;
  phone?: string;
  email?: string;
  website?: string;
  businessHours?: string; // JSON string
  deliveryFee: number;
  minimumOrder: number;
  isActive: boolean;
  isFeatured: boolean;
  verifiedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MenuItem {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  vendorId: string;
  categoryId?: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  compareAtPrice?: number;
  cost?: number;
  sku?: string;
  barcode?: string;
  images: string[];
  category: string;
  tags: string[];
  variants?: string; // JSON string
  options?: string; // JSON string
  nutritionInfo?: string; // JSON string
  allergens: string[];
  preparationTime?: number;
  inventory?: number;
  trackInventory: boolean;
  available: boolean;
  isActive: boolean;
  isFeatured: boolean;
  sortOrder: number;
  rating: number;
  reviewCount: number;
  salesCount: number;
  createdAt: string;
  updatedAt: string;
}

// Order Management Types

export interface OrderItem {
  itemId: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
  options?: Array<{
    name: string;
    choice: string;
    price?: number;
  }>;
  specialInstructions?: string;
}

export interface DeliveryAddress {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  instructions?: string;
}

export interface Order {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  orderNumber: string;
  customerId: string;
  vendorId: string;
  runnerId?: string;
  items: string; // JSON string of OrderItem[]
  subtotal: number;
  discount: number;
  deliveryFee: number;
  serviceFee: number;
  tax: number;
  tip: number;
  total: number;
  currency: string;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  paymentIntentId?: string;
  deliveryAddress: string; // JSON string of DeliveryAddress
  deliveryInstructions?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  preparationTime?: number;
  notes?: string;
  cancellationReason?: string;
  refundAmount?: number;
  refundReason?: string;
  trackingUpdates?: string; // JSON string
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

// Reviews and Ratings Types

export interface Review {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  orderId: string;
  customerId: string;
  vendorId: string;
  runnerId?: string;
  itemId?: string;
  type: ReviewType;
  rating: number; // 1-5
  title?: string;
  comment?: string;
  images: string[];
  tags: string[];
  isVerified: boolean;
  isPublic: boolean;
  helpfulCount: number;
  reportCount: number;
  status: 'active' | 'hidden' | 'reported' | 'deleted';
  moderatedAt?: string;
  moderatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

// Communication Types

export interface Message {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  orderId?: string;
  type: MessageType;
  content?: string;
  attachments: string[];
  metadata?: string; // JSON string
  isRead: boolean;
  readAt?: string;
  isDelivered: boolean;
  deliveredAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  replyToId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  participants: string[];
  type: 'direct' | 'group' | 'support';
  orderId?: string;
  title?: string;
  lastMessageId?: string;
  lastMessageAt?: string;
  unreadCount?: string; // JSON string with user-specific counts
  isActive: boolean;
  isArchived: boolean;
  archivedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Notification Types

export interface Notification {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  userId: string;
  type: string;
  title: string;
  body: string;
  data?: string; // JSON string
  imageUrl?: string;
  actionUrl?: string;
  category: string;
  priority: NotificationPriority;
  isRead: boolean;
  readAt?: string;
  isSent: boolean;
  sentAt?: string;
  deliveryStatus: 'pending' | 'sent' | 'delivered' | 'failed';
  scheduledFor?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    newMessages: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
    shareLocation: boolean;
  };
  app: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    currency: string;
  };
}

export interface BusinessHours {
  [key: string]: {
    isOpen: boolean;
    openTime?: string;
    closeTime?: string;
    breaks?: Array<{
      startTime: string;
      endTime: string;
    }>;
  };
}

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface TrackingUpdate {
  id: string;
  status: OrderStatus;
  message: string;
  timestamp: string;
  location?: Coordinates;
}

// Parsed JSON types for better type safety
export interface ParsedOrder extends Omit<Order, 'items' | 'deliveryAddress' | 'trackingUpdates'> {
  items: OrderItem[];
  deliveryAddress: DeliveryAddress;
  trackingUpdates?: TrackingUpdate[];
}

export interface ParsedUser extends Omit<User, 'preferences'> {
  preferences?: UserPreferences;
}

export interface ParsedVendor extends Omit<Vendor, 'businessHours' | 'coordinates'> {
  businessHours?: BusinessHours;
  coordinates?: Coordinates;
}

export interface ParsedMenuItem extends Omit<MenuItem, 'variants' | 'options' | 'nutritionInfo'> {
  variants?: Array<{
    name: string;
    options: string[];
    prices?: { [key: string]: number };
  }>;
  options?: Array<{
    name: string;
    type: 'single' | 'multiple';
    required: boolean;
    choices: Array<{
      name: string;
      price?: number;
    }>;
  }>;
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
}

// Marketing and Promotions Types

export interface Promotion {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  code: string;
  name: string;
  description?: string;
  type: PromotionType;
  discountType: DiscountType;
  discountValue: number;
  minimumOrderValue?: number;
  maximumDiscount?: number;
  vendorId?: string;
  categoryIds: string[];
  itemIds: string[];
  userIds: string[];
  usageLimit?: number;
  usageLimitPerUser?: number;
  usageCount: number;
  isActive: boolean;
  isPublic: boolean;
  startsAt: string;
  endsAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface PromotionUsage {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  promotionId: string;
  userId: string;
  orderId: string;
  discountAmount: number;
  originalAmount: number;
  finalAmount: number;
  createdAt: string;
}

// Analytics Types

export interface AnalyticsEvent {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  userId?: string;
  sessionId?: string;
  eventName: string;
  eventCategory: string;
  eventAction: string;
  eventLabel?: string;
  eventValue?: number;
  properties?: string; // JSON string
  userAgent?: string;
  ipAddress?: string;
  platform?: string;
  appVersion?: string;
  deviceId?: string;
  createdAt: string;
}

// Payment Types

export interface Payment {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  orderId: string;
  userId: string;
  vendorId: string;
  paymentMethod: PaymentMethod;
  provider: string;
  providerTransactionId?: string;
  paymentIntentId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  refundedAt?: string;
  metadata?: string; // JSON string
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Runner Management Types

export interface RunnerProfile {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  userId: string;
  vehicleType: VehicleType;
  vehicleDetails?: string; // JSON string
  licenseNumber?: string;
  licenseExpiryDate?: string;
  insuranceDetails?: string; // JSON string
  bankDetails?: string; // JSON string (encrypted)
  emergencyContact?: string; // JSON string
  workingAreas: string[];
  maxDeliveryRadius: number;
  isAvailable: boolean;
  isOnline: boolean;
  currentLocation?: string; // JSON string with coordinates
  rating: number;
  reviewCount: number;
  totalDeliveries: number;
  totalEarnings: number;
  verificationStatus: VerificationStatus;
  verifiedAt?: string;
  documentsUploaded: string[];
  backgroundCheckStatus: VerificationStatus;
  backgroundCheckDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Support System Types

export interface SupportTicket {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  ticketNumber: string;
  userId: string;
  orderId?: string;
  category: string;
  priority: TicketPriority;
  subject: string;
  description: string;
  status: TicketStatus;
  assignedTo?: string;
  attachments: string[];
  tags: string[];
  resolution?: string;
  resolutionTime?: number; // in minutes
  satisfactionRating?: number; // 1-5
  satisfactionComment?: string;
  closedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Configuration Types

export interface VendorSettings {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  vendorId: string;
  autoAcceptOrders: boolean;
  preparationTime: number; // in minutes
  maxOrdersPerHour?: number;
  deliverySettings?: string; // JSON string
  paymentSettings?: string; // JSON string
  notificationSettings?: string; // JSON string
  businessHours?: string; // JSON string
  holidaySchedule?: string; // JSON string
  taxSettings?: string; // JSON string
  integrations?: string; // JSON string
  customFields?: string; // JSON string
  createdAt: string;
  updatedAt: string;
}

// Authentication Types

export interface OTPCode {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
  phone: string;
  code: string;
  type: 'verification' | 'password_reset' | 'login';
  attempts: number;
  maxAttempts: number;
  isUsed: boolean;
  usedAt?: string;
  expiresAt: string;
  createdAt: string;
}

// Parsed JSON types for complex fields

export interface VehicleDetails {
  make?: string;
  model?: string;
  year?: number;
  color?: string;
  licensePlate?: string;
  registrationNumber?: string;
}

export interface InsuranceDetails {
  provider: string;
  policyNumber: string;
  expiryDate: string;
  coverageAmount: number;
}

export interface BankDetails {
  accountHolderName: string;
  bankName: string;
  accountNumber: string;
  branchCode: string;
  accountType: 'savings' | 'current';
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

export interface DeliverySettings {
  acceptCashOnDelivery: boolean;
  requireSignature: boolean;
  allowContactlessDelivery: boolean;
  maxDeliveryDistance: number;
  deliveryTimeSlots: Array<{
    day: string;
    slots: Array<{
      startTime: string;
      endTime: string;
      capacity: number;
    }>;
  }>;
}

export interface PaymentSettings {
  acceptedMethods: PaymentMethod[];
  autoWithdraw: boolean;
  withdrawalThreshold: number;
  withdrawalSchedule: 'daily' | 'weekly' | 'monthly';
  taxId?: string;
  vatNumber?: string;
}

export interface NotificationSettings {
  newOrders: boolean;
  orderUpdates: boolean;
  paymentNotifications: boolean;
  promotionalEmails: boolean;
  weeklyReports: boolean;
  channels: {
    push: boolean;
    email: boolean;
    sms: boolean;
  };
}

// Parsed versions of complex types

export interface ParsedRunnerProfile extends Omit<RunnerProfile, 'vehicleDetails' | 'insuranceDetails' | 'bankDetails' | 'emergencyContact' | 'currentLocation'> {
  vehicleDetails?: VehicleDetails;
  insuranceDetails?: InsuranceDetails;
  bankDetails?: BankDetails;
  emergencyContact?: EmergencyContact;
  currentLocation?: Coordinates;
}

export interface ParsedVendorSettings extends Omit<VendorSettings, 'deliverySettings' | 'paymentSettings' | 'notificationSettings' | 'businessHours' | 'holidaySchedule'> {
  deliverySettings?: DeliverySettings;
  paymentSettings?: PaymentSettings;
  notificationSettings?: NotificationSettings;
  businessHours?: BusinessHours;
  holidaySchedule?: Array<{
    date: string;
    name: string;
    isClosed: boolean;
    specialHours?: {
      openTime: string;
      closeTime: string;
    };
  }>;
}

// Collection names for type safety
export const COLLECTIONS = {
  USERS: 'users',
  ADDRESSES: 'addresses',
  CATEGORIES: 'categories',
  VENDORS: 'vendors',
  MENU_ITEMS: 'menu-items',
  ORDERS: 'orders',
  REVIEWS: 'reviews',
  MESSAGES: 'messages',
  CONVERSATIONS: 'conversations',
  NOTIFICATIONS: 'notifications',
  PROMOTIONS: 'promotions',
  PROMOTION_USAGE: 'promotion-usage',
  ANALYTICS_EVENTS: 'analytics-events',
  PAYMENTS: 'payments',
  RUNNER_PROFILES: 'runner-profiles',
  SUPPORT_TICKETS: 'support-tickets',
  VENDOR_SETTINGS: 'vendor-settings',
  OTP_CODES: 'otp-codes',
} as const;

// Database configuration
export const DATABASE_ID = 'hvppyplug-main';

// Type guards for runtime type checking
export function isUser(obj: any): obj is User {
  return obj && typeof obj.name === 'string' && typeof obj.phone === 'string' && typeof obj.role === 'string';
}

export function isOrder(obj: any): obj is Order {
  return obj && typeof obj.orderNumber === 'string' && typeof obj.customerId === 'string' && typeof obj.vendorId === 'string';
}

export function isVendor(obj: any): obj is Vendor {
  return obj && typeof obj.name === 'string' && typeof obj.ownerId === 'string' && typeof obj.businessType === 'string';
}

// Utility types for API responses
export type CreateUserData = Omit<User, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId' | 'createdAt' | 'updatedAt'>;
export type UpdateUserData = Partial<CreateUserData>;

export type CreateOrderData = Omit<Order, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId' | 'orderNumber' | 'createdAt' | 'updatedAt' | 'completedAt'>;
export type UpdateOrderData = Partial<Pick<Order, 'status' | 'paymentStatus' | 'runnerId' | 'estimatedDeliveryTime' | 'actualDeliveryTime' | 'notes' | 'trackingUpdates'>>;

export type CreateVendorData = Omit<Vendor, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId' | 'rating' | 'reviewCount' | 'verifiedAt' | 'createdAt' | 'updatedAt'>;
export type UpdateVendorData = Partial<CreateVendorData>;

// Query helper types
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  filters?: Array<{
    field: string;
    operator: 'equal' | 'notEqual' | 'lessThan' | 'lessThanEqual' | 'greaterThan' | 'greaterThanEqual' | 'search' | 'contains';
    value: any;
  }>;
}

export interface PaginatedResponse<T> {
  documents: T[];
  total: number;
  limit: number;
  offset: number;
}

// Error types
export interface DatabaseError {
  code: string;
  message: string;
  type: 'validation' | 'permission' | 'not_found' | 'conflict' | 'server_error';
  details?: any;
}
