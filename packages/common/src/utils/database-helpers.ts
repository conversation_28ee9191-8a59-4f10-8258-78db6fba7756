/**
 * Database Helper Utilities
 * 
 * Utility functions for working with Appwrite database types and operations
 */

import {
  User, ParsedUser, UserPreferences,
  Order, ParsedOrder, OrderItem, DeliveryAddress, TrackingUpdate,
  Vendor, ParsedVendor, BusinessHours, Coordinates,
  MenuItem, ParsedMenuItem,
  RunnerProfile, ParsedRunnerProfile, VehicleDetails, InsuranceDetails, BankDetails, EmergencyContact,
  VendorSettings, ParsedVendorSettings, DeliverySettings, PaymentSettings, NotificationSettings,
  Message, Conversation, Review, Notification, Promotion, AnalyticsEvent, Payment, SupportTicket, OTPCode,
  QueryOptions, DatabaseError
} from '../types/database';

// JSON parsing utilities with error handling

export function safeJsonParse<T>(jsonString: string | undefined | null, fallback: T): T {
  if (!jsonString) return fallback;
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.warn('Failed to parse JSON:', error);
    return fallback;
  }
}

export function safeJsonStringify(obj: any): string {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.warn('Failed to stringify object:', error);
    return '{}';
  }
}

// Type parsers for complex database fields

export function parseUser(user: User): ParsedUser {
  return {
    ...user,
    preferences: safeJsonParse<UserPreferences>(user.preferences, {
      notifications: {
        push: true,
        email: true,
        sms: false,
        orderUpdates: true,
        promotions: false,
        newMessages: true,
      },
      privacy: {
        showOnlineStatus: true,
        allowDirectMessages: true,
        shareLocation: false,
      },
      app: {
        theme: 'system',
        language: 'en',
        currency: 'ZAR',
      },
    }),
  };
}

export function parseOrder(order: Order): ParsedOrder {
  return {
    ...order,
    items: safeJsonParse<OrderItem[]>(order.items, []),
    deliveryAddress: safeJsonParse<DeliveryAddress>(order.deliveryAddress, {
      street: '',
      city: '',
      province: '',
      postalCode: '',
      coordinates: { lat: 0, lng: 0 },
    }),
    trackingUpdates: safeJsonParse<TrackingUpdate[]>(order.trackingUpdates, []),
  };
}

export function parseVendor(vendor: Vendor): ParsedVendor {
  return {
    ...vendor,
    businessHours: safeJsonParse<BusinessHours>(vendor.businessHours, {}),
    coordinates: safeJsonParse<Coordinates>(vendor.coordinates, { lat: 0, lng: 0 }),
  };
}

export function parseMenuItem(menuItem: MenuItem): ParsedMenuItem {
  return {
    ...menuItem,
    variants: safeJsonParse(menuItem.variants, []),
    options: safeJsonParse(menuItem.options, []),
    nutritionInfo: safeJsonParse(menuItem.nutritionInfo, {}),
  };
}

export function parseRunnerProfile(profile: RunnerProfile): ParsedRunnerProfile {
  return {
    ...profile,
    vehicleDetails: safeJsonParse<VehicleDetails>(profile.vehicleDetails, {}),
    insuranceDetails: safeJsonParse<InsuranceDetails>(profile.insuranceDetails, {
      provider: '',
      policyNumber: '',
      expiryDate: '',
      coverageAmount: 0,
    }),
    bankDetails: safeJsonParse<BankDetails>(profile.bankDetails, {
      accountHolderName: '',
      bankName: '',
      accountNumber: '',
      branchCode: '',
      accountType: 'savings',
    }),
    emergencyContact: safeJsonParse<EmergencyContact>(profile.emergencyContact, {
      name: '',
      relationship: '',
      phone: '',
    }),
    currentLocation: safeJsonParse<Coordinates>(profile.currentLocation, { lat: 0, lng: 0 }),
  };
}

export function parseVendorSettings(settings: VendorSettings): ParsedVendorSettings {
  return {
    ...settings,
    deliverySettings: safeJsonParse<DeliverySettings>(settings.deliverySettings, {
      acceptCashOnDelivery: false,
      requireSignature: false,
      allowContactlessDelivery: true,
      maxDeliveryDistance: 10,
      deliveryTimeSlots: [],
    }),
    paymentSettings: safeJsonParse<PaymentSettings>(settings.paymentSettings, {
      acceptedMethods: ['card'],
      autoWithdraw: false,
      withdrawalThreshold: 1000,
      withdrawalSchedule: 'weekly',
    }),
    notificationSettings: safeJsonParse<NotificationSettings>(settings.notificationSettings, {
      newOrders: true,
      orderUpdates: true,
      paymentNotifications: true,
      promotionalEmails: false,
      weeklyReports: true,
      channels: {
        push: true,
        email: true,
        sms: false,
      },
    }),
    businessHours: safeJsonParse<BusinessHours>(settings.businessHours, {}),
    holidaySchedule: safeJsonParse(settings.holidaySchedule, []),
  };
}

// Data preparation utilities for database operations

export function prepareUserForDatabase(user: Partial<ParsedUser>): Partial<User> {
  const { preferences, ...rest } = user;
  return {
    ...rest,
    preferences: preferences ? safeJsonStringify(preferences) : undefined,
  };
}

export function prepareOrderForDatabase(order: Partial<ParsedOrder>): Partial<Order> {
  const { items, deliveryAddress, trackingUpdates, ...rest } = order;
  return {
    ...rest,
    items: items ? safeJsonStringify(items) : undefined,
    deliveryAddress: deliveryAddress ? safeJsonStringify(deliveryAddress) : undefined,
    trackingUpdates: trackingUpdates ? safeJsonStringify(trackingUpdates) : undefined,
  };
}

export function prepareVendorForDatabase(vendor: Partial<ParsedVendor>): Partial<Vendor> {
  const { businessHours, coordinates, ...rest } = vendor;
  return {
    ...rest,
    businessHours: businessHours ? safeJsonStringify(businessHours) : undefined,
    coordinates: coordinates ? safeJsonStringify(coordinates) : undefined,
  };
}

export function prepareMenuItemForDatabase(menuItem: Partial<ParsedMenuItem>): Partial<MenuItem> {
  const { variants, options, nutritionInfo, ...rest } = menuItem;
  return {
    ...rest,
    variants: variants ? safeJsonStringify(variants) : undefined,
    options: options ? safeJsonStringify(options) : undefined,
    nutritionInfo: nutritionInfo ? safeJsonStringify(nutritionInfo) : undefined,
  };
}

export function prepareRunnerProfileForDatabase(profile: Partial<ParsedRunnerProfile>): Partial<RunnerProfile> {
  const { vehicleDetails, insuranceDetails, bankDetails, emergencyContact, currentLocation, ...rest } = profile;
  return {
    ...rest,
    vehicleDetails: vehicleDetails ? safeJsonStringify(vehicleDetails) : undefined,
    insuranceDetails: insuranceDetails ? safeJsonStringify(insuranceDetails) : undefined,
    bankDetails: bankDetails ? safeJsonStringify(bankDetails) : undefined,
    emergencyContact: emergencyContact ? safeJsonStringify(emergencyContact) : undefined,
    currentLocation: currentLocation ? safeJsonStringify(currentLocation) : undefined,
  };
}

export function prepareVendorSettingsForDatabase(settings: Partial<ParsedVendorSettings>): Partial<VendorSettings> {
  const { deliverySettings, paymentSettings, notificationSettings, businessHours, holidaySchedule, ...rest } = settings;
  return {
    ...rest,
    deliverySettings: deliverySettings ? safeJsonStringify(deliverySettings) : undefined,
    paymentSettings: paymentSettings ? safeJsonStringify(paymentSettings) : undefined,
    notificationSettings: notificationSettings ? safeJsonStringify(notificationSettings) : undefined,
    businessHours: businessHours ? safeJsonStringify(businessHours) : undefined,
    holidaySchedule: holidaySchedule ? safeJsonStringify(holidaySchedule) : undefined,
  };
}

// Validation utilities

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  // South African phone number validation
  const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

export function validateOrderNumber(orderNumber: string): boolean {
  // Format: HVP-YYYYMMDD-XXXX
  const orderRegex = /^HVP-\d{8}-\d{4}$/;
  return orderRegex.test(orderNumber);
}

export function validatePromotionCode(code: string): boolean {
  // Alphanumeric, 3-20 characters
  const codeRegex = /^[A-Z0-9]{3,20}$/;
  return codeRegex.test(code.toUpperCase());
}

// Query builder utilities

export function buildQuery(options: QueryOptions): string[] {
  const queries: string[] = [];
  
  if (options.filters) {
    options.filters.forEach(filter => {
      switch (filter.operator) {
        case 'equal':
          queries.push(`equal("${filter.field}", "${filter.value}")`);
          break;
        case 'notEqual':
          queries.push(`notEqual("${filter.field}", "${filter.value}")`);
          break;
        case 'lessThan':
          queries.push(`lessThan("${filter.field}", ${filter.value})`);
          break;
        case 'lessThanEqual':
          queries.push(`lessThanEqual("${filter.field}", ${filter.value})`);
          break;
        case 'greaterThan':
          queries.push(`greaterThan("${filter.field}", ${filter.value})`);
          break;
        case 'greaterThanEqual':
          queries.push(`greaterThanEqual("${filter.field}", ${filter.value})`);
          break;
        case 'search':
          queries.push(`search("${filter.field}", "${filter.value}")`);
          break;
        case 'contains':
          queries.push(`contains("${filter.field}", "${filter.value}")`);
          break;
      }
    });
  }

  if (options.orderBy) {
    const direction = options.orderDirection === 'desc' ? 'desc' : 'asc';
    queries.push(`orderBy("${options.orderBy}", "${direction}")`);
  }

  if (options.limit) {
    queries.push(`limit(${options.limit})`);
  }

  if (options.offset) {
    queries.push(`offset(${options.offset})`);
  }

  return queries;
}

// Error handling utilities

export function createDatabaseError(
  code: string,
  message: string,
  type: DatabaseError['type'] = 'server_error',
  details?: any
): DatabaseError {
  return {
    code,
    message,
    type,
    details,
  };
}

export function isDatabaseError(error: any): error is DatabaseError {
  return error && typeof error.code === 'string' && typeof error.message === 'string' && typeof error.type === 'string';
}

// Utility functions for common operations

export function generateOrderNumber(): string {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `HVP-${dateStr}-${random}`;
}

export function generateTicketNumber(): string {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `TKT-${dateStr}-${random}`;
}

export function calculateOrderTotal(
  subtotal: number,
  discount: number = 0,
  deliveryFee: number = 0,
  serviceFee: number = 0,
  tax: number = 0,
  tip: number = 0
): number {
  return Math.max(0, subtotal - discount + deliveryFee + serviceFee + tax + tip);
}

export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function isWithinDeliveryRadius(
  vendorCoords: Coordinates,
  deliveryCoords: Coordinates,
  radiusKm: number
): boolean {
  const distance = calculateDistance(
    vendorCoords.lat,
    vendorCoords.lng,
    deliveryCoords.lat,
    deliveryCoords.lng
  );
  return distance <= radiusKm;
}

// Date and time utilities

export function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString();
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}

export function formatTime(dateString: string): string {
  return new Date(dateString).toLocaleTimeString();
}

export function isExpired(expiryDate: string): boolean {
  return new Date(expiryDate) < new Date();
}

export function addMinutes(date: Date, minutes: number): Date {
  return new Date(date.getTime() + minutes * 60000);
}

export function addHours(date: Date, hours: number): Date {
  return new Date(date.getTime() + hours * 3600000);
}

export function addDays(date: Date, days: number): Date {
  return new Date(date.getTime() + days * 86400000);
}
