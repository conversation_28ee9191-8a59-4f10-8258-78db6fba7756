# HVPPYPlug+ Database Setup Instructions

## Current Status ✅

✅ **Configuration Files Ready**: All Appwrite configuration files have been merged successfully  
✅ **Scripts Ready**: Migration and seeding scripts are prepared  
✅ **Dependencies Installed**: All required packages are available  
✅ **Environment Configured**: Project ID and endpoint are set correctly  

## Next Step: Get Appwrite API Key 🔑

To complete the setup, you need to get an API key from your Appwrite console:

### 1. Get API Key from Appwrite Console

1. **Go to Appwrite Console**: https://fra.cloud.appwrite.io/console/project-6880d655003a8926a438
2. **Navigate to Settings** → **API Keys**
3. **Create a new API Key** with these scopes:
   - `databases.read`
   - `databases.write` 
   - `collections.read`
   - `collections.write`
   - `documents.read`
   - `documents.write`
   - `attributes.read`
   - `attributes.write`
   - `indexes.read`
   - `indexes.write`

### 2. Update Environment Variable

Replace the placeholder in your `.env` file:
```bash
# Change this line in .env:
APPWRITE_API_KEY=your_appwrite_api_key_here

# To your actual API key:
APPWRITE_API_KEY=your_actual_api_key_from_console
```

### 3. Run the Setup Scripts

Once you have the API key, run these commands:

```bash
# Navigate to scripts directory
cd scripts

# 1. Verify collections (should show missing collections)
node verify-collections.js

# 2. Run database migration (creates all collections)
node database-migration.js

# 3. Verify collections again (should show all 18 collections)
node verify-collections.js

# 4. Seed with realistic South African data
node data-seeders.js

# 5. Final verification
node verify-collections.js
```

## What the Scripts Will Do

### 🏗️ Database Migration (`database-migration.js`)
- **Backup existing data** (if any)
- **Create 18 collections** with proper schema
- **Set up relationships** and indexes
- **Apply security rules** and permissions
- **Validate everything** works correctly

### 🌱 Data Seeding (`data-seeders.js`)
- **100 users** with South African phone numbers (082, 083, etc.)
- **25 vendors** with authentic SA business names and locations
- **15 categories** including traditional SA food categories
- **200 menu items** including Bunny Chow, Boerewors, Biltong, etc.
- **150 orders** with realistic order flows and statuses
- **80 addresses** in real SA cities (Johannesburg, Cape Town, Durban)
- **20 runner profiles** with SA vehicle types and working areas
- **10 promotions** with SA-specific discount codes

### 📊 Expected Results
After running all scripts, you'll have:
- **18 collections** fully configured
- **1,000+ realistic records** with South African context
- **Proper relationships** between all entities
- **Performance optimized** with strategic indexes
- **Security implemented** with role-based access control

## Alternative: Quick Demo Mode

If you want to see the scripts in action without setting up Appwrite, you can run them in demo mode:

```bash
# Run in demo mode (shows what would happen)
export DEMO_MODE=true
node verify-collections.js
node data-seeders.js
```

## Verification Commands

After setup, verify everything works:

```bash
# Check all collections exist
node verify-collections.js

# Check specific collection counts
node -e "
const { Client, Databases } = require('node-appwrite');
const client = new Client()
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);
const databases = new Databases(client);

databases.listDocuments('hvppyplug-main', 'users')
  .then(result => console.log('Users:', result.total))
  .catch(console.error);
"
```

## Troubleshooting

### Common Issues:

1. **"Project not found"** → Check project ID in .env file
2. **"Not authorized"** → Check API key has correct scopes
3. **"Collection already exists"** → Run with `--force` flag or delete existing collections
4. **Rate limiting** → Scripts include delays, but you can increase them if needed

### Support:
- Check logs in `logs/` directory
- Review Appwrite console for detailed errors
- Verify network connectivity to Appwrite endpoint

---

## Ready to Proceed? 🚀

Once you have the API key:
1. Update the `.env` file with your actual API key
2. Run the migration script: `node database-migration.js`
3. Run the seeding script: `node data-seeders.js`
4. Verify everything: `node verify-collections.js`

Your HVPPYPlug+ database will then be ready for MVP development! 🎉
