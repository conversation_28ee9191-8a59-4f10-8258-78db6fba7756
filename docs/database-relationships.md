# HVPPYPlug+ Database Relationships and Data Integrity

## Overview

This document defines the relationships between collections in the HVPPYPlug+ database using Appwrite's relationship attributes for optimal data integrity and query performance.

## Relationship Types

### 1. One-to-Many Relationships

#### users → addresses
- **Parent**: users
- **Child**: addresses
- **Foreign Key**: addresses.userId
- **Relationship**: One user can have multiple addresses
- **Cascade**: Delete addresses when user is deleted

#### users → vendors (via ownerId)
- **Parent**: users
- **Child**: vendors
- **Foreign Key**: vendors.ownerId
- **Relationship**: One user can own multiple vendor businesses
- **Cascade**: Soft delete vendors when user is deleted

#### users → runner_profiles
- **Parent**: users
- **Child**: runner_profiles
- **Foreign Key**: runner_profiles.userId
- **Relationship**: One user can have one runner profile
- **Cascade**: Delete runner profile when user is deleted

#### vendors → menu_items
- **Parent**: vendors
- **Child**: menu_items
- **Foreign Key**: menu_items.vendorId
- **Relationship**: One vendor can have multiple menu items
- **Cascade**: Delete menu items when vendor is deleted

#### vendors → vendor_settings
- **Parent**: vendors
- **Child**: vendor_settings
- **Foreign Key**: vendor_settings.vendorId
- **Relationship**: One vendor has one settings record
- **Cascade**: Delete settings when vendor is deleted

#### categories → menu_items
- **Parent**: categories
- **Child**: menu_items
- **Foreign Key**: menu_items.categoryId
- **Relationship**: One category can have multiple menu items
- **Cascade**: Set categoryId to null when category is deleted

#### categories → categories (Self-referencing)
- **Parent**: categories
- **Child**: categories
- **Foreign Key**: categories.parentId
- **Relationship**: Categories can have subcategories
- **Cascade**: Set parentId to null when parent category is deleted

#### users → orders (as customer)
- **Parent**: users
- **Child**: orders
- **Foreign Key**: orders.customerId
- **Relationship**: One user can have multiple orders as customer
- **Cascade**: Soft delete orders when user is deleted

#### vendors → orders
- **Parent**: vendors
- **Child**: orders
- **Foreign Key**: orders.vendorId
- **Relationship**: One vendor can have multiple orders
- **Cascade**: Soft delete orders when vendor is deleted

#### users → orders (as runner)
- **Parent**: users
- **Child**: orders
- **Foreign Key**: orders.runnerId
- **Relationship**: One runner can have multiple orders
- **Cascade**: Set runnerId to null when runner is deleted

#### orders → reviews
- **Parent**: orders
- **Child**: reviews
- **Foreign Key**: reviews.orderId
- **Relationship**: One order can have multiple reviews (customer, vendor, runner)
- **Cascade**: Delete reviews when order is deleted

#### orders → payments
- **Parent**: orders
- **Child**: payments
- **Foreign Key**: payments.orderId
- **Relationship**: One order can have multiple payment attempts
- **Cascade**: Keep payments for audit trail

#### users → messages (as sender)
- **Parent**: users
- **Child**: messages
- **Foreign Key**: messages.senderId
- **Relationship**: One user can send multiple messages
- **Cascade**: Soft delete messages when user is deleted

#### users → messages (as receiver)
- **Parent**: users
- **Child**: messages
- **Foreign Key**: messages.receiverId
- **Relationship**: One user can receive multiple messages
- **Cascade**: Soft delete messages when user is deleted

#### conversations → messages
- **Parent**: conversations
- **Child**: messages
- **Foreign Key**: messages.conversationId
- **Relationship**: One conversation can have multiple messages
- **Cascade**: Delete messages when conversation is deleted

#### users → notifications
- **Parent**: users
- **Child**: notifications
- **Foreign Key**: notifications.userId
- **Relationship**: One user can have multiple notifications
- **Cascade**: Delete notifications when user is deleted

#### promotions → promotion_usage
- **Parent**: promotions
- **Child**: promotion_usage
- **Foreign Key**: promotion_usage.promotionId
- **Relationship**: One promotion can have multiple usage records
- **Cascade**: Keep usage records for audit trail

#### users → support_tickets
- **Parent**: users
- **Child**: support_tickets
- **Foreign Key**: support_tickets.userId
- **Relationship**: One user can have multiple support tickets
- **Cascade**: Keep tickets for audit trail

### 2. Many-to-Many Relationships

#### users ↔ conversations
- **Junction**: conversations.participants (array)
- **Relationship**: Users can participate in multiple conversations
- **Implementation**: Store user IDs in participants array

#### promotions ↔ categories
- **Junction**: promotions.categoryIds (array)
- **Relationship**: Promotions can apply to multiple categories
- **Implementation**: Store category IDs in categoryIds array

#### promotions ↔ menu_items
- **Junction**: promotions.itemIds (array)
- **Relationship**: Promotions can apply to specific menu items
- **Implementation**: Store menu item IDs in itemIds array

#### promotions ↔ users
- **Junction**: promotions.userIds (array)
- **Relationship**: Promotions can be targeted to specific users
- **Implementation**: Store user IDs in userIds array

### 3. Reference Relationships

#### orders → addresses (delivery address)
- **Type**: Reference by ID stored as JSON
- **Field**: orders.deliveryAddress
- **Relationship**: Order references a delivery address
- **Implementation**: Store address data as JSON for historical accuracy

#### reviews → menu_items (optional)
- **Parent**: menu_items
- **Child**: reviews
- **Foreign Key**: reviews.itemId
- **Relationship**: Reviews can be for specific menu items
- **Cascade**: Set itemId to null when menu item is deleted

#### messages → orders (optional)
- **Parent**: orders
- **Child**: messages
- **Foreign Key**: messages.orderId
- **Relationship**: Messages can be related to specific orders
- **Cascade**: Set orderId to null when order is deleted

#### support_tickets → orders (optional)
- **Parent**: orders
- **Child**: support_tickets
- **Foreign Key**: support_tickets.orderId
- **Relationship**: Support tickets can be related to specific orders
- **Cascade**: Set orderId to null when order is deleted

## Data Integrity Rules

### 1. Referential Integrity
- All foreign key relationships must be enforced
- Cascade deletes should be implemented where appropriate
- Soft deletes should be used for audit trail preservation

### 2. Business Logic Constraints
- Users can only have one active runner profile
- Vendors must have at least one category
- Orders must have valid customer, vendor, and delivery address
- Reviews can only be created after order completion
- Promotions must have valid date ranges

### 3. Data Validation
- Phone numbers must be unique across users
- Email addresses must be unique if provided
- Order numbers must be unique
- Promotion codes must be unique
- Ticket numbers must be unique

### 4. Performance Optimization
- Index all foreign key fields
- Index frequently queried fields (status, dates, ratings)
- Use compound indexes for complex queries
- Implement proper pagination for large datasets

## Implementation Notes

### Appwrite Relationship Attributes
```json
{
  "key": "userId",
  "type": "relationship",
  "relatedCollection": "users",
  "relationType": "oneToMany",
  "twoWay": true,
  "twoWayKey": "addresses",
  "onDelete": "cascade",
  "side": "child"
}
```

### Query Patterns
- Use relationship queries for efficient data fetching
- Implement proper error handling for missing relationships
- Cache frequently accessed relationship data
- Use batch operations for bulk relationship updates

## Next Steps
1. Implement relationship attributes in Appwrite configuration
2. Create security rules based on relationships
3. Generate TypeScript types with relationship definitions
4. Create migration scripts for relationship setup
