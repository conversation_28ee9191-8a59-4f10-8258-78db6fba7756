# HVPPYPlug+ Database Security Rules and Permissions

## Overview

This document defines comprehensive role-based access control (RBAC) and security rules for all collections in the HVPPYPlug+ database, ensuring data privacy, security, and proper access patterns.

## User Roles

### Role Definitions
- **customer**: End users who place orders
- **vendor**: Business owners who provide products/services
- **runner**: Delivery personnel
- **admin**: System administrators
- **support**: Customer support agents

## Collection Security Rules

### 1. users Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"any\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for basic profile information (name, avatar)
- **Create**: Anyone can register (phone verification required)
- **Update**: Users can update their own profile, admins can update any
- **Delete**: Only admins can delete users (soft delete preferred)

#### Sensitive Fields
- Phone numbers: Only visible to self, admins, and related business partners
- Email: Only visible to self and admins
- Push tokens: Only visible to self and system

### 2. addresses Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"user:self\")",
    "update(\"user:self\")",
    "delete(\"user:self\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Users can only see their own addresses, admins see all
- **Create/Update/Delete**: Users manage their own addresses only
- **Vendor Access**: Vendors can read delivery addresses for their orders only

### 3. categories Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"role:admin\")",
    "update(\"role:admin\")",
    "update(\"role:vendor\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for all active categories
- **Create/Delete**: Only admins can create/delete categories
- **Update**: Admins can update any, vendors can suggest updates

### 4. vendors Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"role:vendor\")",
    "create(\"role:admin\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for active/verified vendors
- **Create**: Vendors can create their own business profile
- **Update**: Vendors can update their own profile, admins can update any
- **Delete**: Only admins can delete (soft delete preferred)

#### Field-Level Security
- **Financial data**: Only visible to owner and admins
- **Contact info**: Visible to customers with active orders
- **Status**: Only admins can change verification status

### 5. menu-items Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"role:vendor\")",
    "update(\"role:vendor\")",
    "update(\"role:admin\")",
    "delete(\"role:vendor\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for available items from active vendors
- **Create/Update/Delete**: Vendors manage their own items, admins can manage any
- **Inventory**: Only vendor and admin can see actual inventory levels

### 6. orders Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"role:customer\")",
    "update(\"role:vendor\")",
    "update(\"role:runner\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Customers see their orders, vendors see orders for their business, runners see assigned orders
- **Create**: Only customers can create orders
- **Update**: Vendors update order status, runners update delivery status
- **Delete**: Only admins can delete (soft delete preferred)

#### Dynamic Permissions
```javascript
// Custom permission logic
function canReadOrder(user, order) {
  return user.id === order.customerId || 
         user.id === order.vendorId || 
         user.id === order.runnerId || 
         user.role === 'admin';
}
```

### 7. reviews Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"role:customer\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "delete(\"user:self\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for approved reviews
- **Create**: Only customers who completed orders can review
- **Update/Delete**: Users can manage their own reviews, admins can moderate

#### Validation Rules
- One review per customer per order
- Reviews only allowed after order completion
- Moderation required for reviews with reports

### 8. messages Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"user:self\")",
    "update(\"user:self\")",
    "delete(\"user:self\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Only participants in conversation can read messages
- **Create**: Users can send messages to related parties (customer-vendor, customer-runner)
- **Update**: Users can edit their own messages within time limit
- **Delete**: Users can delete their own messages, admins can moderate

#### Dynamic Permissions
```javascript
function canReadMessage(user, message) {
  return user.id === message.senderId || 
         user.id === message.receiverId || 
         user.role === 'admin';
}
```

### 9. conversations Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"user:self\")",
    "update(\"user:self\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Only participants can read conversation
- **Create**: Users can create conversations with business partners
- **Update**: Participants can update conversation settings
- **Delete**: Only admins can delete conversations

### 10. notifications Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "create(\"role:admin\")",
    "create(\"any\")",
    "update(\"user:self\")",
    "delete(\"user:self\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Users see only their own notifications
- **Create**: System can create notifications, admins can send broadcasts
- **Update**: Users can mark as read, admins can manage any
- **Delete**: Users can delete their own, admins can manage any

### 11. promotions Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"any\")",
    "create(\"role:vendor\")",
    "create(\"role:admin\")",
    "update(\"role:vendor\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Public read for active promotions
- **Create**: Vendors can create promotions for their business, admins can create system-wide
- **Update**: Vendors can update their own, admins can update any
- **Delete**: Only admins can delete

### 12. promotion-usage Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"any\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Users see their own usage, admins see all
- **Create**: System creates usage records automatically
- **Update/Delete**: Only admins for audit corrections

### 13. analytics-events Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"role:admin\")",
    "read(\"role:vendor\")",
    "create(\"any\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Admins see all, vendors see their own business data
- **Create**: System creates events automatically
- **Update/Delete**: Only admins for data management

### 14. payments Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"any\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Users see their own payments, vendors see payments for their orders
- **Create**: System creates payment records
- **Update/Delete**: Only admins for audit and corrections

#### Sensitive Data
- Payment details encrypted at rest
- PCI compliance for card data
- Audit trail for all changes

### 15. runner-profiles Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"role:runner\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Runners see their own profile, admins see all, customers see limited info for assigned runners
- **Create**: Runners can create their profile
- **Update**: Runners update their own, admins can update any
- **Delete**: Only admins can delete

### 16. support-tickets Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "read(\"role:support\")",
    "create(\"user:self\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "update(\"role:support\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Users see their own tickets, support agents and admins see assigned/all tickets
- **Create**: Users can create support tickets
- **Update**: Users can add comments, support agents can update status
- **Delete**: Only admins can delete

### 17. vendor-settings Collection

#### Permissions
```json
{
  "permissions": [
    "read(\"user:self\")",
    "read(\"role:admin\")",
    "create(\"role:vendor\")",
    "update(\"user:self\")",
    "update(\"role:admin\")",
    "delete(\"role:admin\")"
  ]
}
```

#### Business Rules
- **Read**: Vendors see their own settings, admins see all
- **Create**: Vendors create their settings
- **Update**: Vendors update their own, admins can update any
- **Delete**: Only admins can delete

## Security Implementation

### 1. Authentication Requirements
- All operations require valid authentication
- Phone number verification for account creation
- Session management with proper expiration
- Multi-factor authentication for sensitive operations

### 2. Data Encryption
- Sensitive fields encrypted at rest
- TLS encryption for data in transit
- Secure key management
- Regular security audits

### 3. Rate Limiting
- API rate limiting per user/IP
- Bulk operation restrictions
- Abuse detection and prevention
- Automated security monitoring

### 4. Audit Trail
- All data modifications logged
- User action tracking
- Security event monitoring
- Compliance reporting

## Next Steps
1. Implement security rules in Appwrite configuration
2. Create custom permission functions for complex rules
3. Set up monitoring and alerting for security events
4. Regular security testing and updates
