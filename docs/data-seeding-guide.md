# HVPPYPlug+ Data Seeding Guide

## Overview

This guide provides comprehensive instructions for seeding the HVPPYPlug+ database with realistic South African data using Faker.js with the `zu_ZA` locale.

## Features

### 🇿🇦 South African Localization
- **Phone Numbers**: Authentic SA mobile numbers (082, 083, 084, etc.)
- **Locations**: Real provinces, cities, and townships
- **Business Names**: Culturally appropriate business naming
- **Food Items**: Traditional South African cuisine included
- **Languages**: Multi-language support (English, Afrikaans, Zulu, Xhosa)

### 📊 Realistic Data Relationships
- **Relational Integrity**: Proper foreign key relationships
- **Business Logic**: Realistic order flows and user behaviors
- **Geographic Accuracy**: Proper coordinate ranges for South Africa
- **Cultural Context**: Township areas, traditional foods, local business types

## Installation

### Prerequisites
```bash
# Navigate to scripts directory
cd scripts

# Install dependencies
npm install

# Or using pnpm (recommended)
pnpm install
```

### Environment Setup
Create a `.env` file in the scripts directory:
```bash
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=hvppyplug
APPWRITE_API_KEY=your_api_key_here
APPWRITE_DATABASE_ID=hvppyplug-main
NODE_ENV=development
```

## Usage

### Quick Start
```bash
# Run complete seeding process
npm run seed

# Or with pnpm
pnpm seed
```

### Environment-Specific Seeding
```bash
# Development environment (more data)
npm run seed:dev

# Staging environment (production-like data)
npm run seed:staging
```

### Individual Operations
```bash
# Verify collections exist before seeding
npm run verify

# Backup existing data
npm run backup

# Merge configuration files
npm run merge-config
```

## Seeding Configuration

### Default Seed Counts
```javascript
const seedConfig = {
  users: 100,           // Mixed roles: customers, vendors, runners, admins
  vendors: 25,          // Business profiles
  categories: 15,       // Product categories with subcategories
  menuItems: 200,       // Products across all vendors
  orders: 150,          // Order history with realistic statuses
  reviews: 300,         // Customer feedback and ratings
  addresses: 80,        // Customer delivery addresses
  runnerProfiles: 20,   // Delivery personnel profiles
  promotions: 10,       // Marketing campaigns and discounts
  supportTickets: 30,   // Customer service tickets
};
```

### Customizing Seed Data
Edit `scripts/data-seeders.js` to modify:
- **Quantities**: Adjust `seedConfig` object
- **Data Types**: Modify `southAfricanData` object
- **Business Logic**: Update individual seeder functions

## Data Structure

### User Distribution
- **70%** Customers (regular app users)
- **20%** Vendors (business owners)
- **10%** Runners (delivery personnel)
- **1** Admin user (system administrator)

### Geographic Distribution
```javascript
provinces: [
  'Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape',
  'Limpopo', 'Mpumalanga', 'North West', 'Free State', 'Northern Cape'
]

cities: {
  'Gauteng': ['Johannesburg', 'Pretoria', 'Soweto', 'Sandton'],
  'Western Cape': ['Cape Town', 'Stellenbosch', 'Paarl'],
  'KwaZulu-Natal': ['Durban', 'Pietermaritzburg', 'Newcastle'],
  // ... more cities
}

townships: [
  'Soweto', 'Alexandra', 'Khayelitsha', 'Gugulethu',
  'Mamelodi', 'Soshanguve', 'Umlazi', 'KwaMashu'
]
```

### Business Types
- **Restaurants**: Traditional SA cuisine, fast food, fine dining
- **Grocery**: Spaza shops, supermarkets, convenience stores
- **Pharmacy**: Health and wellness products
- **Electronics**: Mobile phones, accessories
- **Services**: Various local services

### Traditional South African Foods
```javascript
traditionalFoods: [
  'Boerewors', 'Biltong', 'Potjiekos', 'Bobotie', 'Sosaties',
  'Koeksisters', 'Vetkoek', 'Pap en Wors', 'Bunny Chow',
  'Gatsby', 'Samoosas', 'Roti'
]
```

## Generated Data Examples

### Sample User
```json
{
  "name": "Thabo Mthembu",
  "phone": "**********",
  "email": "<EMAIL>",
  "role": "customer",
  "status": "active",
  "isVerified": true,
  "preferences": {
    "notifications": { "push": true, "email": true, "sms": false },
    "privacy": { "showOnlineStatus": true, "allowDirectMessages": true },
    "app": { "theme": "dark", "language": "zu", "currency": "ZAR" }
  }
}
```

### Sample Vendor
```json
{
  "name": "Mama Nomsa's Kitchen",
  "slug": "mama-nomsas-kitchen",
  "businessType": "restaurant",
  "location": "123 Vilakazi Street, Soweto, Gauteng",
  "deliveryRadius": 15.5,
  "rating": 4.7,
  "deliveryFee": 25.00,
  "businessHours": {
    "monday": { "isOpen": true, "openTime": "08:00", "closeTime": "20:00" },
    "sunday": { "isOpen": true, "openTime": "10:00", "closeTime": "18:00" }
  }
}
```

### Sample Menu Item
```json
{
  "name": "Bunny Chow",
  "description": "Traditional Durban curry served in a hollowed-out loaf of bread",
  "price": 65.99,
  "category": "Traditional South African",
  "tags": ["traditional", "spicy", "popular"],
  "allergens": ["gluten"],
  "preparationTime": 25,
  "variants": [
    {
      "name": "Size",
      "options": ["Quarter", "Half", "Full"],
      "prices": { "Half": 15, "Full": 30 }
    }
  ]
}
```

### Sample Order
```json
{
  "orderNumber": "HVP-20250130-1234",
  "items": [
    {
      "itemId": "menu_item_id",
      "name": "Bunny Chow",
      "quantity": 2,
      "price": 65.99,
      "total": 131.98,
      "options": [
        { "name": "Size", "choice": "Half", "price": 15 }
      ]
    }
  ],
  "subtotal": 161.98,
  "deliveryFee": 25.00,
  "serviceFee": 8.10,
  "total": 195.08,
  "currency": "ZAR",
  "status": "delivered",
  "paymentMethod": "card"
}
```

## Verification

### Post-Seeding Checks
```bash
# Verify all collections were created
npm run verify

# Check data integrity
node -e "
const { databases } = require('./database-migration.js');
databases.listDocuments('hvppyplug-main', 'users')
  .then(result => console.log('Users created:', result.total));
"
```

### Manual Verification
1. **Appwrite Console**: Check collections and document counts
2. **Relationships**: Verify foreign key relationships work
3. **Data Quality**: Spot-check for realistic South African data
4. **Performance**: Test query performance with seeded data

## Troubleshooting

### Common Issues

#### Rate Limiting
```bash
# Error: Too many requests
# Solution: Increase delays in seeder functions
```

#### Permission Errors
```bash
# Error: Insufficient permissions
# Solution: Check API key has required scopes:
# - databases.read, databases.write
# - collections.read, collections.write
# - documents.read, documents.write
```

#### Memory Issues
```bash
# Error: Out of memory
# Solution: Reduce seed counts or run in batches
```

### Performance Optimization
```javascript
// Reduce batch sizes for slower connections
const batchSize = 10; // Instead of processing all at once

// Increase delays between operations
await new Promise(resolve => setTimeout(resolve, 100));
```

## Advanced Usage

### Custom Seeding Scenarios

#### E-commerce Focus
```javascript
// Increase product variety
seedConfig.menuItems = 500;
seedConfig.categories = 25;
seedConfig.vendors = 50;
```

#### Delivery Focus
```javascript
// More runners and orders
seedConfig.runnerProfiles = 50;
seedConfig.orders = 500;
seedConfig.users = 200; // More customers
```

#### Analytics Focus
```javascript
// More historical data
seedConfig.orders = 1000;
seedConfig.reviews = 2000;
// Extend date ranges in faker.date.past()
```

### Integration with CI/CD
```yaml
# .github/workflows/seed-staging.yml
name: Seed Staging Database
on:
  push:
    branches: [staging]
jobs:
  seed:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: cd scripts && npm install
      - run: cd scripts && npm run seed:staging
        env:
          APPWRITE_ENDPOINT: ${{ secrets.APPWRITE_ENDPOINT }}
          APPWRITE_PROJECT_ID: ${{ secrets.APPWRITE_PROJECT_ID }}
          APPWRITE_API_KEY: ${{ secrets.APPWRITE_API_KEY }}
```

## Data Privacy and Compliance

### Fake Data Only
- All generated data is completely fictional
- No real personal information is used
- Safe for development and testing environments

### Production Considerations
- **Never run seeders on production databases**
- Use environment variables to prevent accidental production seeding
- Implement safeguards in seeding scripts

### POPIA Compliance
- Generated data complies with South African POPIA requirements
- No real personal information is stored or processed
- Suitable for development and testing purposes

## Next Steps

After successful seeding:

1. **Integration Testing**: Test client applications with seeded data
2. **Performance Testing**: Measure query performance with realistic data volumes
3. **UI/UX Testing**: Verify user interfaces work with diverse data
4. **Business Logic Testing**: Validate business rules with realistic scenarios
5. **Load Testing**: Test system performance under realistic load

## Support

### Resources
- **Faker.js Documentation**: https://fakerjs.dev/
- **Appwrite Documentation**: https://appwrite.io/docs
- **South African Data Standards**: Local business and geographic standards

### Getting Help
- Check logs in `logs/` directory
- Review Appwrite console for errors
- Verify API key permissions
- Check network connectivity

---

**Last Updated**: January 30, 2025  
**Faker Version**: 8.4.1  
**Locale**: zu_ZA (South African)  
**Total Seed Records**: ~1,000+ realistic entries
