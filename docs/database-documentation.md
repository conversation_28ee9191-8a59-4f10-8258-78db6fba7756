# HVPPYPlug+ Database Documentation

## Overview

This document provides comprehensive documentation for the HVPPYPlug+ database schema, including entity relationships, collection schemas, usage examples, and developer guidelines.

## Database Architecture

### Database Information
- **Database ID**: `hvppyplug-main`
- **Database Name**: HVPPYPlug+ Main Database
- **Total Collections**: 18
- **Database Type**: NoSQL (Appwrite)
- **Schema Version**: Enhanced v2.0

### Design Principles
1. **Scalability**: Designed to handle growth from startup to enterprise scale
2. **Performance**: Optimized indexes and query patterns
3. **Security**: Role-based access control and data encryption
4. **Flexibility**: JSON fields for extensible metadata
5. **Integrity**: Proper relationships and constraints

## Entity Relationship Diagram

```mermaid
erDiagram
    USERS ||--o{ ADDRESSES : has
    USERS ||--o{ VENDORS : owns
    USERS ||--o{ ORDERS : places
    USERS ||--o{ REVIEWS : writes
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ NOTIFICATIONS : receives
    USERS ||--o{ SUPPORT_TICKETS : creates
    USERS ||--|| RUNNER_PROFILES : has
    
    VENDORS ||--o{ MENU_ITEMS : offers
    VENDORS ||--o{ ORDERS : receives
    VENDORS ||--|| VENDOR_SETTINGS : has
    VENDORS }o--o{ CATEGORIES : belongs_to
    
    CATEGORIES ||--o{ MENU_ITEMS : categorizes
    CATEGORIES ||--o{ CATEGORIES : parent_of
    
    ORDERS ||--o{ REVIEWS : generates
    ORDERS ||--o{ PAYMENTS : requires
    ORDERS ||--o{ MESSAGES : relates_to
    ORDERS }o--|| PROMOTIONS : uses
    
    CONVERSATIONS ||--o{ MESSAGES : contains
    
    PROMOTIONS ||--o{ PROMOTION_USAGE : tracks
    
    USERS {
        string id PK
        string name
        string phone UK
        string email UK
        string role
        string status
        boolean isVerified
        datetime lastActiveAt
        string pushToken
        json preferences
        datetime createdAt
        datetime updatedAt
    }
    
    ADDRESSES {
        string id PK
        string userId FK
        string type
        string label
        string street
        string city
        string province
        string postalCode
        string country
        json coordinates
        string instructions
        boolean isDefault
        boolean isActive
    }
    
    CATEGORIES {
        string id PK
        string name
        string slug UK
        string description
        string icon
        string color
        string imageUrl
        string parentId FK
        integer sortOrder
        boolean isActive
        json metadata
    }
    
    VENDORS {
        string id PK
        string name
        string slug UK
        string description
        string ownerId FK
        string businessType
        array categories
        string location
        json coordinates
        double deliveryRadius
        string status
        double rating
        integer reviewCount
        string imageUrl
        string coverImageUrl
        string phone
        string email
        string website
        json businessHours
        double deliveryFee
        double minimumOrder
        boolean isActive
        boolean isFeatured
        datetime verifiedAt
        datetime createdAt
        datetime updatedAt
    }
    
    MENU_ITEMS {
        string id PK
        string vendorId FK
        string categoryId FK
        string name
        string slug
        string description
        double price
        double compareAtPrice
        double cost
        string sku
        string barcode
        array images
        string category
        array tags
        json variants
        json options
        json nutritionInfo
        array allergens
        integer preparationTime
        integer inventory
        boolean trackInventory
        boolean available
        boolean isActive
        boolean isFeatured
        integer sortOrder
        double rating
        integer reviewCount
        integer salesCount
        datetime createdAt
        datetime updatedAt
    }
    
    ORDERS {
        string id PK
        string orderNumber UK
        string customerId FK
        string vendorId FK
        string runnerId FK
        json items
        double subtotal
        double discount
        double deliveryFee
        double serviceFee
        double tax
        double tip
        double total
        string currency
        string status
        string paymentMethod
        string paymentStatus
        string paymentIntentId
        json deliveryAddress
        string deliveryInstructions
        datetime estimatedDeliveryTime
        datetime actualDeliveryTime
        integer preparationTime
        string notes
        string cancellationReason
        double refundAmount
        string refundReason
        json trackingUpdates
        datetime createdAt
        datetime updatedAt
        datetime completedAt
    }
```

## Collection Schemas

### Core User Management

#### users
**Purpose**: Central user management for all user types (customers, vendors, runners, admins)

**Key Attributes**:
- `name` (string, required): User's full name
- `phone` (string, required, unique): Phone number for authentication
- `email` (string, optional, unique): Email address
- `role` (string, required): User role (customer, vendor, runner, admin, support)
- `status` (string, required): Account status (active, inactive, suspended, banned)
- `isVerified` (boolean, required): Phone verification status
- `preferences` (JSON): User preferences and settings

**Relationships**:
- One-to-Many with `addresses`
- One-to-Many with `vendors` (via ownerId)
- One-to-One with `runner_profiles`
- One-to-Many with `orders` (as customer)
- One-to-Many with `reviews`
- One-to-Many with `messages`
- One-to-Many with `notifications`

**Indexes**:
- `phone_unique`: Unique index on phone
- `email_unique`: Unique index on email
- `role_index`: Index on role for filtering
- `status_index`: Index on status for filtering

#### addresses
**Purpose**: Store customer delivery addresses

**Key Attributes**:
- `userId` (string, required): Reference to user
- `type` (string, required): Address type (home, work, other)
- `street` (string, required): Street address
- `city` (string, required): City name
- `province` (string, required): Province/state
- `postalCode` (string, required): Postal code
- `coordinates` (JSON): Latitude and longitude
- `isDefault` (boolean): Default address flag

**Usage Example**:
```typescript
import { databases, DATABASE_ID, COLLECTIONS } from '@/lib/appwrite';
import { parseUser, prepareAddressForDatabase } from '@hvppyplug/common';

// Create new address
const newAddress = {
  userId: user.$id,
  type: 'home',
  street: '123 Main Street',
  city: 'Johannesburg',
  province: 'Gauteng',
  postalCode: '2000',
  country: 'South Africa',
  coordinates: JSON.stringify({ lat: -26.2041, lng: 28.0473 }),
  isDefault: true,
  isActive: true,
};

const address = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.ADDRESSES,
  'unique()',
  newAddress
);
```

### Business Management

#### categories
**Purpose**: Hierarchical categorization of products and services

**Key Attributes**:
- `name` (string, required): Category name
- `slug` (string, required, unique): URL-friendly identifier
- `parentId` (string, optional): Parent category for hierarchy
- `sortOrder` (integer): Display order
- `icon` (string): Icon identifier
- `color` (string): Brand color
- `isActive` (boolean): Active status

**Usage Example**:
```typescript
// Create main category
const mainCategory = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.CATEGORIES,
  'unique()',
  {
    name: 'Food & Beverages',
    slug: 'food-beverages',
    description: 'All food and drink items',
    icon: '🍔',
    color: '#FF6B35',
    sortOrder: 1,
    isActive: true,
  }
);

// Create subcategory
const subCategory = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.CATEGORIES,
  'unique()',
  {
    name: 'Fast Food',
    slug: 'fast-food',
    description: 'Quick service restaurants',
    parentId: mainCategory.$id,
    icon: '🍟',
    color: '#FF6B35',
    sortOrder: 1,
    isActive: true,
  }
);
```

#### vendors
**Purpose**: Business profiles for service providers

**Key Attributes**:
- `name` (string, required): Business name
- `slug` (string, required, unique): URL-friendly identifier
- `ownerId` (string, required): Reference to owner user
- `businessType` (string, required): Type of business
- `status` (string, required): Verification status
- `rating` (double): Average rating
- `reviewCount` (integer): Number of reviews
- `deliveryRadius` (double): Delivery coverage in km
- `businessHours` (JSON): Operating hours

**Usage Example**:
```typescript
import { parseVendor, prepareVendorForDatabase } from '@hvppyplug/common';

// Create vendor profile
const vendorData = {
  name: 'Joe\'s Burger Joint',
  slug: 'joes-burger-joint',
  ownerId: user.$id,
  businessType: 'restaurant',
  description: 'Best burgers in Soweto',
  location: 'Soweto, Johannesburg',
  coordinates: JSON.stringify({ lat: -26.2678, lng: 27.8546 }),
  deliveryRadius: 15,
  status: 'pending',
  rating: 0,
  reviewCount: 0,
  deliveryFee: 25,
  minimumOrder: 50,
  isActive: true,
  isFeatured: false,
  businessHours: JSON.stringify({
    monday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
    tuesday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
    // ... other days
  }),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const vendor = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.VENDORS,
  'unique()',
  vendorData
);
```

#### menu-items
**Purpose**: Product catalog for vendors

**Key Attributes**:
- `vendorId` (string, required): Reference to vendor
- `categoryId` (string, optional): Reference to category
- `name` (string, required): Item name
- `price` (double, required): Item price
- `images` (array): Product images
- `variants` (JSON): Product variations
- `options` (JSON): Customization options
- `available` (boolean): Availability status

**Usage Example**:
```typescript
// Create menu item with variants and options
const menuItem = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.MENU_ITEMS,
  'unique()',
  {
    vendorId: vendor.$id,
    categoryId: category.$id,
    name: 'Classic Burger',
    slug: 'classic-burger',
    description: 'Beef patty with lettuce, tomato, and special sauce',
    price: 89.99,
    compareAtPrice: 99.99,
    images: ['burger1.jpg', 'burger2.jpg'],
    category: 'burgers',
    tags: ['beef', 'classic', 'popular'],
    variants: JSON.stringify([
      {
        name: 'Size',
        options: ['Regular', 'Large'],
        prices: { 'Large': 15 }
      }
    ]),
    options: JSON.stringify([
      {
        name: 'Add-ons',
        type: 'multiple',
        required: false,
        choices: [
          { name: 'Extra Cheese', price: 10 },
          { name: 'Bacon', price: 15 },
          { name: 'Avocado', price: 12 }
        ]
      }
    ]),
    preparationTime: 15,
    available: true,
    isActive: true,
    isFeatured: true,
    sortOrder: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
);
```

### Order Management

#### orders
**Purpose**: Order tracking and management

**Key Attributes**:
- `orderNumber` (string, required, unique): Human-readable order ID
- `customerId` (string, required): Reference to customer
- `vendorId` (string, required): Reference to vendor
- `runnerId` (string, optional): Reference to assigned runner
- `items` (JSON, required): Order items with details
- `total` (double, required): Final order total
- `status` (string, required): Order status
- `paymentStatus` (string, required): Payment status
- `deliveryAddress` (JSON, required): Delivery location

**Usage Example**:
```typescript
import { generateOrderNumber, calculateOrderTotal } from '@hvppyplug/common';

// Create new order
const orderData = {
  orderNumber: generateOrderNumber(),
  customerId: customer.$id,
  vendorId: vendor.$id,
  items: JSON.stringify([
    {
      itemId: menuItem.$id,
      name: 'Classic Burger',
      quantity: 2,
      price: 89.99,
      total: 179.98,
      options: [
        { name: 'Size', choice: 'Large', price: 15 },
        { name: 'Add-ons', choice: 'Extra Cheese', price: 10 }
      ]
    }
  ]),
  subtotal: 209.98,
  discount: 0,
  deliveryFee: 25,
  serviceFee: 5,
  tax: 0,
  tip: 20,
  total: 259.98,
  currency: 'ZAR',
  status: 'pending',
  paymentMethod: 'card',
  paymentStatus: 'pending',
  deliveryAddress: JSON.stringify({
    street: '123 Main Street',
    city: 'Johannesburg',
    province: 'Gauteng',
    postalCode: '2000',
    coordinates: { lat: -26.2041, lng: 28.0473 }
  }),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const order = await databases.createDocument(
  DATABASE_ID,
  COLLECTIONS.ORDERS,
  'unique()',
  orderData
);
```

## Query Patterns

### Common Queries

#### Get User Orders
```typescript
import { Query } from 'appwrite';

const userOrders = await databases.listDocuments(
  DATABASE_ID,
  COLLECTIONS.ORDERS,
  [
    Query.equal('customerId', userId),
    Query.orderDesc('createdAt'),
    Query.limit(20)
  ]
);
```

#### Get Vendor Menu Items
```typescript
const menuItems = await databases.listDocuments(
  DATABASE_ID,
  COLLECTIONS.MENU_ITEMS,
  [
    Query.equal('vendorId', vendorId),
    Query.equal('available', true),
    Query.equal('isActive', true),
    Query.orderAsc('sortOrder')
  ]
);
```

#### Search Vendors by Location
```typescript
const nearbyVendors = await databases.listDocuments(
  DATABASE_ID,
  COLLECTIONS.VENDORS,
  [
    Query.equal('status', 'active'),
    Query.equal('isActive', true),
    Query.search('location', 'Soweto'),
    Query.orderDesc('rating')
  ]
);
```

#### Get Active Promotions
```typescript
const activePromotions = await databases.listDocuments(
  DATABASE_ID,
  COLLECTIONS.PROMOTIONS,
  [
    Query.equal('isActive', true),
    Query.greaterThan('endsAt', new Date().toISOString()),
    Query.lessThan('startsAt', new Date().toISOString())
  ]
);
```

## Performance Optimization

### Indexing Strategy
1. **Primary Indexes**: All foreign keys are indexed
2. **Unique Indexes**: Phone, email, order numbers, promotion codes
3. **Composite Indexes**: Status + date combinations for filtering
4. **Search Indexes**: Full-text search on names and descriptions

### Query Optimization
1. **Limit Results**: Always use Query.limit() for pagination
2. **Specific Filters**: Use Query.equal() before Query.search()
3. **Date Ranges**: Use Query.greaterThan() and Query.lessThan() for date filtering
4. **Avoid N+1**: Batch related data queries when possible

### Caching Strategy
1. **Static Data**: Cache categories, vendor info for 1 hour
2. **Dynamic Data**: Cache user sessions for 15 minutes
3. **Real-time Data**: No caching for orders, messages, notifications

## Security Best Practices

### Permission Patterns
1. **User Data**: Users can only access their own data
2. **Public Data**: Vendors, menu items, categories are publicly readable
3. **Admin Data**: Analytics, support tickets require admin role
4. **Sensitive Data**: Payment info, personal details have restricted access

### Data Validation
1. **Input Sanitization**: All user inputs are validated and sanitized
2. **Type Checking**: Use TypeScript interfaces for compile-time safety
3. **Business Rules**: Validate business logic before database operations
4. **Rate Limiting**: Implement rate limiting for API endpoints

### Audit Trail
1. **Change Tracking**: All modifications are logged with timestamps
2. **User Actions**: Track user actions for security monitoring
3. **Data Access**: Log sensitive data access for compliance
4. **Error Monitoring**: Monitor and alert on database errors

## Development Guidelines

### Naming Conventions
1. **Collections**: kebab-case (e.g., `menu-items`)
2. **Attributes**: camelCase (e.g., `createdAt`)
3. **Indexes**: snake_case with suffix (e.g., `phone_unique`)
4. **Constants**: UPPER_SNAKE_CASE (e.g., `DATABASE_ID`)

### Code Organization
1. **Types**: Define in `packages/common/src/types/database.ts`
2. **Helpers**: Utility functions in `packages/common/src/utils/database-helpers.ts`
3. **Queries**: Reusable query functions in service files
4. **Validation**: Input validation in dedicated validator functions

### Testing Strategy
1. **Unit Tests**: Test helper functions and validators
2. **Integration Tests**: Test database operations with test data
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Test query performance under load

## Migration and Deployment

### Version Control
1. **Schema Changes**: Track in migration scripts
2. **Data Changes**: Document in migration notes
3. **Rollback Plans**: Always have rollback procedures
4. **Testing**: Test migrations on staging environment

### Deployment Process
1. **Backup**: Always backup before deployment
2. **Validation**: Verify schema after deployment
3. **Monitoring**: Monitor performance after changes
4. **Rollback**: Be prepared to rollback if issues arise

---

**Last Updated**: 2025-01-30  
**Schema Version**: Enhanced v2.0  
**Total Collections**: 18
