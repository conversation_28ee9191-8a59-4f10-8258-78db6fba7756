# EAS Build Guide for pnpm Monorepo

This guide explains how to successfully build Expo apps in a pnpm monorepo using EAS Build Service.

## Overview

The HVPPYPlug+ monorepo has been configured to work seamlessly with EAS Build Service despite using pnpm's symlink-based dependency management. This configuration ensures that all packages are properly hoisted and accessible during the build process.

## Configuration Files

### 1. .npmrc Configuration

The root `.npmrc` file contains essential settings for EAS Build compatibility:

```ini
# Essential for Expo/React Native compatibility
node-linker=hoisted
shamefully-hoist=true

# Workspace configuration
link-workspace-packages=true
prefer-workspace-packages=true

# Build optimizations
frozen-lockfile=false
prefer-frozen-lockfile=true
```

### 2. pnpm-workspace.yaml

Defines workspace packages and version catalogs:

```yaml
packages:
  - 'packages/*'
  - 'apps/*'
  - 'backend'

catalog:
  react: "19.0.0"
  react-native: "0.79.5"
  expo: "~53.0.20"
```

### 3. Metro Configuration

Each app has a `metro.config.js` that properly resolves monorepo packages:

```javascript
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');
const config = getDefaultConfig(projectRoot);

// Define monorepo packages
const monorepoPackages = {
  '@hvppyplug/common': path.resolve(monorepoRoot, 'packages/common'),
  '@hvppyplug/compound-components': path.resolve(monorepoRoot, 'packages/compound-components'),
  '@hvppyplug/mobile-services': path.resolve(monorepoRoot, 'packages/mobile-services'),
};

// Configure Metro for monorepo
config.watchFolders = [projectRoot, ...Object.values(monorepoPackages)];
config.resolver.extraNodeModules = monorepoPackages;
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];
config.resolver.disableHierarchicalLookup = true;

module.exports = config;
```

### 4. EAS Configuration

Each app's `eas.json` includes monorepo-specific settings:

```json
{
  "build": {
    "base": {
      "node": "20.11.1",
      "pnpm": "8.15.0",
      "env": {
        "EXPO_USE_METRO_WORKSPACE_ROOT": "1",
        "PNPM_CACHE_FOLDER": ".pnpm-cache",
        "CI": "1"
      },
      "cache": {
        "disabled": false,
        "cacheDefaultPaths": true,
        "customPaths": [
          "node_modules/.cache",
          "../../node_modules/.cache",
          ".pnpm-cache"
        ]
      }
    }
  }
}
```

## Build Process

### 1. Preparation

Before running EAS builds, prepare the monorepo:

```bash
# From monorepo root
./scripts/prepare-eas-build.sh
```

Or manually:

```bash
# Install dependencies
pnpm install --frozen-lockfile

# Build shared packages
pnpm --filter @hvppyplug/common build
pnpm --filter @hvppyplug/compound-components build
pnpm --filter @hvppyplug/mobile-services build
```

### 2. Building Apps

Build each app from its directory:

```bash
# Customer App
cd apps/customer-app
eas build --platform android --profile production

# Vendor App
cd apps/vendor-app
eas build --platform android --profile production

# Runner App
cd apps/runner-app
eas build --platform android --profile production
```

### 3. Development Builds

For development builds with faster iteration:

```bash
cd apps/customer-app
eas build --platform android --profile development
```

## Troubleshooting

### Common Issues

1. **"Cannot find symbol import expo.core.ExpoModulesPackage"**
   - Ensure `expo-modules-core` is properly installed
   - Run `pnpm install --shamefully-hoist`
   - Clear cache: `pnpm clean:cache`

2. **"Module not found" errors**
   - Verify `.npmrc` configuration
   - Check Metro configuration
   - Ensure shared packages are built

3. **Metro resolution errors**
   - Clear Metro cache: `pnpm expo start --clear`
   - Verify `EXPO_USE_METRO_WORKSPACE_ROOT=1` is set

### Build Scripts

Use these convenience scripts from the monorepo root:

```bash
# Prepare for builds
pnpm prepare:build

# Development builds
pnpm build:customer-dev
pnpm build:vendor-dev
pnpm build:runner-dev

# Production builds
pnpm build:android
pnpm build:ios
```

## Environment Variables

Key environment variables for EAS builds:

- `EXPO_USE_METRO_WORKSPACE_ROOT=1` - Enables Metro workspace detection
- `PNPM_CACHE_FOLDER=.pnpm-cache` - Configures pnpm cache location
- `CI=1` - Enables CI mode for consistent builds

## Best Practices

1. **Always use hoisted node-linker** for React Native compatibility
2. **Build shared packages first** before app builds
3. **Use frozen lockfile** for consistent dependency versions
4. **Clear caches** when encountering resolution issues
5. **Test locally** before running EAS builds
6. **Use development profiles** for faster iteration

## Performance Tips

1. Enable caching in EAS configuration
2. Use specific watch folders in Metro config
3. Build shared packages in parallel when possible
4. Use pnpm's built-in caching features

This configuration ensures reliable EAS builds while maintaining the benefits of a pnpm monorepo structure.
