
# HVPPYPlug+ Database Deployment Instructions

## Prerequisites
1. Install Appwrite CLI: `npm install -g appwrite-cli`
2. Login to Appwrite: `appwrite login`
3. Set project context: `appwrite init project`

## Deployment Commands

### Option 1: Deploy Complete Configuration
```bash
cd backend
appwrite deploy --force
```

### Option 2: Deploy Collections Only
```bash
cd backend
appwrite deploy collection --force
```

### Option 3: Deploy Individual Collections
```bash
# Deploy specific collections
appwrite deploy collection users --force
appwrite deploy collection vendors --force
# ... repeat for other collections
```

## Post-Deployment Steps

1. **Verify Collections**: Check Appwrite console for all collections
2. **Test Permissions**: Verify role-based access control
3. **Create Initial Data**: Add categories, admin users, etc.
4. **Update Environment Variables**: Ensure all apps have correct database IDs

## Rollback Instructions

If deployment fails:
1. Check Appwrite console for error details
2. Fix configuration issues
3. Re-run deployment with `--force` flag
4. For critical issues, restore from backup

## Monitoring

- Monitor collection performance in Appwrite console
- Set up alerts for database errors
- Regular backup of critical data
- Performance optimization based on usage patterns

Generated on: 2025-07-30T15:54:39.177Z
Total Collections: 18
