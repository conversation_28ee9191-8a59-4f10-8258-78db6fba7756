# pnpm configuration for Expo monorepo compatibility
# This ensures packages are hoisted to work with Metro bundler
node-linker=hoisted

# Enable shameful hoisting for better compatibility with React Native
shamefully-hoist=true

# Auto install peer dependencies
auto-install-peers=true

# Strict peer dependencies (can be disabled if causing issues)
strict-peer-dependencies=false

# EAS Build optimizations
prefer-workspace-packages=true
link-workspace-packages=true

# Workspace configuration for monorepo
enable-pre-post-scripts=true
recursive-install=true

# Dependency resolution optimizations
resolution-mode=highest
dedupe-peer-dependents=true

# Build and installation optimizations
frozen-lockfile=false
prefer-frozen-lockfile=true

# Cache settings for faster builds
store-dir=~/.pnpm-store
cache-dir=~/.pnpm-cache

# Registry and network settings
registry=https://registry.npmjs.org/
network-timeout=300000

# EAS Build specific settings
# Ensure proper package resolution for React Native
hoist-pattern[]=*eslint*
hoist-pattern[]=*prettier*
hoist-pattern[]=@babel/*
hoist-pattern[]=@types/*

# Logging for debugging build issues
loglevel=info
