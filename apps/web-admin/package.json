{"name": "@hvppyplug/web-admin", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "clean": "rm -rf .next", "type-check": "tsc --noEmit", "lint": "next lint"}, "dependencies": {"@hvppyplug/common": "workspace:*", "react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.3", "@tanstack/react-query": "^5.28.4", "zustand": "^4.5.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.2", "lucide-react": "^0.363.0", "appwrite": "^16.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-tabs": "^1.0.4", "react-hook-form": "^7.51.0", "@hookform/resolvers": "^3.3.4", "zod": "^3.22.4", "recharts": "^2.12.2", "date-fns": "^3.6.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.3", "@eslint/eslintrc": "^3"}}