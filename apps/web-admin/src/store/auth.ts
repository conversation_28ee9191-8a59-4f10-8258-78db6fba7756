import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService, type User, type AuthUser } from '@/lib/auth';

interface AuthState {
  user: AuthUser | null;
  userProfile: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      userProfile: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const { session, user: userProfile } = await authService.login(email, password);
          const user = await authService.getCurrentUser();
          
          set({
            user,
            userProfile,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        
        try {
          await authService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      checkAuth: async () => {
        set({ isLoading: true });
        
        try {
          const user = await authService.getCurrentUser();
          
          if (user) {
            const userProfile = await authService.getUserProfile(user.$id);
            
            if (userProfile && userProfile.role === 'admin') {
              set({
                user,
                userProfile,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              });
            } else {
              // Not an admin, logout
              await authService.logout();
              set({
                user: null,
                userProfile: null,
                isAuthenticated: false,
                isLoading: false,
                error: 'Access denied. Admin privileges required.',
              });
            }
          } else {
            set({
              user: null,
              userProfile: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          }
        } catch (error) {
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Authentication check failed',
          });
        }
      },

      clearError: () => set({ error: null }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        userProfile: state.userProfile,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
