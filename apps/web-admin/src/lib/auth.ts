import { account, databases, DATABASE_ID, COLLECTIONS, USER_ROLES } from './appwrite';
import { ID, Query } from 'appwrite';

export interface User {
  $id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  avatarUrl?: string;
  $createdAt: string;
  $updatedAt: string;
}

export interface AuthUser {
  $id: string;
  name: string;
  email: string;
  emailVerification: boolean;
  phoneVerification: boolean;
  prefs: Record<string, any>;
  registration: string;
  status: boolean;
}

class AuthService {
  // Get current session
  async getCurrentSession() {
    try {
      return await account.getSession('current');
    } catch (error) {
      return null;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      return await account.get();
    } catch (error) {
      return null;
    }
  }

  // Get user profile from database
  async getUserProfile(userId: string): Promise<User | null> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      return response as User;
    } catch (error) {
      return null;
    }
  }

  // Login with email and password
  async login(email: string, password: string) {
    try {
      const session = await account.createEmailPasswordSession(email, password);
      
      // Get user profile to check if admin
      const userProfile = await this.getUserProfile(session.userId);
      
      if (!userProfile || userProfile.role !== USER_ROLES.ADMIN) {
        await this.logout();
        throw new Error('Access denied. Admin privileges required.');
      }
      
      return { session, user: userProfile };
    } catch (error) {
      throw error;
    }
  }

  // Logout
  async logout() {
    try {
      await account.deleteSession('current');
    } catch (error) {
      // Session might already be expired
      console.error('Logout error:', error);
    }
  }

  // Create admin user (for initial setup)
  async createAdminUser(email: string, password: string, name: string, phone: string) {
    try {
      // Create account
      const user = await account.create(ID.unique(), email, password, name);
      
      // Create user profile in database
      const userProfile = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        user.$id,
        {
          name,
          phone,
          role: USER_ROLES.ADMIN,
        }
      );
      
      return { user, userProfile };
    } catch (error) {
      throw error;
    }
  }

  // Check if user is admin
  async isAdmin(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return false;
      
      const profile = await this.getUserProfile(user.$id);
      return profile?.role === USER_ROLES.ADMIN;
    } catch (error) {
      return false;
    }
  }

  // Get all users with pagination
  async getUsers(limit = 25, offset = 0, search?: string) {
    try {
      const queries = [
        Query.limit(limit),
        Query.offset(offset),
        Query.orderDesc('$createdAt')
      ];

      if (search) {
        queries.push(Query.search('name', search));
      }

      return await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.USERS,
        queries
      );
    } catch (error) {
      throw error;
    }
  }

  // Update user role
  async updateUserRole(userId: string, role: string) {
    try {
      return await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        { role }
      );
    } catch (error) {
      throw error;
    }
  }

  // Delete user
  async deleteUser(userId: string) {
    try {
      // Delete from database
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      
      // Note: Cannot delete from auth service directly via client SDK
      // This would need to be done via server-side function
      return true;
    } catch (error) {
      throw error;
    }
  }
}

export const authService = new AuthService();
