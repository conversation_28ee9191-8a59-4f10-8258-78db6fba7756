import React, { useEffect } from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { ThemeProvider } from './src/providers/ThemeProvider'
import { ErrorBoundary } from './src/components/ErrorBoundary'
import { RootNavigator } from './src/navigation/RootNavigator'
import { notificationService } from './src/services/notificationService'
import { offlineService } from './src/services/offlineService'
import { useAuthStore } from './src/stores/authStore'
import { getCurrentUser } from './src/lib/appwrite'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
    },
  },
})

export default function App() {
  useEffect(() => {
    // Initialize services and check authentication
    const initializeApp = async () => {
      try {
        // Initialize services
        await Promise.all([
          notificationService.initialize(),
          offlineService.initialize()
        ])

        // Check if user is already authenticated
        const user = await getCurrentUser()
        if (user) {
          // Update auth store with current user
          useAuthStore.getState().refreshUser()
        }

        console.log('✅ App initialized successfully')
      } catch (error) {
        console.error('❌ Failed to initialize app:', error)
      }
    }

    initializeApp()
  }, [])

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider>
              <RootNavigator />
            </ThemeProvider>
          </QueryClientProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  )
}
