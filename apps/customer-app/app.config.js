import 'dotenv/config';

export default {
  expo: {
    name: "HVPPYPlug+ Customer",
    slug: "hvppyplug-customer",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "automatic",
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#F97316",
    },
    assetBundlePatterns: ["**/*"],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.hvppyplug.customer",
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#F97316",
      },
      package: "com.hvppyplug.customer",
    },
    web: {
      favicon: "./assets/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission:
            "This app needs access to location to show nearby services and enable delivery.",
          locationAlwaysPermission:
            "This app needs access to location to track delivery progress.",
          locationWhenInUsePermission:
            "This app needs access to location to show nearby services.",
        },
      ],
      [
        "expo-notifications",
        {
          icon: "./assets/notification-icon.png",
          color: "#F97316",
          defaultChannel: "default",
        },
      ],
      [
        "expo-camera",
        {
          cameraPermission:
            "This app needs access to camera to take photos for your profile and orders.",
        },
      ],
      [
        "expo-image-picker",
        {
          photosPermission:
            "This app needs access to your photos to upload images for your profile and orders.",
        },
      ],
      "expo-secure-store",
      "expo-local-authentication",
    ],
    extra: {
      // Appwrite Configuration
      appwriteEndpoint:
        process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ||
        "https://cloud.appwrite.io/v1",
      appwriteProjectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || "",
      appwriteDatabaseId:
        process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || "hvppyplug-main",
      appwriteStorageId:
        process.env.EXPO_PUBLIC_APPWRITE_STORAGE_ID || "images",

      // Environment
      environment: process.env.NODE_ENV || "development",

      // Feature Flags
      enablePushNotifications:
        process.env.EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS === "true",
      enableLocationTracking:
        process.env.EXPO_PUBLIC_ENABLE_LOCATION_TRACKING === "true",
      enableOfflineMode: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MODE === "true",

      eas: {
        projectId: "fc7430a2-75ff-4106-bb18-b4377e60ca24",
      },
    },
  },
};
