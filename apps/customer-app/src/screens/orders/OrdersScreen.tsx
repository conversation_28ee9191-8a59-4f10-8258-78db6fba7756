import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, RefreshControl, TouchableOpacity, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { useOrdersStore } from '../../stores/ordersStore'
import { OrderCard, ActiveOrdersList } from '../../components/orders'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import { EmptyState } from '../../components/common/EmptyState'
import type { Order } from '../../components/orders/types'

export function OrdersScreen() {
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const {
    orders,
    activeOrders,
    orderHistory,
    isLoading,
    error,
    getOrders,
    clearError
  } = useOrdersStore()

  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState<'active' | 'history'>('active')

  useEffect(() => {
    if (user?.id) {
      loadOrders()
    }
  }, [user?.id])

  const loadOrders = async () => {
    if (!user?.id) return

    try {
      clearError()
      await getOrders({ customerId: user.id })
    } catch (error) {
      console.error('Failed to load orders:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadOrders()
    setRefreshing(false)
  }

  const handleOrderPress = (order: Order) => {
    // Navigate to order detail screen
    console.log('Navigate to order detail:', order.id)
  }

  const handleTrackOrder = (order: Order) => {
    // Navigate to order tracking screen
    console.log('Track order:', order.id)
  }

  const handleContactVendor = (order: Order) => {
    // Open phone dialer or chat
    console.log('Contact vendor:', order.vendor.phone)
  }

  const renderTabButton = (tab: 'active' | 'history', label: string, count: number) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: activeTab === tab ? colors.primary[500] : colors.background.tertiary,
          borderColor: activeTab === tab ? colors.primary[500] : colors.border.primary
        }
      ]}
      onPress={() => setActiveTab(tab)}
      activeOpacity={0.7}
    >
      <Text style={[
        typography.body.medium,
        {
          color: activeTab === tab ? colors.white : colors.text.primary,
          fontWeight: activeTab === tab ? '600' : '400'
        }
      ]}>
        {label} ({count})
      </Text>
    </TouchableOpacity>
  )

  const renderContent = () => {
    if (isLoading && !refreshing) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner />
          <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 16 }]}>
            Loading your orders...
          </Text>
        </View>
      )
    }

    const ordersToShow = activeTab === 'active' ? activeOrders : orderHistory

    if (ordersToShow.length === 0) {
      return (
        <EmptyState
          title={activeTab === 'active' ? 'No active orders' : 'No order history'}
          description={
            activeTab === 'active'
              ? 'When you place an order, it will appear here.'
              : 'Your completed orders will appear here.'
          }
          icon={activeTab === 'active' ? '📋' : '📜'}
          style={styles.emptyState}
        />
      )
    }

    return (
      <ScrollView
        style={styles.ordersList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
            colors={[colors.primary[500]]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {ordersToShow.map((order) => (
          <OrderCard
            key={order.id}
            order={order}
            onPress={handleOrderPress}
            onTrack={activeTab === 'active' ? handleTrackOrder : undefined}
            onContact={handleContactVendor}
            showActions={activeTab === 'active'}
          />
        ))}
      </ScrollView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.header}>
        <Text style={[typography.display.small, { color: colors.text.primary }]}>
          My Orders
        </Text>

        <View style={[styles.tabs, { marginTop: 16 }]}>
          {renderTabButton('active', 'Active', activeOrders.length)}
          {renderTabButton('history', 'History', orderHistory.length)}
        </View>
      </View>

      {error && (
        <View style={[styles.errorContainer, { backgroundColor: colors.error[50] }]}>
          <Text style={[typography.body.small, { color: colors.error[700] }]}>
            {error}
          </Text>
        </View>
      )}

      <View style={styles.content}>
        {renderContent()}
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 0,
  },
  tabs: {
    flexDirection: 'row',
    gap: 8,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  errorContainer: {
    margin: 16,
    padding: 12,
    borderRadius: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    padding: 32,
  },
  ordersList: {
    flex: 1,
    padding: 16,
  },
})
