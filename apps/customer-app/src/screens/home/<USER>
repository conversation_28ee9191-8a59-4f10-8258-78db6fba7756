import React, { useState, useEffect } from 'react'
import { View, ScrollView, RefreshControl, Alert, Text, TouchableOpacity } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useLocation } from '@hvppyplug/mobile-services'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { useRestaurantStore } from '../../stores/restaurantStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import { SkeletonList } from '../../components/SkeletonLoader'
import { RestaurantCard } from '../../components/restaurants'
import type { Restaurant } from '../../components/restaurants/types'

import type { HomeStackScreenProps } from '../../types/navigation'

type Props = HomeStackScreenProps<'HomeMain'>

export function HomeScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const {
    restaurants,
    featuredRestaurants,
    nearbyRestaurants,
    isLoading,
    error,
    getRestaurants,
    getFeaturedRestaurants,
    getNearbyRestaurants,
    clearError
  } = useRestaurantStore()

  const [refreshing, setRefreshing] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  // Location hook for getting user's current location
  const {
    currentLocation,
    getCurrentLocation,
    isLoading: locationLoading,
  } = useLocation({
    enableBackgroundLocation: false,
    enableAppwriteSync: true,
  })

  useEffect(() => {
    initializeData()
  }, [])

  useEffect(() => {
    if (currentLocation?.coords) {
      // Get nearby restaurants when location is available
      getNearbyRestaurants(
        currentLocation.coords.latitude,
        currentLocation.coords.longitude,
        10 // 10km radius
      )
    }
  }, [currentLocation])

  const initializeData = async () => {
    try {
      clearError()

      // Get current location
      await getCurrentLocation()

      // Load initial data
      await Promise.all([
        getFeaturedRestaurants(),
        getRestaurants({ refresh: true })
      ])
    } catch (err: any) {
      console.error('Failed to initialize home screen data:', err)
      Alert.alert('Error', 'Failed to load data. Please try again.')
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await initializeData()
    setRefreshing(false)
  }

  const handleRestaurantPress = (restaurant: Restaurant) => {
    // Navigate to restaurant detail screen
    navigation.navigate('ProviderProfile', { providerId: restaurant.id })
  }

  const handleNavigateToSearch = () => {
    // Navigate to search screen - this will need to be implemented in navigation
    console.log('Navigate to search')
  }

  const handleNavigateToLocation = () => {
    // Navigate to location picker - this will need to be implemented in navigation
    console.log('Navigate to location picker')
  }

  // Get the restaurants to display
  const displayRestaurants = nearbyRestaurants.length > 0 ? nearbyRestaurants : featuredRestaurants
  const categoryNames = ['Food', 'Groceries', 'Pharmacy', 'Electronics'] // Static categories for now

  // Show loading state
  if (isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <SkeletonList count={5} style={{ padding: 16 }} />
      </SafeAreaView>
    )
  }

  // Show error state
  if (error && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 }}>
          <Text style={[typography.body.large, { color: colors.text.primary, textAlign: 'center', marginBottom: 16 }]}>
            Something went wrong
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginBottom: 24 }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary[500],
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 8,
            }}
            onPress={initializeData}
          >
            <Text style={[typography.label.medium, { color: colors.white }]}>
              Try Again
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Header */}
        <View style={{ padding: 16, backgroundColor: colors.background.secondary }}>
          <Text style={[typography.display.small, { color: colors.text.primary }]}>
            Welcome back, {user?.name || 'Guest'}!
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 4 }]}>
            What service do you need today?
          </Text>
        </View>

        {/* Location Header */}
        <View style={{
          padding: 16,
          backgroundColor: colors.primary[50],
          borderBottomWidth: 1,
          borderBottomColor: colors.border.primary
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <Text style={[typography.body.small, { color: colors.text.secondary }]}>
                Delivering to
              </Text>
              <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                {currentLocation?.coords ? 'Current Location' : 'Select delivery location'}
              </Text>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary[500],
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 6,
              }}
              onPress={handleNavigateToLocation}
            >
              <Text style={[typography.label.small, { color: colors.white }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View style={{ padding: 16 }}>
          <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }]}>
            Categories
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              {['All', ...categoryNames].map((category) => (
                <TouchableOpacity
                  key={category}
                  style={{
                    backgroundColor: selectedCategory === category ? colors.primary[500] : colors.background.tertiary,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    borderWidth: 1,
                    borderColor: selectedCategory === category ? colors.primary[500] : colors.border.primary,
                  }}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    typography.label.small,
                    {
                      color: selectedCategory === category ? colors.white : colors.text.primary,
                      fontWeight: selectedCategory === category ? '600' : '400'
                    }
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Restaurants */}
        <View style={{ padding: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
            <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600' }]}>
              {nearbyRestaurants.length > 0 ? 'Nearby Restaurants' : 'Featured Restaurants'}
            </Text>
            <View style={{
              backgroundColor: colors.primary[100],
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
            }}>
              <Text style={[typography.body.small, { color: colors.primary[700] }]}>
                {displayRestaurants.length} available
              </Text>
            </View>
          </View>

          {displayRestaurants.length === 0 ? (
            <View style={{
              backgroundColor: colors.background.tertiary,
              padding: 24,
              borderRadius: 12,
              alignItems: 'center'
            }}>
              <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center' }]}>
                No restaurants available in your area yet.
              </Text>
              <Text style={[typography.body.small, { color: colors.text.tertiary, textAlign: 'center', marginTop: 4 }]}>
                We're working to bring more restaurants to your location.
              </Text>
            </View>
          ) : (
            <View style={{ gap: 12 }}>
              {displayRestaurants.slice(0, 5).map((restaurant) => (
                <RestaurantCard
                  key={restaurant.id}
                  restaurant={restaurant}
                  onPress={handleRestaurantPress}
                  variant="default"
                  showDistance={true}
                  showDeliveryFee={true}
                />
              ))}

              {displayRestaurants.length > 5 && (
                <TouchableOpacity
                  style={{
                    backgroundColor: colors.primary[50],
                    borderRadius: 12,
                    padding: 16,
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: colors.primary[200],
                  }}
                  onPress={handleNavigateToSearch}
                >
                  <Text style={[typography.body.medium, { color: colors.primary[600], fontWeight: '600' }]}>
                    View All {displayRestaurants.length} Restaurants
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
