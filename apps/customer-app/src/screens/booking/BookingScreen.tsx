import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Alert, TextInput } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserIcon,
  MessageSquareIcon,
  CreditCardIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useServicesStore } from '../../stores/servicesStore'
import { useOrdersStore } from '../../stores/ordersStore'
import { useAuthStore } from '../../stores/authStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'

// Import UI components from the library
// import { MultiStepForm, type FormStep } from '@hvppyplug/ui-components-v2'
// import { DateTimePicker } from '@hvppyplug/ui-components-v2'
// import { PaymentMethodSelector } from '@hvppyplug/ui-components-v2'

// Temporary placeholder components and types
type FormStep = {
  id: string
  title: string
  component: React.ComponentType<any>
}

const MultiStepForm = ({ steps, onComplete }: { steps: FormStep[]; onComplete: (data: any) => void }) => {
  return (
    <View style={{ flex: 1, backgroundColor: 'white', padding: 16 }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', marginBottom: 16 }}>Multi-Step Form Placeholder</Text>
      <Text style={{ color: '#666' }}>Form steps would be rendered here</Text>
    </View>
  )
}

const DateTimePicker = ({ value, onChange, mode }: any) => {
  return (
    <View style={{ padding: 16, backgroundColor: '#f0f0f0', borderRadius: 8 }}>
      <Text style={{ fontWeight: '500', marginBottom: 8 }}>Date & Time Picker</Text>
      <Text style={{ color: '#666' }}>Picker placeholder</Text>
    </View>
  )
}

const PaymentMethodSelector = ({ selectedMethod, onMethodChange, methods }: any) => {
  return (
    <View style={{ padding: 16, backgroundColor: '#f0f0f0', borderRadius: 8 }}>
      <Text style={{ fontWeight: '500', marginBottom: 8 }}>Payment Method</Text>
      <Text style={{ color: '#666' }}>Payment selector placeholder</Text>
    </View>
  )
}

import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Booking'>

export function BookingScreen({ navigation, route }: Props) {
  const { serviceId, providerId } = route.params
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const { getService, isLoading } = useServicesStore()
  const { createOrder, isLoading: orderLoading } = useOrdersStore()

  const [service, setService] = useState<any>(null)
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null)
  const [selectedAddress, setSelectedAddress] = useState<any>(null)
  const [notes, setNotes] = useState('')
  const [currentStep, setCurrentStep] = useState<'datetime' | 'location' | 'details' | 'payment'>('datetime')

  useEffect(() => {
    loadService()
    // Set default address if user has addresses
    if (user?.addresses && user.addresses.length > 0) {
      setSelectedAddress(user.addresses[0])
    }
  }, [serviceId])

  const loadService = async () => {
    try {
      const serviceData = await getService(serviceId)
      setService(serviceData)
    } catch (error) {
      Alert.alert('Error', 'Failed to load service details')
      navigation.goBack()
    }
  }

  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 8; hour <= 17; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 17) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const handleBooking = async () => {
    if (!selectedTimeSlot || !selectedAddress) {
      Alert.alert('Missing Information', 'Please select a time slot and address')
      return
    }

    try {
      const orderData = {
        customerId: user!.id,
        providerId,
        serviceId,
        status: 'pending' as const,
        items: [{
          id: service.id,
          serviceId: service.id,
          serviceName: service.name,
          description: service.description,
          quantity: 1,
          unitPrice: service.price.amount,
          totalPrice: service.price.amount,
        }],
        totalAmount: service.price.amount,
        currency: 'ZAR',
        paymentStatus: 'pending' as const,
        deliveryAddress: selectedAddress,
        estimatedDuration: service.duration,
        scheduledDate: `${selectedDate}T${selectedTimeSlot}:00`,
        notes,
      }

      const order = await createOrder(orderData)

      Alert.alert(
        'Booking Confirmed!',
        'Your service has been booked successfully. You will receive a confirmation shortly.',
        [
          {
            text: 'View Order',
            onPress: () => navigation.navigate('OrderTracking', { orderId: order.id })
          }
        ]
      )
    } catch (error: any) {
      Alert.alert('Booking Failed', error.message)
    }
  }

  if (isLoading || !service) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading booking details..." />
      </SafeAreaView>
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeftIcon size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginLeft: 16 }
        ]}>
          Book Service
        </Text>
      </View>

      {/* Service Summary */}
      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 4 }
        ]}>
          {service.name}
        </Text>
        <Text style={[
          typography.body.medium,
          { color: colors.text.secondary, marginBottom: 8 }
        ]}>
          {service.provider.name}
        </Text>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text style={[
            typography.body.small,
            { color: colors.text.tertiary }
          ]}>
            Duration: {service.duration} minutes
          </Text>
          <Text style={[
            typography.body.large,
            { color: colors.primary[600], fontWeight: '700' }
          ]}>
            R{service.price.amount}
          </Text>
        </View>
      </View>
    </View>
  )

  const renderStepIndicator = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: colors.background.primary,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {[
        { key: 'datetime', label: 'Date & Time', icon: CalendarIcon },
        { key: 'location', label: 'Location', icon: MapPinIcon },
        { key: 'details', label: 'Details', icon: MessageSquareIcon },
        { key: 'payment', label: 'Payment', icon: CreditCardIcon }
      ].map((step, index) => {
        const isActive = step.key === currentStep
        const isCompleted = ['datetime', 'location', 'details', 'payment'].indexOf(currentStep) > index

        return (
          <View key={step.key} style={{ flex: 1, alignItems: 'center' }}>
            <View style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: isActive ? colors.primary[500] : isCompleted ? colors.success[500] : colors.background.tertiary,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 4,
            }}>
              <step.icon
                size={16}
                color={isActive || isCompleted ? colors.white : colors.text.tertiary}
              />
            </View>
            <Text style={[
              typography.label.small,
              {
                color: isActive ? colors.primary[500] : isCompleted ? colors.success[500] : colors.text.tertiary,
                textAlign: 'center'
              }
            ]}>
              {step.label}
            </Text>
          </View>
        )
      })}
    </View>
  )

  const renderDateTimeStep = () => (
    <View style={{ padding: 16 }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 16 }
      ]}>
        Select Date & Time
      </Text>

      {/* Date Selection */}
      <View style={{ marginBottom: 24 }}>
        <Text style={[
          typography.body.medium,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
        ]}>
          Date
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 16,
            borderWidth: 1,
            borderColor: colors.border.primary,
          }}
        >
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary }
          ]}>
            {new Date(selectedDate).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Time Slots */}
      <View>
        <Text style={[
          typography.body.medium,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
        ]}>
          Available Time Slots
        </Text>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
          {generateTimeSlots().map((slot) => (
            <TouchableOpacity
              key={slot}
              style={{
                backgroundColor: selectedTimeSlot === slot ? colors.primary[500] : colors.background.secondary,
                borderWidth: 1,
                borderColor: selectedTimeSlot === slot ? colors.primary[500] : colors.border.primary,
                borderRadius: 8,
                paddingHorizontal: 16,
                paddingVertical: 8,
              }}
              onPress={() => setSelectedTimeSlot(slot)}
            >
              <Text style={[
                typography.body.medium,
                {
                  color: selectedTimeSlot === slot ? colors.white : colors.text.primary,
                  fontWeight: selectedTimeSlot === slot ? '600' : '400'
                }
              ]}>
                {slot}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  )

  const renderLocationStep = () => (
    <View style={{ padding: 16 }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 16 }
      ]}>
        Service Location
      </Text>

      {user?.addresses && user.addresses.length > 0 ? (
        <View>
          {user.addresses.map((address, index) => (
            <TouchableOpacity
              key={address.id}
              style={{
                backgroundColor: selectedAddress?.id === address.id ? colors.primary[50] : colors.background.secondary,
                borderWidth: 1,
                borderColor: selectedAddress?.id === address.id ? colors.primary[500] : colors.border.primary,
                borderRadius: 12,
                padding: 16,
                marginBottom: 12,
              }}
              onPress={() => setSelectedAddress(address)}
            >
              <Text style={[
                typography.body.medium,
                { color: colors.text.primary, fontWeight: '600', marginBottom: 4 }
              ]}>
                {address.label}
              </Text>
              <Text style={[
                typography.body.small,
                { color: colors.text.secondary }
              ]}>
                {address.street}, {address.city}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        <View style={{
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 24,
          alignItems: 'center',
        }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, textAlign: 'center', marginBottom: 16 }
          ]}>
            No saved addresses found
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary[500],
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 8,
            }}
            onPress={() => navigation.navigate('MainTabs', {
              screen: 'Profile',
              params: {
                screen: 'AddAddress'
              }
            } as never)}
          >
            <Text style={[typography.label.medium, { color: colors.white }]}>
              Add Address
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  )

  const renderDetailsStep = () => (
    <View style={{ padding: 16 }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 16 }
      ]}>
        Additional Details
      </Text>

      <View style={{ marginBottom: 16 }}>
        <Text style={[
          typography.body.medium,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
        ]}>
          Special Instructions (Optional)
        </Text>
        <TextInput
          style={{
            backgroundColor: colors.background.secondary,
            borderWidth: 1,
            borderColor: colors.border.primary,
            borderRadius: 12,
            padding: 16,
            minHeight: 100,
            textAlignVertical: 'top',
            color: colors.text.primary,
          }}
          placeholder="Any special instructions for the service provider..."
          placeholderTextColor={colors.text.tertiary}
          value={notes}
          onChangeText={setNotes}
          multiline
        />
      </View>

      {/* Booking Summary */}
      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <Text style={[
          typography.body.medium,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Booking Summary
        </Text>

        <View style={{ gap: 8 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              Service:
            </Text>
            <Text style={[typography.body.small, { color: colors.text.primary }]}>
              {service.name}
            </Text>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              Date & Time:
            </Text>
            <Text style={[typography.body.small, { color: colors.text.primary }]}>
              {new Date(selectedDate).toLocaleDateString()} at {selectedTimeSlot}
            </Text>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              Location:
            </Text>
            <Text style={[typography.body.small, { color: colors.text.primary }]}>
              {selectedAddress?.label}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            borderTopWidth: 1,
            borderTopColor: colors.border.primary,
            paddingTop: 8,
            marginTop: 8
          }}>
            <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
              Total:
            </Text>
            <Text style={[typography.body.medium, { color: colors.primary[600], fontWeight: '700' }]}>
              R{service.price.amount}
            </Text>
          </View>
        </View>
      </View>
    </View>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'datetime':
        return renderDateTimeStep()
      case 'location':
        return renderLocationStep()
      case 'details':
        return renderDetailsStep()
      case 'payment':
        return (
          <View style={{ padding: 16 }}>
            <Text style={[typography.body.large, { color: colors.text.primary, textAlign: 'center' }]}>
              Payment integration coming soon
            </Text>
          </View>
        )
      default:
        return renderDateTimeStep()
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 'datetime':
        return selectedTimeSlot !== null
      case 'location':
        return selectedAddress !== null
      case 'details':
        return true
      case 'payment':
        return true
      default:
        return false
    }
  }

  const handleNext = () => {
    const steps = ['datetime', 'location', 'details', 'payment']
    const currentIndex = steps.indexOf(currentStep)

    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1] as any)
    } else {
      handleBooking()
    }
  }

  const handleBack = () => {
    const steps = ['datetime', 'location', 'details', 'payment']
    const currentIndex = steps.indexOf(currentStep)

    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1] as any)
    }
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderStepIndicator()}
        {renderCurrentStep()}
      </ScrollView>

      {/* Action Buttons */}
      <View style={{
        backgroundColor: colors.background.primary,
        borderTopWidth: 1,
        borderTopColor: colors.border.primary,
        padding: 16,
      }}>
        <View style={{ flexDirection: 'row', gap: 12 }}>
          {currentStep !== 'datetime' && (
            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: colors.background.secondary,
                borderWidth: 1,
                borderColor: colors.border.primary,
                borderRadius: 12,
                paddingVertical: 16,
                alignItems: 'center',
              }}
              onPress={handleBack}
            >
              <Text style={[typography.label.medium, { color: colors.text.primary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={{
              flex: currentStep === 'datetime' ? 1 : 2,
              backgroundColor: canProceed() ? colors.primary[500] : colors.gray[400],
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
            }}
            onPress={handleNext}
            disabled={!canProceed() || orderLoading}
          >
            {orderLoading ? (
              <LoadingSpinner size="small" color={colors.white} />
            ) : (
              <Text style={[typography.label.medium, { color: colors.white, fontWeight: '600' }]}>
                {currentStep === 'payment' ? 'Confirm Booking' : 'Next'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  )
}
