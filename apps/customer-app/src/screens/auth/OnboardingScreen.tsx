import React, { useState, useRef } from 'react'
import { Dimensions, StyleSheet } from 'react-native'
import {
  VStack,
  HStack,
  Box,
  Text,
  Button,
  ButtonText,
  ScrollView,
  SafeAreaView,
  Image,
} from '@hvppyplug/ui-components-v2'
import {
  HomeIcon,
  ShieldCheckIcon,
  ClockIcon,
  StarIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from 'lucide-react-native'
import { useAuthStore } from '../../stores/authStore'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Onboarding'>

interface OnboardingSlide {
  id: string
  title: string
  subtitle: string
  description: string
  icon: React.ComponentType<any>
  color: string
}

const { width } = Dimensions.get('window')

const onboardingSlides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Welcome to HVPPYPlug+',
    subtitle: 'Your Service Marketplace',
    description: 'Connect with trusted service providers in your area for all your home and business needs.',
    icon: HomeIcon,
    color: '#F97316',
  },
  {
    id: '2',
    title: 'Trusted Professionals',
    subtitle: 'Verified & Insured',
    description: 'All our service providers are background-checked, verified, and insured for your peace of mind.',
    icon: ShieldCheckIcon,
    color: '#10B981',
  },
  {
    id: '3',
    title: 'Book Instantly',
    subtitle: 'Quick & Easy',
    description: 'Book services instantly or schedule for later. Track your service provider in real-time.',
    icon: ClockIcon,
    color: '#3B82F6',
  },
  {
    id: '4',
    title: 'Quality Guaranteed',
    subtitle: 'Rated & Reviewed',
    description: 'Read reviews from real customers and rate your experience to help others make informed choices.',
    icon: StarIcon,
    color: '#8B5CF6',
  },
]

export function OnboardingScreen({ navigation }: Props) {
  const { completeOnboarding } = useAuthStore()
  const [currentSlide, setCurrentSlide] = useState(0)
  const scrollViewRef = useRef<ScrollView>(null)

  const handleNext = () => {
    if (currentSlide < onboardingSlides.length - 1) {
      const nextSlide = currentSlide + 1
      setCurrentSlide(nextSlide)
      scrollViewRef.current?.scrollTo({
        x: nextSlide * width,
        animated: true,
      })
    }
  }

  const handlePrevious = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1
      setCurrentSlide(prevSlide)
      scrollViewRef.current?.scrollTo({
        x: prevSlide * width,
        animated: true,
      })
    }
  }

  const handleGetStarted = () => {
    completeOnboarding()
    navigation.replace('Login')
  }

  const handleSkip = () => {
    completeOnboarding()
    navigation.replace('Login')
  }

  const isLastSlide = currentSlide === onboardingSlides.length - 1

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <VStack className="flex-1">
        {/* Skip Button */}
        <HStack className="justify-end p-4">
          <Button variant="ghost" size="sm" onPress={handleSkip}>
            <ButtonText className="text-typography-600">Skip</ButtonText>
          </Button>
        </HStack>

        {/* Slides */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width)
            setCurrentSlide(slideIndex)
          }}
          className="flex-1"
        >
          {onboardingSlides.map((slide, index) => (
            <VStack
              key={slide.id}
              className="flex-1 justify-center items-center px-8"
              style={{ width }}
            >
              {/* Icon */}
              <Box
                className="rounded-full p-6 mb-8"
                style={{ backgroundColor: `${slide.color}20` }}
              >
                <slide.icon size={64} color={slide.color} />
              </Box>

              {/* Content */}
              <VStack space="md" className="items-center">
                <Text className="text-typography-900 text-3xl font-bold text-center">
                  {slide.title}
                </Text>
                <Text
                  className="text-xl font-semibold text-center"
                  style={{ color: slide.color }}
                >
                  {slide.subtitle}
                </Text>
                <Text className="text-typography-600 text-center text-lg leading-6 max-w-sm">
                  {slide.description}
                </Text>
              </VStack>
            </VStack>
          ))}
        </ScrollView>

        {/* Pagination Dots */}
        <HStack className="justify-center py-6" space="sm">
          {onboardingSlides.map((_, index) => (
            <Box
              key={index}
              className="rounded-full"
              style={{
                width: currentSlide === index ? 24 : 8,
                height: 8,
                backgroundColor: currentSlide === index ? '#F97316' : '#E5E7EB',
              }}
            />
          ))}
        </HStack>

        {/* Navigation Buttons */}
        <HStack className="justify-between items-center p-6">
          <Button
            variant="ghost"
            size="lg"
            onPress={handlePrevious}
            isDisabled={currentSlide === 0}
            className="opacity-60"
          >
            <ArrowLeftIcon size={20} color="#6B7280" />
            <ButtonText className="text-typography-600 ml-2">Previous</ButtonText>
          </Button>

          {isLastSlide ? (
            <Button
              size="lg"
              action="primary"
              onPress={handleGetStarted}
              className="flex-1 ml-4"
            >
              <ButtonText>Get Started</ButtonText>
            </Button>
          ) : (
            <Button
              size="lg"
              action="primary"
              onPress={handleNext}
              className="flex-1 ml-4"
            >
              <ButtonText className="mr-2">Next</ButtonText>
              <ArrowRightIcon size={20} color="white" />
            </Button>
          )}
        </HStack>
      </VStack>
    </SafeAreaView>
  )
}


