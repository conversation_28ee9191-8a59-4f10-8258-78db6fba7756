import React from 'react'
import { StyleSheet } from 'react-native'
import {
  Box,
  VStack,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
} from '@hvppyplug/ui-components-v2'
import { LoginForm } from '../../components/auth/LoginForm'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Login'>

export function LoginScreen({ navigation }: Props) {
  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword')
  }

  const handleRegister = () => {
    navigation.navigate('Register')
  }

  const handleLoginSuccess = () => {
    // Navigation will be handled by the RootNavigator based on auth state
  }

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <KeyboardAvoidingView className="flex-1" behavior="padding">
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <VStack className="flex-1 justify-center px-6 py-8" space="xl">
            {/* Header */}
            <VStack space="sm" className="items-center mb-8">
              <Text className="text-typography-900 text-3xl font-bold text-center">
                Welcome Back
              </Text>
              <Text className="text-typography-600 text-center max-w-sm">
                Sign in to your account to continue your HVPPYPlug+ journey
              </Text>
            </VStack>

            {/* Login Form */}
            <LoginForm
              onForgotPassword={handleForgotPassword}
              onRegister={handleRegister}
              onSuccess={handleLoginSuccess}
              showBiometric={true}
              showSocialLogin={true}
            />
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}




