import React from 'react'
import { StyleSheet } from 'react-native'
import {
  Box,
  VStack,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
} from '@hvppyplug/ui-components-v2'
import { ForgotPasswordForm } from '../../components/auth/ForgotPasswordForm'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'ForgotPassword'>

export function ForgotPasswordScreen({ navigation }: Props) {
  const handleBack = () => {
    navigation.goBack()
  }

  const handleSuccess = () => {
    navigation.goBack()
  }

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <KeyboardAvoidingView className="flex-1" behavior="padding">
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <VStack className="flex-1 justify-center px-6 py-8" space="xl">
            {/* Forgot Password Form */}
            <ForgotPasswordForm
              onBack={handleBack}
              onSuccess={handleSuccess}
            />
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}


