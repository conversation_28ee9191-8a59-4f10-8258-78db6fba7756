import React from 'react'
import { StyleSheet } from 'react-native'
import {
  Box,
  VStack,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
} from '@hvppyplug/ui-components-v2'
import { RegisterForm } from '../../components/auth/RegisterForm'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Register'>

interface ValidationErrors {
  name?: string
  email?: string
  password?: string
  confirmPassword?: string
}

export function RegisterScreen({ navigation }: Props) {
  const handleLogin = () => {
    navigation.goBack()
  }

  const handleRegisterSuccess = () => {
    // Navigation will be handled by the RootNavigator based on auth state
  }

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <KeyboardAvoidingView className="flex-1" behavior="padding">
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <VStack className="flex-1 justify-center px-6 py-8" space="xl">
            {/* Header */}
            <VStack space="sm" className="items-center mb-8">
              <Text className="text-typography-900 text-3xl font-bold text-center">
                Create Account
              </Text>
              <Text className="text-typography-600 text-center max-w-sm">
                Join HVPPYPlug+ and discover amazing services in your area
              </Text>
            </VStack>

            {/* Register Form */}
            <RegisterForm
              onLogin={handleLogin}
              onSuccess={handleRegisterSuccess}
              showTermsAndConditions={true}
            />
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}




