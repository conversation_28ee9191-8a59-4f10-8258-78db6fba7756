/**
 * Customer App Authentication Navigator
 * HVPPYPlug+ Customer Authentication Flow
 */

import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {
  WelcomeScreen,
  PhoneInputScreen,
  OTPVerificationScreen,
  UserRegistrationScreen,
  LoginScreen,
  PasswordResetScreen,
  AuthStackParamList,
  UserRole
} from '@hvppyplug/ui-components-v2/auth'

const Stack = createNativeStackNavigator<AuthStackParamList>()

interface AuthNavigatorProps {
  onAuthComplete: (user: any) => void
}

export const AuthNavigator: React.FC<AuthNavigatorProps> = ({ onAuthComplete }) => {
  const customerRole: UserRole = 'customer'
  
  // Customer app branding colors
  const primaryColor = '#FF6B6B'
  const secondaryColor = '#FF8E8E'

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right'
      }}
      initialRouteName="Welcome"
    >
      <Stack.Screen name="Welcome">
        {({ navigation }) => (
          <WelcomeScreen
            onRoleSelect={(role) => {
              // For customer app, always use customer role
              navigation.navigate('PhoneInput', { role: customerRole })
            }}
            onSkip={() => {
              navigation.navigate('PhoneInput', { role: customerRole })
            }}
            appName="HVPPYPlug+ Customer"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PhoneInput">
        {({ navigation, route }) => (
          <PhoneInputScreen
            onPhoneSubmit={(phone) => {
              navigation.navigate('OTPVerification', { 
                phone, 
                type: 'verification' 
              })
            }}
            onBack={() => navigation.goBack()}
            role={customerRole}
            title="Enter Your Phone Number"
            subtitle="We'll send you a verification code to get started"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="OTPVerification">
        {({ navigation, route }) => (
          <OTPVerificationScreen
            phone={route.params.phone}
            type={route.params.type}
            onVerificationSuccess={(phone, code) => {
              // Check if user exists or needs registration
              navigation.navigate('Registration', { 
                phone, 
                role: customerRole 
              })
            }}
            onBack={() => navigation.goBack()}
            title="Verify Your Phone"
            subtitle="Enter the 6-digit code sent to your phone"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Registration">
        {({ navigation, route }) => (
          <UserRegistrationScreen
            phone={route.params.phone}
            role={route.params.role}
            onRegistrationSuccess={(user) => {
              onAuthComplete(user)
            }}
            onBack={() => navigation.goBack()}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Login">
        {({ navigation, route }) => (
          <LoginScreen
            onLoginSuccess={(user) => {
              onAuthComplete(user)
            }}
            onForgotPassword={(phone) => {
              navigation.navigate('PasswordReset', { phone })
            }}
            onSignUp={() => {
              navigation.navigate('PhoneInput', { role: customerRole })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PasswordReset">
        {({ navigation, route }) => (
          <PasswordResetScreen
            onResetSuccess={() => {
              navigation.navigate('Login', { phone: route.params?.phone })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
}

export default AuthNavigator
