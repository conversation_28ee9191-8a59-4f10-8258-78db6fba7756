import { AppwriteService } from '@hvppyplug/mobile-services'

/**
 * Safe getter for AppwriteService instance
 * Throws a user-friendly error if service is not initialized
 */
export const getAppwriteService = () => {
  try {
    const service = AppwriteService.getInstance()
    return service
  } catch (error) {
    throw new Error('Service is initializing. Please wait a moment and try again.')
  }
}
