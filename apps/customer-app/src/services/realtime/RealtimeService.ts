import { Client, Databases, RealtimeResponseEvent } from 'appwrite'
import { client, DATABASE_ID } from '../../lib/appwrite'

export type RealtimeEventType = 
  | 'order.created'
  | 'order.updated' 
  | 'order.status_changed'
  | 'message.created'
  | 'message.updated'
  | 'delivery.location_updated'
  | 'notification.received'

export interface RealtimeEvent {
  type: RealtimeEventType
  payload: any
  timestamp: string
}

export type RealtimeCallback = (event: RealtimeEvent) => void

class RealtimeServiceClass {
  private subscriptions: Map<string, () => void> = new Map()
  private callbacks: Map<string, RealtimeCallback[]> = new Map()

  // Subscribe to order updates for a specific customer
  subscribeToOrderUpdates(customerId: string, callback: RealtimeCallback): string {
    const subscriptionId = `orders_${customerId}_${Date.now()}`
    
    const unsubscribe = client.subscribe(
      [`databases.${DATABASE_ID}.collections.orders.documents`],
      (response: RealtimeResponseEvent<any>) => {
        const { events, payload } = response
        
        // Filter events for this customer
        if (payload.customerId === customerId) {
          events.forEach(event => {
            let eventType: RealtimeEventType
            
            if (event.includes('create')) {
              eventType = 'order.created'
            } else if (event.includes('update')) {
              eventType = payload.status !== payload.$updatedAt ? 'order.status_changed' : 'order.updated'
            } else {
              return
            }

            const realtimeEvent: RealtimeEvent = {
              type: eventType,
              payload: payload,
              timestamp: new Date().toISOString()
            }

            callback(realtimeEvent)
          })
        }
      }
    )

    this.subscriptions.set(subscriptionId, unsubscribe)
    return subscriptionId
  }

  // Subscribe to chat messages for a specific conversation
  subscribeToChatMessages(conversationId: string, callback: RealtimeCallback): string {
    const subscriptionId = `chat_${conversationId}_${Date.now()}`
    
    const unsubscribe = client.subscribe(
      [`databases.${DATABASE_ID}.collections.messages.documents`],
      (response: RealtimeResponseEvent<any>) => {
        const { events, payload } = response
        
        // Filter events for this conversation
        if (payload.conversationId === conversationId) {
          events.forEach(event => {
            let eventType: RealtimeEventType
            
            if (event.includes('create')) {
              eventType = 'message.created'
            } else if (event.includes('update')) {
              eventType = 'message.updated'
            } else {
              return
            }

            const realtimeEvent: RealtimeEvent = {
              type: eventType,
              payload: payload,
              timestamp: new Date().toISOString()
            }

            callback(realtimeEvent)
          })
        }
      }
    )

    this.subscriptions.set(subscriptionId, unsubscribe)
    return subscriptionId
  }

  // Subscribe to delivery location updates for a specific order
  subscribeToDeliveryTracking(orderId: string, callback: RealtimeCallback): string {
    const subscriptionId = `delivery_${orderId}_${Date.now()}`
    
    const unsubscribe = client.subscribe(
      [`databases.${DATABASE_ID}.collections.delivery_tracking.documents`],
      (response: RealtimeResponseEvent<any>) => {
        const { events, payload } = response
        
        // Filter events for this order
        if (payload.orderId === orderId) {
          events.forEach(event => {
            if (event.includes('update') || event.includes('create')) {
              const realtimeEvent: RealtimeEvent = {
                type: 'delivery.location_updated',
                payload: payload,
                timestamp: new Date().toISOString()
              }

              callback(realtimeEvent)
            }
          })
        }
      }
    )

    this.subscriptions.set(subscriptionId, unsubscribe)
    return subscriptionId
  }

  // Subscribe to user notifications
  subscribeToNotifications(userId: string, callback: RealtimeCallback): string {
    const subscriptionId = `notifications_${userId}_${Date.now()}`
    
    const unsubscribe = client.subscribe(
      [`databases.${DATABASE_ID}.collections.notifications.documents`],
      (response: RealtimeResponseEvent<any>) => {
        const { events, payload } = response
        
        // Filter events for this user
        if (payload.userId === userId) {
          events.forEach(event => {
            if (event.includes('create')) {
              const realtimeEvent: RealtimeEvent = {
                type: 'notification.received',
                payload: payload,
                timestamp: new Date().toISOString()
              }

              callback(realtimeEvent)
            }
          })
        }
      }
    )

    this.subscriptions.set(subscriptionId, unsubscribe)
    return subscriptionId
  }

  // Subscribe to multiple event types with a single callback
  subscribeToMultipleEvents(
    eventTypes: { type: 'orders' | 'chat' | 'delivery' | 'notifications', id: string }[],
    callback: RealtimeCallback
  ): string[] {
    const subscriptionIds: string[] = []

    eventTypes.forEach(({ type, id }) => {
      let subscriptionId: string

      switch (type) {
        case 'orders':
          subscriptionId = this.subscribeToOrderUpdates(id, callback)
          break
        case 'chat':
          subscriptionId = this.subscribeToChatMessages(id, callback)
          break
        case 'delivery':
          subscriptionId = this.subscribeToDeliveryTracking(id, callback)
          break
        case 'notifications':
          subscriptionId = this.subscribeToNotifications(id, callback)
          break
        default:
          return
      }

      subscriptionIds.push(subscriptionId)
    })

    return subscriptionIds
  }

  // Unsubscribe from a specific subscription
  unsubscribe(subscriptionId: string): void {
    const unsubscribeFunction = this.subscriptions.get(subscriptionId)
    if (unsubscribeFunction) {
      unsubscribeFunction()
      this.subscriptions.delete(subscriptionId)
    }
  }

  // Unsubscribe from multiple subscriptions
  unsubscribeMultiple(subscriptionIds: string[]): void {
    subscriptionIds.forEach(id => this.unsubscribe(id))
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll(): void {
    this.subscriptions.forEach((unsubscribeFunction) => {
      unsubscribeFunction()
    })
    this.subscriptions.clear()
    this.callbacks.clear()
  }

  // Get active subscription count
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size
  }

  // Check if a subscription is active
  isSubscriptionActive(subscriptionId: string): boolean {
    return this.subscriptions.has(subscriptionId)
  }

  // Add a global event listener for debugging
  addGlobalEventListener(callback: (event: any) => void): string {
    const subscriptionId = `global_${Date.now()}`
    
    const unsubscribe = client.subscribe(
      [`databases.${DATABASE_ID}.collections`],
      callback
    )

    this.subscriptions.set(subscriptionId, unsubscribe)
    return subscriptionId
  }
}

export const RealtimeService = new RealtimeServiceClass()
