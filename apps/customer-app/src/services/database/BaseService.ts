import { databases, DATABASE_ID } from '../../lib/appwrite'
import { Query } from 'appwrite'

export interface QueryOptions {
  limit?: number
  offset?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
  filters?: Record<string, any>
}

export interface PaginatedResult<T> {
  documents: T[]
  total: number
  hasMore: boolean
  offset: number
  limit: number
}

export abstract class BaseService {
  protected abstract collectionId: string

  protected async create<T>(data: Omit<T, '$id' | '$createdAt' | '$updatedAt'>): Promise<T> {
    try {
      const document = await databases.createDocument(
        DATABASE_ID,
        this.collectionId,
        'unique()',
        data
      )
      return document as T
    } catch (error) {
      console.error(`Error creating document in ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }

  protected async getById<T>(id: string): Promise<T | null> {
    try {
      const document = await databases.getDocument(
        DATABASE_ID,
        this.collectionId,
        id
      )
      return document as T
    } catch (error) {
      if (error.code === 404) {
        return null
      }
      console.error(`Error getting document ${id} from ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }

  protected async update<T>(id: string, data: Partial<T>): Promise<T> {
    try {
      const document = await databases.updateDocument(
        DATABASE_ID,
        this.collectionId,
        id,
        data
      )
      return document as T
    } catch (error) {
      console.error(`Error updating document ${id} in ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }

  protected async delete(id: string): Promise<void> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        this.collectionId,
        id
      )
    } catch (error) {
      console.error(`Error deleting document ${id} from ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }

  protected async list<T>(options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    try {
      const queries = this.buildQueries(options)
      
      const response = await databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        queries
      )

      return {
        documents: response.documents as T[],
        total: response.total,
        hasMore: response.documents.length === (options.limit || 25),
        offset: options.offset || 0,
        limit: options.limit || 25
      }
    } catch (error) {
      console.error(`Error listing documents from ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }

  protected buildQueries(options: QueryOptions): string[] {
    const queries: string[] = []

    // Add limit
    if (options.limit) {
      queries.push(Query.limit(options.limit))
    }

    // Add offset
    if (options.offset) {
      queries.push(Query.offset(options.offset))
    }

    // Add ordering
    if (options.orderBy) {
      const direction = options.orderDirection === 'desc' ? Query.orderDesc : Query.orderAsc
      queries.push(direction(options.orderBy))
    }

    // Add filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            queries.push(Query.equal(key, value))
          } else if (typeof value === 'string' && value.includes('*')) {
            queries.push(Query.search(key, value.replace('*', '')))
          } else {
            queries.push(Query.equal(key, value))
          }
        }
      })
    }

    return queries
  }

  protected handleError(error: any): Error {
    if (error.message) {
      return new Error(error.message)
    }
    return new Error('An unexpected error occurred')
  }

  // Utility method for batch operations
  protected async batchCreate<T>(items: Omit<T, '$id' | '$createdAt' | '$updatedAt'>[]): Promise<T[]> {
    const results: T[] = []
    
    for (const item of items) {
      try {
        const created = await this.create<T>(item)
        results.push(created)
      } catch (error) {
        console.error('Error in batch create:', error)
        // Continue with other items
      }
    }
    
    return results
  }

  // Utility method for searching
  protected async search<T>(searchTerm: string, searchFields: string[], options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    const searchQueries = searchFields.map(field => 
      Query.search(field, searchTerm)
    )

    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        [
          ...searchQueries,
          ...this.buildQueries(options)
        ]
      )

      return {
        documents: response.documents as T[],
        total: response.total,
        hasMore: response.documents.length === (options.limit || 25),
        offset: options.offset || 0,
        limit: options.limit || 25
      }
    } catch (error) {
      console.error(`Error searching in ${this.collectionId}:`, error)
      throw this.handleError(error)
    }
  }
}
