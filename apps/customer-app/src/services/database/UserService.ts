import { BaseService, QueryOptions } from './BaseService'
import { databases, DATABASE_ID } from '../../lib/appwrite'
import type { UserProfile, Address, PaymentMethod } from '../../components/profile/types'

export class UserService extends BaseService {
  protected collectionId = 'users'

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const user = await this.getById(userId)
      if (!user) return null

      // Get user's addresses
      const addresses = await this.getUserAddresses(userId)
      
      // Get user's payment methods
      const paymentMethods = await this.getUserPaymentMethods(userId)

      return this.transformUserData(user, addresses, paymentMethods)
    } catch (error) {
      console.error('Error getting user profile:', error)
      throw this.handleError(error)
    }
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const updatedUser = await this.update(userId, {
        ...updates,
        updatedAt: new Date().toISOString()
      })

      return this.transformUserData(updatedUser, [], [])
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw this.handleError(error)
    }
  }

  async getUserAddresses(userId: string): Promise<Address[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        'addresses',
        [
          `userId = "${userId}"`,
          'isActive = true'
        ]
      )

      return response.documents.map(this.transformAddressData)
    } catch (error) {
      console.error('Error getting user addresses:', error)
      return []
    }
  }

  async addUserAddress(userId: string, addressData: Omit<Address, 'id' | 'createdAt'>): Promise<Address> {
    try {
      const address = await databases.createDocument(
        DATABASE_ID,
        'addresses',
        'unique()',
        {
          ...addressData,
          userId,
          createdAt: new Date().toISOString()
        }
      )

      return this.transformAddressData(address)
    } catch (error) {
      console.error('Error adding user address:', error)
      throw this.handleError(error)
    }
  }

  async updateUserAddress(addressId: string, updates: Partial<Address>): Promise<Address> {
    try {
      const updatedAddress = await databases.updateDocument(
        DATABASE_ID,
        'addresses',
        addressId,
        updates
      )

      return this.transformAddressData(updatedAddress)
    } catch (error) {
      console.error('Error updating user address:', error)
      throw this.handleError(error)
    }
  }

  async deleteUserAddress(addressId: string): Promise<void> {
    try {
      await databases.updateDocument(
        DATABASE_ID,
        'addresses',
        addressId,
        { isActive: false }
      )
    } catch (error) {
      console.error('Error deleting user address:', error)
      throw this.handleError(error)
    }
  }

  async setDefaultAddress(userId: string, addressId: string): Promise<void> {
    try {
      // First, unset all default addresses for this user
      const addresses = await this.getUserAddresses(userId)
      for (const address of addresses) {
        if (address.isDefault) {
          await this.updateUserAddress(address.id, { isDefault: false })
        }
      }

      // Set the new default address
      await this.updateUserAddress(addressId, { isDefault: true })
    } catch (error) {
      console.error('Error setting default address:', error)
      throw this.handleError(error)
    }
  }

  async getUserPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        'payments',
        [
          `userId = "${userId}"`,
          'isActive = true'
        ]
      )

      return response.documents.map(this.transformPaymentMethodData)
    } catch (error) {
      console.error('Error getting user payment methods:', error)
      return []
    }
  }

  async addPaymentMethod(userId: string, paymentData: Omit<PaymentMethod, 'id' | 'createdAt'>): Promise<PaymentMethod> {
    try {
      const paymentMethod = await databases.createDocument(
        DATABASE_ID,
        'payments',
        'unique()',
        {
          ...paymentData,
          userId,
          createdAt: new Date().toISOString()
        }
      )

      return this.transformPaymentMethodData(paymentMethod)
    } catch (error) {
      console.error('Error adding payment method:', error)
      throw this.handleError(error)
    }
  }

  async deletePaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      await databases.updateDocument(
        DATABASE_ID,
        'payments',
        paymentMethodId,
        { isActive: false }
      )
    } catch (error) {
      console.error('Error deleting payment method:', error)
      throw this.handleError(error)
    }
  }

  async setDefaultPaymentMethod(userId: string, paymentMethodId: string): Promise<void> {
    try {
      // First, unset all default payment methods for this user
      const paymentMethods = await this.getUserPaymentMethods(userId)
      for (const method of paymentMethods) {
        if (method.isDefault) {
          await databases.updateDocument(
            DATABASE_ID,
            'payments',
            method.id,
            { isDefault: false }
          )
        }
      }

      // Set the new default payment method
      await databases.updateDocument(
        DATABASE_ID,
        'payments',
        paymentMethodId,
        { isDefault: true }
      )
    } catch (error) {
      console.error('Error setting default payment method:', error)
      throw this.handleError(error)
    }
  }

  private transformUserData(user: any, addresses: Address[], paymentMethods: PaymentMethod[]): UserProfile {
    return {
      id: user.$id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      avatar: user.avatarUrl,
      dateOfBirth: user.dateOfBirth,
      gender: user.gender,
      preferences: user.preferences || {
        notifications: {
          orderUpdates: true,
          promotions: true,
          newRestaurants: true,
          recommendations: true,
          chat: true,
          email: true,
          sms: false,
          push: true
        },
        dietary: {
          vegetarian: false,
          vegan: false,
          glutenFree: false,
          halal: false,
          kosher: false,
          allergies: [],
          dislikes: []
        },
        language: 'en',
        currency: 'ZAR',
        theme: 'system'
      },
      addresses,
      paymentMethods,
      stats: {
        totalOrders: user.totalOrders || 0,
        totalSpent: user.totalSpent || 0,
        favoriteRestaurants: user.favoriteRestaurants || [],
        averageRating: user.averageRating || 0,
        reviewsCount: user.reviewsCount || 0
      },
      createdAt: user.$createdAt,
      updatedAt: user.$updatedAt
    }
  }

  private transformAddressData(address: any): Address {
    return {
      id: address.$id,
      label: address.label,
      type: address.type || 'other',
      street: address.street,
      city: address.city,
      province: address.province,
      postalCode: address.postalCode,
      country: address.country || 'South Africa',
      coordinates: address.coordinates || { latitude: 0, longitude: 0 },
      instructions: address.instructions,
      isDefault: address.isDefault || false,
      isActive: address.isActive !== false,
      createdAt: address.$createdAt
    }
  }

  private transformPaymentMethodData(payment: any): PaymentMethod {
    return {
      id: payment.$id,
      type: payment.type,
      name: payment.name || this.getPaymentMethodName(payment.type, payment.details),
      isDefault: payment.isDefault || false,
      isActive: payment.isActive !== false,
      details: payment.details || {},
      createdAt: payment.$createdAt
    }
  }

  private getPaymentMethodName(type: string, details: any): string {
    switch (type) {
      case 'card':
        return `**** ${details?.last4 || '****'}`
      case 'payfast':
        return `PayFast (${details?.email || 'Email'})`
      case 'mpesa':
        return `M-Pesa (${details?.phoneNumber || 'Phone'})`
      case 'cash':
        return 'Cash on Delivery'
      default:
        return 'Payment Method'
    }
  }
}
