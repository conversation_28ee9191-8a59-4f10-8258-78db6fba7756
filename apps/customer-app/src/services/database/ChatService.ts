import { BaseService, QueryOptions, PaginatedResult } from './BaseService'
import { Query } from 'appwrite'
import { databases, DATABASE_ID } from '../../lib/appwrite'
import type { ChatMessage, Conversation, ChatUser } from '../../components/chat/types'

export interface ChatQueryOptions extends QueryOptions {
  conversationId?: string
  senderId?: string
  type?: 'text' | 'image' | 'file' | 'location' | 'system'
  isRead?: boolean
}

export class ChatService extends BaseService {
  protected collectionId = 'messages'

  async createConversation(
    type: 'customer_vendor' | 'customer_runner' | 'support',
    participants: string[],
    orderId?: string
  ): Promise<Conversation> {
    try {
      const conversation = await databases.createDocument(
        DATABASE_ID,
        'conversations',
        'unique()',
        {
          type,
          participants,
          orderId,
          isActive: true,
          unreadCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      )

      return this.transformConversationData(conversation)
    } catch (error) {
      console.error('Error creating conversation:', error)
      throw this.handleError(error)
    }
  }

  async getConversations(userId: string): Promise<Conversation[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        'conversations',
        [
          Query.equal('participants', userId),
          Query.equal('isActive', true),
          Query.orderDesc('updatedAt'),
          Query.limit(50)
        ]
      )

      const conversations = await Promise.all(
        response.documents.map(async (conv) => {
          const conversation = this.transformConversationData(conv)
          
          // Get last message for each conversation
          const lastMessage = await this.getLastMessage(conversation.id)
          if (lastMessage) {
            conversation.lastMessage = lastMessage
          }

          return conversation
        })
      )

      return conversations
    } catch (error) {
      console.error('Error getting conversations:', error)
      throw this.handleError(error)
    }
  }

  async getConversationById(conversationId: string): Promise<Conversation | null> {
    try {
      const conversation = await databases.getDocument(
        DATABASE_ID,
        'conversations',
        conversationId
      )

      return this.transformConversationData(conversation)
    } catch (error) {
      if (error.code === 404) {
        return null
      }
      console.error('Error getting conversation:', error)
      throw this.handleError(error)
    }
  }

  async sendMessage(
    conversationId: string,
    senderId: string,
    content: string,
    type: 'text' | 'image' | 'file' | 'location' | 'system' = 'text',
    attachments?: any[],
    replyTo?: string
  ): Promise<ChatMessage> {
    try {
      const message = await this.create<ChatMessage>({
        conversationId,
        senderId,
        content,
        type,
        attachments: attachments || [],
        replyTo,
        isRead: false,
        timestamp: new Date().toISOString()
      } as any)

      // Update conversation's last message and timestamp
      await databases.updateDocument(
        DATABASE_ID,
        'conversations',
        conversationId,
        {
          updatedAt: new Date().toISOString()
        }
      )

      return this.transformMessageData(message)
    } catch (error) {
      console.error('Error sending message:', error)
      throw this.handleError(error)
    }
  }

  async getMessages(
    conversationId: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResult<ChatMessage>> {
    try {
      const result = await this.list<ChatMessage>({
        ...options,
        filters: {
          conversationId
        },
        orderBy: 'timestamp',
        orderDirection: 'desc',
        limit: options.limit || 50
      })

      return {
        ...result,
        documents: result.documents.map(this.transformMessageData).reverse()
      }
    } catch (error) {
      console.error('Error getting messages:', error)
      throw this.handleError(error)
    }
  }

  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      // Get unread messages in this conversation that are not from the current user
      const response = await databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        [
          Query.equal('conversationId', conversationId),
          Query.equal('isRead', false),
          Query.notEqual('senderId', userId)
        ]
      )

      // Mark all unread messages as read
      const updatePromises = response.documents.map(message =>
        databases.updateDocument(
          DATABASE_ID,
          this.collectionId,
          message.$id,
          { isRead: true }
        )
      )

      await Promise.all(updatePromises)

      // Update conversation unread count
      await databases.updateDocument(
        DATABASE_ID,
        'conversations',
        conversationId,
        { unreadCount: 0 }
      )
    } catch (error) {
      console.error('Error marking messages as read:', error)
      throw this.handleError(error)
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await this.delete(messageId)
    } catch (error) {
      console.error('Error deleting message:', error)
      throw this.handleError(error)
    }
  }

  async editMessage(messageId: string, newContent: string): Promise<ChatMessage> {
    try {
      const updatedMessage = await this.update<ChatMessage>(messageId, {
        content: newContent,
        isEdited: true,
        editedAt: new Date().toISOString()
      })

      return this.transformMessageData(updatedMessage)
    } catch (error) {
      console.error('Error editing message:', error)
      throw this.handleError(error)
    }
  }

  private async getLastMessage(conversationId: string): Promise<ChatMessage | null> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        [
          Query.equal('conversationId', conversationId),
          Query.orderDesc('timestamp'),
          Query.limit(1)
        ]
      )

      if (response.documents.length === 0) {
        return null
      }

      return this.transformMessageData(response.documents[0])
    } catch (error) {
      console.error('Error getting last message:', error)
      return null
    }
  }

  private transformConversationData(conversation: any): Conversation {
    return {
      id: conversation.$id,
      type: conversation.type,
      participants: conversation.participants || [],
      unreadCount: conversation.unreadCount || 0,
      isActive: conversation.isActive !== false,
      orderId: conversation.orderId,
      createdAt: conversation.$createdAt || conversation.createdAt,
      updatedAt: conversation.$updatedAt || conversation.updatedAt
    }
  }

  private transformMessageData(message: any): ChatMessage {
    return {
      id: message.$id,
      conversationId: message.conversationId,
      senderId: message.senderId,
      senderName: message.senderName || 'Unknown',
      senderAvatar: message.senderAvatar,
      content: message.content,
      type: message.type || 'text',
      timestamp: message.$createdAt || message.timestamp,
      isRead: message.isRead || false,
      attachments: message.attachments || [],
      replyTo: message.replyTo,
      isEdited: message.isEdited || false,
      editedAt: message.editedAt
    }
  }
}
