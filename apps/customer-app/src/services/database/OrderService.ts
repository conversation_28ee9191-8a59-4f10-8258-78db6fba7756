import { BaseService, QueryOptions, PaginatedResult } from './BaseService'
import { Query } from 'appwrite'
import { databases, DATABASE_ID } from '../../lib/appwrite'
import type { Order, OrderStatus, OrderTracking } from '../../components/orders/types'

export interface OrderQueryOptions extends QueryOptions {
  customerId?: string
  vendorId?: string
  status?: OrderStatus[]
  dateFrom?: string
  dateTo?: string
}

export class OrderService extends BaseService {
  protected collectionId = 'orders'

  async createOrder(orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): Promise<Order> {
    try {
      const orderNumber = this.generateOrderNumber()
      
      const order = await this.create<Order>({
        ...orderData,
        orderNumber,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as any)

      return order
    } catch (error) {
      console.error('Error creating order:', error)
      throw this.handleError(error)
    }
  }

  async getOrderById(id: string): Promise<Order | null> {
    try {
      const order = await this.getById<Order>(id)
      if (!order) return null

      return this.transformOrderData(order)
    } catch (error) {
      console.error('Error getting order by ID:', error)
      throw this.handleError(error)
    }
  }

  async getCustomerOrders(customerId: string, options: OrderQueryOptions = {}): Promise<PaginatedResult<Order>> {
    try {
      const result = await this.list<Order>({
        ...options,
        filters: {
          ...options.filters,
          customerId
        },
        orderBy: 'createdAt',
        orderDirection: 'desc'
      })

      return {
        ...result,
        documents: result.documents.map(this.transformOrderData)
      }
    } catch (error) {
      console.error('Error getting customer orders:', error)
      throw this.handleError(error)
    }
  }

  async getActiveOrders(customerId: string): Promise<Order[]> {
    try {
      const activeStatuses: OrderStatus[] = ['pending', 'confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way']
      
      const result = await this.getCustomerOrders(customerId, {
        filters: {
          status: activeStatuses
        },
        limit: 10
      })

      return result.documents
    } catch (error) {
      console.error('Error getting active orders:', error)
      throw this.handleError(error)
    }
  }

  async getOrderHistory(customerId: string, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    try {
      const completedStatuses: OrderStatus[] = ['delivered', 'cancelled', 'refunded']
      
      return await this.getCustomerOrders(customerId, {
        ...options,
        filters: {
          ...options.filters,
          status: completedStatuses
        }
      })
    } catch (error) {
      console.error('Error getting order history:', error)
      throw this.handleError(error)
    }
  }

  async updateOrderStatus(orderId: string, status: OrderStatus, trackingData?: Partial<OrderTracking>): Promise<Order> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date().toISOString()
      }

      if (trackingData) {
        updateData.tracking = trackingData
      }

      if (status === 'delivered') {
        updateData.actualDeliveryTime = new Date().toISOString()
      }

      const updatedOrder = await this.update<Order>(orderId, updateData)
      return this.transformOrderData(updatedOrder)
    } catch (error) {
      console.error('Error updating order status:', error)
      throw this.handleError(error)
    }
  }

  async cancelOrder(orderId: string, reason?: string): Promise<Order> {
    try {
      const updateData: any = {
        status: 'cancelled' as OrderStatus,
        updatedAt: new Date().toISOString()
      }

      if (reason) {
        updateData.specialInstructions = `${updateData.specialInstructions || ''}\nCancellation reason: ${reason}`.trim()
      }

      const cancelledOrder = await this.update<Order>(orderId, updateData)
      return this.transformOrderData(cancelledOrder)
    } catch (error) {
      console.error('Error cancelling order:', error)
      throw this.handleError(error)
    }
  }

  async addOrderReview(orderId: string, rating: number, comment?: string, images?: string[]): Promise<Order> {
    try {
      const review = {
        id: `review_${Date.now()}`,
        rating,
        comment,
        images,
        createdAt: new Date().toISOString()
      }

      const updatedOrder = await this.update<Order>(orderId, {
        review,
        updatedAt: new Date().toISOString()
      })

      return this.transformOrderData(updatedOrder)
    } catch (error) {
      console.error('Error adding order review:', error)
      throw this.handleError(error)
    }
  }

  async reorderItems(orderId: string, customerId: string): Promise<Order> {
    try {
      const originalOrder = await this.getOrderById(orderId)
      if (!originalOrder) {
        throw new Error('Original order not found')
      }

      const newOrderData = {
        customerId,
        vendorId: originalOrder.vendorId,
        vendor: originalOrder.vendor,
        status: 'pending' as OrderStatus,
        items: originalOrder.items,
        subtotal: originalOrder.subtotal,
        deliveryFee: originalOrder.deliveryFee,
        tax: originalOrder.tax,
        total: originalOrder.total,
        currency: originalOrder.currency,
        paymentStatus: 'pending' as any,
        deliveryAddress: originalOrder.deliveryAddress,
        estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000).toISOString(), // 45 minutes from now
        specialInstructions: originalOrder.specialInstructions
      }

      return await this.createOrder(newOrderData)
    } catch (error) {
      console.error('Error reordering items:', error)
      throw this.handleError(error)
    }
  }

  private generateOrderNumber(): string {
    const timestamp = Date.now().toString()
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `HVP${timestamp.slice(-6)}${random}`
  }

  private transformOrderData(order: any): Order {
    return {
      id: order.$id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      vendorId: order.vendorId,
      vendor: order.vendor || {
        id: order.vendorId,
        name: 'Unknown Vendor',
        image: undefined,
        phone: undefined
      },
      status: order.status,
      items: order.items || [],
      subtotal: order.subtotal || 0,
      deliveryFee: order.deliveryFee || 0,
      tax: order.tax || 0,
      total: order.total || 0,
      currency: order.currency || 'ZAR',
      paymentStatus: order.paymentStatus || 'pending',
      paymentMethod: order.paymentMethod,
      deliveryAddress: order.deliveryAddress,
      estimatedDeliveryTime: order.estimatedDeliveryTime,
      actualDeliveryTime: order.actualDeliveryTime,
      specialInstructions: order.specialInstructions,
      tracking: order.tracking,
      review: order.review,
      createdAt: order.$createdAt || order.createdAt,
      updatedAt: order.$updatedAt || order.updatedAt
    }
  }
}
