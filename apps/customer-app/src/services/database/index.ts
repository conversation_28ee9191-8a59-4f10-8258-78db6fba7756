// Database Services
export { BaseService } from './BaseService'
export { RestaurantService } from './RestaurantService'
export { OrderService } from './OrderService'
export { UserService } from './UserService'

// Service instances (singletons)
export const restaurantService = new RestaurantService()
export const orderService = new OrderService()
export const userService = new UserService()

// Types
export type { QueryOptions, PaginatedResult } from './BaseService'
export type { RestaurantQueryOptions } from './RestaurantService'
export type { OrderQueryOptions } from './OrderService'
