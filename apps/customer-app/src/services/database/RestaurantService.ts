import { BaseService, QueryOptions, PaginatedResult } from './BaseService'
import { Query } from 'appwrite'
import { databases, DATABASE_ID } from '../../lib/appwrite'
import type { Restaurant, MenuCategory, MenuItem } from '../../components/restaurants/types'

export interface RestaurantQueryOptions extends QueryOptions {
  cuisine?: string[]
  isOpen?: boolean
  isFeatured?: boolean
  minRating?: number
  maxDeliveryTime?: number
  location?: {
    latitude: number
    longitude: number
    radius: number // in kilometers
  }
}

export class RestaurantService extends BaseService {
  protected collectionId = 'vendors'

  async getRestaurants(options: RestaurantQueryOptions = {}): Promise<PaginatedResult<Restaurant>> {
    const queries = this.buildRestaurantQueries(options)
    
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        queries
      )

      // Transform vendor data to restaurant format
      const restaurants = response.documents.map(this.transformVendorToRestaurant)

      return {
        documents: restaurants,
        total: response.total,
        hasMore: response.documents.length === (options.limit || 25),
        offset: options.offset || 0,
        limit: options.limit || 25
      }
    } catch (error) {
      console.error('Error getting restaurants:', error)
      throw this.handleError(error)
    }
  }

  async getRestaurantById(id: string): Promise<Restaurant | null> {
    try {
      const vendor = await this.getById(id)
      if (!vendor) return null

      return this.transformVendorToRestaurant(vendor)
    } catch (error) {
      console.error('Error getting restaurant by ID:', error)
      throw this.handleError(error)
    }
  }

  async getFeaturedRestaurants(limit: number = 10): Promise<Restaurant[]> {
    try {
      const result = await this.getRestaurants({
        isFeatured: true,
        isOpen: true,
        limit,
        orderBy: 'rating',
        orderDirection: 'desc'
      })
      return result.documents
    } catch (error) {
      console.error('Error getting featured restaurants:', error)
      throw this.handleError(error)
    }
  }

  async getNearbyRestaurants(
    latitude: number, 
    longitude: number, 
    radius: number = 10,
    limit: number = 20
  ): Promise<Restaurant[]> {
    try {
      const result = await this.getRestaurants({
        location: { latitude, longitude, radius },
        isOpen: true,
        limit,
        orderBy: 'distance'
      })
      return result.documents
    } catch (error) {
      console.error('Error getting nearby restaurants:', error)
      throw this.handleError(error)
    }
  }

  async searchRestaurants(
    searchTerm: string, 
    options: RestaurantQueryOptions = {}
  ): Promise<PaginatedResult<Restaurant>> {
    try {
      const searchFields = ['name', 'description', 'cuisine', 'tags']
      return await this.search<Restaurant>(searchTerm, searchFields, options)
    } catch (error) {
      console.error('Error searching restaurants:', error)
      throw this.handleError(error)
    }
  }

  async getRestaurantMenu(restaurantId: string): Promise<MenuCategory[]> {
    try {
      // Get menu items for this restaurant
      const response = await databases.listDocuments(
        DATABASE_ID,
        'menu-items',
        [
          Query.equal('vendorId', restaurantId),
          Query.equal('isAvailable', true),
          Query.orderAsc('categoryName'),
          Query.orderAsc('sortOrder')
        ]
      )

      // Group items by category
      const categoriesMap = new Map<string, MenuItem[]>()
      
      response.documents.forEach((item: any) => {
        const categoryName = item.categoryName || 'Other'
        if (!categoriesMap.has(categoryName)) {
          categoriesMap.set(categoryName, [])
        }
        categoriesMap.get(categoryName)!.push(this.transformMenuItemData(item))
      })

      // Convert to MenuCategory array
      const categories: MenuCategory[] = Array.from(categoriesMap.entries()).map(([name, items], index) => ({
        id: `category_${index}`,
        name,
        description: `${name} items`,
        items,
        isAvailable: true,
        sortOrder: index
      }))

      return categories
    } catch (error) {
      console.error('Error getting restaurant menu:', error)
      throw this.handleError(error)
    }
  }

  private buildRestaurantQueries(options: RestaurantQueryOptions): string[] {
    const queries = this.buildQueries(options)

    // Add restaurant-specific filters
    if (options.cuisine && options.cuisine.length > 0) {
      queries.push(Query.equal('businessType', options.cuisine))
    }

    if (options.isOpen !== undefined) {
      queries.push(Query.equal('isActive', options.isOpen))
    }

    if (options.isFeatured !== undefined) {
      queries.push(Query.equal('isFeatured', options.isFeatured))
    }

    if (options.minRating) {
      queries.push(Query.greaterThanEqual('rating', options.minRating))
    }

    // Note: Location-based queries would need to be implemented with Appwrite Functions
    // or handled client-side for now

    return queries
  }

  private transformVendorToRestaurant(vendor: any): Restaurant {
    return {
      id: vendor.$id,
      name: vendor.name,
      description: vendor.description || '',
      image: vendor.imageUrl || vendor.images?.[0] || '',
      images: vendor.images || [vendor.imageUrl].filter(Boolean),
      cuisine: [vendor.businessType],
      rating: vendor.rating || 0,
      reviewCount: vendor.reviewCount || 0,
      deliveryTime: `${vendor.averageDeliveryTime || 30}-${(vendor.averageDeliveryTime || 30) + 15} min`,
      deliveryFee: vendor.deliveryFee || 0,
      minimumOrder: vendor.minimumOrder || 0,
      isOpen: vendor.isActive || false,
      isFeatured: vendor.isFeatured || false,
      distance: 0, // Will be calculated client-side
      address: {
        street: vendor.address?.street || '',
        city: vendor.address?.city || '',
        province: vendor.address?.province || '',
        coordinates: {
          latitude: vendor.location?.coordinates?.latitude || 0,
          longitude: vendor.location?.coordinates?.longitude || 0
        }
      },
      contact: {
        phone: vendor.phone || '',
        email: vendor.email
      },
      businessHours: this.parseBusinessHours(vendor.businessHours),
      menu: [], // Will be loaded separately
      tags: vendor.tags || [],
      createdAt: vendor.$createdAt,
      updatedAt: vendor.$updatedAt
    }
  }

  private transformMenuItemData(item: any): MenuItem {
    return {
      id: item.$id,
      name: item.name,
      description: item.description || '',
      image: item.imageUrl,
      price: item.price,
      originalPrice: item.originalPrice,
      isAvailable: item.isAvailable,
      isPopular: item.isPopular || false,
      isVegetarian: item.isVegetarian || false,
      isVegan: item.isVegan || false,
      isGlutenFree: item.isGlutenFree || false,
      isSpicy: item.isSpicy || false,
      allergens: item.allergens || [],
      nutritionInfo: item.nutritionInfo,
      customizations: item.customizations || [],
      preparationTime: item.preparationTime || 15,
      tags: item.tags || []
    }
  }

  private parseBusinessHours(businessHours: any): any[] {
    if (!businessHours) {
      return [
        { day: 'Monday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Tuesday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Wednesday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Thursday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Friday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Saturday', openTime: '09:00', closeTime: '22:00', isOpen: true },
        { day: 'Sunday', openTime: '09:00', closeTime: '22:00', isOpen: true }
      ]
    }

    if (typeof businessHours === 'string') {
      try {
        return JSON.parse(businessHours)
      } catch {
        return []
      }
    }

    return businessHours
  }
}
