import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-netinfo/netinfo'

export interface OfflineAction {
  id: string
  type: 'CREATE' | 'UPDATE' | 'DELETE'
  collection: string
  data: any
  timestamp: string
  retryCount: number
  maxRetries: number
}

export interface SyncStatus {
  isOnline: boolean
  lastSyncTime: string | null
  pendingActions: number
  isSyncing: boolean
  syncErrors: string[]
}

class OfflineServiceClass {
  private isOnline = true
  private syncInProgress = false
  private pendingActions: OfflineAction[] = []
  private syncCallbacks: ((status: SyncStatus) => void)[] = []
  private readonly STORAGE_KEY = 'offline_actions'
  private readonly MAX_RETRIES = 3

  constructor() {
    this.initializeNetworkListener()
    this.loadPendingActions()
  }

  // Initialize network state listener
  private initializeNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline
      this.isOnline = state.isConnected ?? false

      console.log('Network state changed:', {
        isConnected: state.isConnected,
        type: state.type,
        isInternetReachable: state.isInternetReachable
      })

      // If we just came back online, trigger sync
      if (!wasOnline && this.isOnline) {
        this.syncPendingActions()
      }

      this.notifySyncCallbacks()
    })
  }

  // Load pending actions from storage
  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.pendingActions = JSON.parse(stored)
        console.log(`Loaded ${this.pendingActions.length} pending actions`)
      }
    } catch (error) {
      console.error('Error loading pending actions:', error)
    }
  }

  // Save pending actions to storage
  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Error saving pending actions:', error)
    }
  }

  // Add action to offline queue
  async addOfflineAction(
    type: 'CREATE' | 'UPDATE' | 'DELETE',
    collection: string,
    data: any
  ): Promise<string> {
    const action: OfflineAction = {
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      collection,
      data,
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries: this.MAX_RETRIES
    }

    this.pendingActions.push(action)
    await this.savePendingActions()
    
    console.log(`Added offline action: ${type} ${collection}`, action.id)
    this.notifySyncCallbacks()

    // If online, try to sync immediately
    if (this.isOnline) {
      this.syncPendingActions()
    }

    return action.id
  }

  // Sync pending actions when online
  async syncPendingActions(): Promise<void> {
    if (!this.isOnline || this.syncInProgress || this.pendingActions.length === 0) {
      return
    }

    this.syncInProgress = true
    this.notifySyncCallbacks()

    console.log(`Starting sync of ${this.pendingActions.length} pending actions`)

    const actionsToSync = [...this.pendingActions]
    const syncErrors: string[] = []

    for (const action of actionsToSync) {
      try {
        await this.executeAction(action)
        
        // Remove successful action
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
        console.log(`Successfully synced action: ${action.id}`)
        
      } catch (error: any) {
        console.error(`Error syncing action ${action.id}:`, error)
        
        // Increment retry count
        const actionIndex = this.pendingActions.findIndex(a => a.id === action.id)
        if (actionIndex >= 0) {
          this.pendingActions[actionIndex].retryCount++
          
          // Remove action if max retries exceeded
          if (this.pendingActions[actionIndex].retryCount >= action.maxRetries) {
            this.pendingActions.splice(actionIndex, 1)
            syncErrors.push(`Action ${action.id} failed after ${action.maxRetries} retries`)
          }
        }
      }
    }

    await this.savePendingActions()
    this.syncInProgress = false
    
    console.log(`Sync completed. ${this.pendingActions.length} actions remaining`)
    this.notifySyncCallbacks()
  }

  // Execute a single offline action
  private async executeAction(action: OfflineAction): Promise<void> {
    // This would integrate with your actual API services
    // For now, we'll simulate the API calls
    
    switch (action.collection) {
      case 'orders':
        await this.syncOrderAction(action)
        break
      case 'messages':
        await this.syncMessageAction(action)
        break
      case 'users':
        await this.syncUserAction(action)
        break
      default:
        throw new Error(`Unknown collection: ${action.collection}`)
    }
  }

  // Sync order-related actions
  private async syncOrderAction(action: OfflineAction): Promise<void> {
    const { orderService } = await import('../database')
    
    switch (action.type) {
      case 'CREATE':
        await orderService.createOrder(action.data)
        break
      case 'UPDATE':
        await orderService.updateOrderStatus(action.data.id, action.data.status, action.data.tracking)
        break
      default:
        throw new Error(`Unsupported order action: ${action.type}`)
    }
  }

  // Sync message-related actions
  private async syncMessageAction(action: OfflineAction): Promise<void> {
    const { ChatService } = await import('../database/ChatService')
    const chatService = new ChatService()
    
    switch (action.type) {
      case 'CREATE':
        await chatService.sendMessage(
          action.data.conversationId,
          action.data.senderId,
          action.data.content,
          action.data.type,
          action.data.attachments,
          action.data.replyTo
        )
        break
      case 'UPDATE':
        if (action.data.isRead !== undefined) {
          await chatService.markMessagesAsRead(action.data.conversationId, action.data.userId)
        }
        break
      default:
        throw new Error(`Unsupported message action: ${action.type}`)
    }
  }

  // Sync user-related actions
  private async syncUserAction(action: OfflineAction): Promise<void> {
    const { userService } = await import('../database')
    
    switch (action.type) {
      case 'UPDATE':
        await userService.updateUserProfile(action.data.userId, action.data.updates)
        break
      case 'CREATE':
        if (action.data.type === 'address') {
          await userService.addUserAddress(action.data.userId, action.data.address)
        } else if (action.data.type === 'payment') {
          await userService.addPaymentMethod(action.data.userId, action.data.paymentMethod)
        }
        break
      default:
        throw new Error(`Unsupported user action: ${action.type}`)
    }
  }

  // Get current sync status
  getSyncStatus(): SyncStatus {
    return {
      isOnline: this.isOnline,
      lastSyncTime: this.pendingActions.length === 0 ? new Date().toISOString() : null,
      pendingActions: this.pendingActions.length,
      isSyncing: this.syncInProgress,
      syncErrors: []
    }
  }

  // Subscribe to sync status changes
  onSyncStatusChange(callback: (status: SyncStatus) => void): () => void {
    this.syncCallbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.syncCallbacks.indexOf(callback)
      if (index >= 0) {
        this.syncCallbacks.splice(index, 1)
      }
    }
  }

  // Notify all sync callbacks
  private notifySyncCallbacks(): void {
    const status = this.getSyncStatus()
    this.syncCallbacks.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('Error in sync callback:', error)
      }
    })
  }

  // Force sync (manual trigger)
  async forcSync(): Promise<void> {
    if (this.isOnline) {
      await this.syncPendingActions()
    } else {
      throw new Error('Cannot sync while offline')
    }
  }

  // Clear all pending actions (use with caution)
  async clearPendingActions(): Promise<void> {
    this.pendingActions = []
    await this.savePendingActions()
    this.notifySyncCallbacks()
  }

  // Get pending actions for debugging
  getPendingActions(): OfflineAction[] {
    return [...this.pendingActions]
  }

  // Check if device is online
  isDeviceOnline(): boolean {
    return this.isOnline
  }

  // Get network info
  async getNetworkInfo(): Promise<any> {
    return await NetInfo.fetch()
  }
}

export const OfflineService = new OfflineServiceClass()
