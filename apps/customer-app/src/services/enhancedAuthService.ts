import { Account, ID, Models } from 'appwrite'
import { client } from '../lib/appwrite'

export interface UserProfile {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  emailVerification: boolean
  phoneVerification: boolean
  preferences: {
    notifications: boolean
    locationTracking: boolean
    biometricAuth: boolean
  }
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  name: string
  email: string
  password: string
  phone?: string
}

export interface PasswordResetData {
  email: string
}

export interface VerificationData {
  userId: string
  secret: string
}

export class EnhancedAuthService {
  private account: Account

  constructor() {
    this.account = new Account(client)
  }

  // Authentication Methods
  async login(credentials: LoginCredentials): Promise<Models.Session> {
    try {
      const session = await this.account.createEmailSession(
        credentials.email,
        credentials.password
      )
      return session
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async register(data: RegisterData): Promise<Models.User<Models.Preferences>> {
    try {
      const user = await this.account.create(
        ID.unique(),
        data.email,
        data.password,
        data.name
      )

      // Automatically log in after registration
      await this.login({
        email: data.email,
        password: data.password,
      })

      // Send email verification
      await this.sendEmailVerification()

      return user
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async logout(): Promise<void> {
    try {
      await this.account.deleteSession('current')
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async logoutAll(): Promise<void> {
    try {
      await this.account.deleteSessions()
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async getCurrentUser(): Promise<Models.User<Models.Preferences> | null> {
    try {
      return await this.account.get()
    } catch (error) {
      return null
    }
  }

  async getCurrentSession(): Promise<Models.Session | null> {
    try {
      return await this.account.getSession('current')
    } catch (error) {
      return null
    }
  }

  // Password Management
  async forgotPassword(email: string): Promise<void> {
    try {
      await this.account.createRecovery(
        email,
        `${process.env.EXPO_PUBLIC_APP_URL}/reset-password`
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async resetPassword(
    userId: string,
    secret: string,
    password: string,
    confirmPassword: string
  ): Promise<void> {
    try {
      await this.account.updateRecovery(
        userId,
        secret,
        password,
        confirmPassword
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async updatePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      await this.account.updatePassword(newPassword, currentPassword)
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // Email Verification
  async sendEmailVerification(): Promise<void> {
    try {
      await this.account.createVerification(
        `${process.env.EXPO_PUBLIC_APP_URL}/verify-email`
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async verifyEmail(userId: string, secret: string): Promise<void> {
    try {
      await this.account.updateVerification(userId, secret)
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // Phone Verification
  async sendPhoneVerification(phone: string): Promise<void> {
    try {
      await this.account.createPhoneVerification()
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async verifyPhone(userId: string, secret: string): Promise<void> {
    try {
      await this.account.updatePhoneVerification(userId, secret)
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // Profile Management
  async updateProfile(data: {
    name?: string
    email?: string
    phone?: string
  }): Promise<Models.User<Models.Preferences>> {
    try {
      let user = await this.account.get()

      if (data.name) {
        user = await this.account.updateName(data.name)
      }

      if (data.email) {
        user = await this.account.updateEmail(data.email, '')
      }

      if (data.phone) {
        user = await this.account.updatePhone(data.phone, '')
      }

      return user
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async updatePreferences(preferences: Record<string, any>): Promise<Models.User<Models.Preferences>> {
    try {
      return await this.account.updatePrefs(preferences)
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // OAuth Methods
  async loginWithGoogle(): Promise<void> {
    try {
      await this.account.createOAuth2Session(
        'google',
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/success`,
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/failure`
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async loginWithApple(): Promise<void> {
    try {
      await this.account.createOAuth2Session(
        'apple',
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/success`,
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/failure`
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async loginWithFacebook(): Promise<void> {
    try {
      await this.account.createOAuth2Session(
        'facebook',
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/success`,
        `${process.env.EXPO_PUBLIC_APP_URL}/auth/failure`
      )
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // Session Management
  async getSessions(): Promise<Models.SessionList> {
    try {
      return await this.account.listSessions()
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.account.deleteSession(sessionId)
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error))
    }
  }

  // Utility Methods
  private getErrorMessage(error: any): string {
    if (error?.message) {
      return error.message
    }
    
    if (error?.code) {
      switch (error.code) {
        case 401:
          return 'Invalid credentials. Please check your email and password.'
        case 409:
          return 'An account with this email already exists.'
        case 429:
          return 'Too many requests. Please try again later.'
        case 500:
          return 'Server error. Please try again later.'
        default:
          return 'An unexpected error occurred. Please try again.'
      }
    }

    return 'An unexpected error occurred. Please try again.'
  }

  // Helper method to transform Appwrite user to UserProfile
  transformUserToProfile(user: Models.User<Models.Preferences>): UserProfile {
    return {
      id: user.$id,
      name: user.name,
      email: user.email,
      phone: user.phone || undefined,
      avatar: user.prefs?.avatar || undefined,
      emailVerification: user.emailVerification,
      phoneVerification: user.phoneVerification,
      preferences: {
        notifications: user.prefs?.notifications ?? true,
        locationTracking: user.prefs?.locationTracking ?? true,
        biometricAuth: user.prefs?.biometricAuth ?? false,
      },
      createdAt: user.$createdAt,
      updatedAt: user.$updatedAt,
    }
  }
}

export const enhancedAuthService = new EnhancedAuthService()
