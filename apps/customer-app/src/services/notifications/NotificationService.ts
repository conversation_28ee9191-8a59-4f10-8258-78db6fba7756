import { Alert, Platform } from 'react-native'
import PushNotification from 'react-native-push-notification'
import PushNotificationIOS from '@react-native-community/push-notification-ios'

export interface NotificationData {
  id: string
  title: string
  message: string
  type: 'order' | 'chat' | 'promotion' | 'system'
  data?: any
  timestamp: string
  isRead: boolean
  priority: 'low' | 'normal' | 'high'
}

export interface PushNotificationPayload {
  title: string
  message: string
  data?: any
  channelId?: string
  priority?: 'low' | 'normal' | 'high'
  sound?: boolean
  vibrate?: boolean
  largeIcon?: string
  smallIcon?: string
  color?: string
  actions?: Array<{
    id: string
    title: string
    icon?: string
  }>
}

class NotificationServiceClass {
  private isInitialized = false
  private deviceToken: string | null = null
  private notificationHandlers: Map<string, (notification: NotificationData) => void> = new Map()

  // Initialize push notifications
  initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isInitialized) {
        resolve()
        return
      }

      // Configure push notifications
      PushNotification.configure({
        // Called when token is generated
        onRegister: (token) => {
          console.log('Push notification token:', token)
          this.deviceToken = token.token
        },

        // Called when a remote notification is received
        onNotification: (notification) => {
          console.log('Push notification received:', notification)
          this.handleIncomingNotification(notification)

          // Required on iOS only
          if (Platform.OS === 'ios') {
            notification.finish(PushNotificationIOS.FetchResult.NoData)
          }
        },

        // Called when a remote notification is received while app is in foreground
        onAction: (notification) => {
          console.log('Push notification action:', notification)
          this.handleNotificationAction(notification)
        },

        // Should the initial notification be popped automatically
        popInitialNotification: true,

        // Permissions
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },

        // Should registration happen automatically on app startup
        requestPermissions: Platform.OS === 'ios',
      })

      // Create notification channels for Android
      if (Platform.OS === 'android') {
        this.createNotificationChannels()
      }

      this.isInitialized = true
      resolve()
    })
  }

  // Create notification channels for Android
  private createNotificationChannels(): void {
    const channels = [
      {
        channelId: 'order_updates',
        channelName: 'Order Updates',
        channelDescription: 'Notifications about your order status',
        importance: 4, // HIGH
        vibrate: true,
        sound: 'default',
      },
      {
        channelId: 'chat_messages',
        channelName: 'Chat Messages',
        channelDescription: 'New messages from restaurants and delivery drivers',
        importance: 3, // DEFAULT
        vibrate: true,
        sound: 'default',
      },
      {
        channelId: 'promotions',
        channelName: 'Promotions',
        channelDescription: 'Special offers and promotions',
        importance: 2, // LOW
        vibrate: false,
        sound: 'default',
      },
      {
        channelId: 'system',
        channelName: 'System',
        channelDescription: 'System notifications and updates',
        importance: 3, // DEFAULT
        vibrate: false,
        sound: 'default',
      },
    ]

    channels.forEach(channel => {
      PushNotification.createChannel(
        {
          channelId: channel.channelId,
          channelName: channel.channelName,
          channelDescription: channel.channelDescription,
          playSound: true,
          soundName: channel.sound,
          importance: channel.importance,
          vibrate: channel.vibrate,
        },
        (created) => console.log(`Channel ${channel.channelId} created:`, created)
      )
    })
  }

  // Show local notification
  showLocalNotification(payload: PushNotificationPayload): void {
    const channelId = payload.channelId || this.getChannelIdFromType(payload.data?.type || 'system')

    PushNotification.localNotification({
      title: payload.title,
      message: payload.message,
      channelId,
      priority: payload.priority || 'normal',
      playSound: payload.sound !== false,
      vibrate: payload.vibrate !== false,
      largeIcon: payload.largeIcon || 'ic_launcher',
      smallIcon: payload.smallIcon || 'ic_notification',
      color: payload.color || '#FF6B35',
      userInfo: payload.data || {},
      actions: payload.actions || [],
    })
  }

  // Show order status notification
  showOrderNotification(orderId: string, status: string, message: string): void {
    this.showLocalNotification({
      title: `Order ${status}`,
      message,
      channelId: 'order_updates',
      priority: 'high',
      data: {
        type: 'order',
        orderId,
        status,
      },
      actions: [
        {
          id: 'view_order',
          title: 'View Order',
        },
        {
          id: 'track_order',
          title: 'Track',
        },
      ],
    })
  }

  // Show chat message notification
  showChatNotification(conversationId: string, senderName: string, message: string): void {
    this.showLocalNotification({
      title: `Message from ${senderName}`,
      message,
      channelId: 'chat_messages',
      priority: 'normal',
      data: {
        type: 'chat',
        conversationId,
        senderName,
      },
      actions: [
        {
          id: 'reply',
          title: 'Reply',
        },
        {
          id: 'view_chat',
          title: 'View',
        },
      ],
    })
  }

  // Show promotion notification
  showPromotionNotification(title: string, message: string, promoData?: any): void {
    this.showLocalNotification({
      title,
      message,
      channelId: 'promotions',
      priority: 'low',
      data: {
        type: 'promotion',
        ...promoData,
      },
      actions: [
        {
          id: 'view_offer',
          title: 'View Offer',
        },
      ],
    })
  }

  // Handle incoming notifications
  private handleIncomingNotification(notification: any): void {
    const notificationData: NotificationData = {
      id: notification.id || Date.now().toString(),
      title: notification.title || '',
      message: notification.message || notification.body || '',
      type: notification.data?.type || 'system',
      data: notification.data,
      timestamp: new Date().toISOString(),
      isRead: false,
      priority: notification.data?.priority || 'normal',
    }

    // Trigger registered handlers
    this.notificationHandlers.forEach(handler => {
      try {
        handler(notificationData)
      } catch (error) {
        console.error('Error in notification handler:', error)
      }
    })

    // Show in-app notification if app is in foreground
    if (notification.foreground) {
      this.showInAppNotification(notificationData)
    }
  }

  // Handle notification actions
  private handleNotificationAction(notification: any): void {
    const { action, data } = notification

    switch (action) {
      case 'view_order':
        // Navigate to order details
        console.log('Navigate to order:', data?.orderId)
        break
      case 'track_order':
        // Navigate to order tracking
        console.log('Track order:', data?.orderId)
        break
      case 'reply':
      case 'view_chat':
        // Navigate to chat
        console.log('Navigate to chat:', data?.conversationId)
        break
      case 'view_offer':
        // Navigate to promotion
        console.log('View promotion:', data)
        break
      default:
        console.log('Unknown notification action:', action)
    }
  }

  // Show in-app notification
  private showInAppNotification(notification: NotificationData): void {
    Alert.alert(
      notification.title,
      notification.message,
      [
        {
          text: 'Dismiss',
          style: 'cancel',
        },
        {
          text: 'View',
          onPress: () => {
            // Handle view action based on notification type
            this.handleNotificationAction({
              action: this.getDefaultActionForType(notification.type),
              data: notification.data,
            })
          },
        },
      ]
    )
  }

  // Register notification handler
  addNotificationHandler(id: string, handler: (notification: NotificationData) => void): void {
    this.notificationHandlers.set(id, handler)
  }

  // Remove notification handler
  removeNotificationHandler(id: string): void {
    this.notificationHandlers.delete(id)
  }

  // Get device token
  getDeviceToken(): string | null {
    return this.deviceToken
  }

  // Request permissions (iOS)
  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      const permissions = await PushNotificationIOS.requestPermissions()
      return permissions.alert && permissions.badge && permissions.sound
    }
    return true // Android permissions are handled in manifest
  }

  // Clear all notifications
  clearAllNotifications(): void {
    PushNotification.cancelAllLocalNotifications()
    if (Platform.OS === 'ios') {
      PushNotificationIOS.removeAllDeliveredNotifications()
    }
  }

  // Clear specific notification
  clearNotification(notificationId: string): void {
    PushNotification.cancelLocalNotifications({ id: notificationId })
  }

  // Set badge count (iOS)
  setBadgeCount(count: number): void {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.setApplicationIconBadgeNumber(count)
    }
  }

  // Helper methods
  private getChannelIdFromType(type: string): string {
    switch (type) {
      case 'order':
        return 'order_updates'
      case 'chat':
        return 'chat_messages'
      case 'promotion':
        return 'promotions'
      default:
        return 'system'
    }
  }

  private getDefaultActionForType(type: string): string {
    switch (type) {
      case 'order':
        return 'view_order'
      case 'chat':
        return 'view_chat'
      case 'promotion':
        return 'view_offer'
      default:
        return 'view'
    }
  }
}

export const NotificationService = new NotificationServiceClass()
