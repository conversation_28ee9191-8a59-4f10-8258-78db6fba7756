import React from 'react';
import { Avatar as UIAvatar, AvatarFallbackText, AvatarImage } from '@hvppyplug/ui-components-v2/src/ui/avatar';

interface AvatarProps {
  name?: string;
  source?: { uri: string };
  size?: 'sm' | 'md' | 'lg';
  badge?: boolean;
}

export const Avatar: React.FC<AvatarProps> = ({ 
  name,
  source,
  size = 'md',
  badge = false
}) => {
  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <UIAvatar size={size} badge={badge}>
      {source ? (
        <AvatarImage source={source} />
      ) : (
        <AvatarFallbackText>{getInitials(name)}</AvatarFallbackText>
      )}
    </UIAvatar>
  );
};