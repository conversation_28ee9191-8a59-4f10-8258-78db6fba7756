import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Skeleton as UISkeleton } from '@hvppyplug/ui-components-v2/src/ui/skeleton';

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  variant?: 'rect' | 'circle' | 'text';
  isLoading?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({ 
  width,
  height,
  borderRadius = 4,
  variant = 'rect',
  isLoading = true
}) => {
  if (!isLoading) {
    return null;
  }

  return (
    <UISkeleton 
      style={[
        styles.skeleton,
        width !== undefined && { width },
        height !== undefined && { height },
        variant === 'circle' && styles.circle,
        variant === 'text' && styles.text,
        { borderRadius: variant === 'circle' ? 999 : borderRadius }
      ]}
    />
  );
};

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: '#e0e0e0',
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 999,
  },
  text: {
    height: 16,
    borderRadius: 4,
  },
});