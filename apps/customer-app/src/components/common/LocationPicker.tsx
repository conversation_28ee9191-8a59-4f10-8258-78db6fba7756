import React from 'react';
import { Actionsheet, ActionsheetBackdrop, ActionsheetContent, ActionsheetDragIndicator, ActionsheetDragIndicatorWrapper, ActionsheetItem, ActionsheetItemText, ActionsheetFlatList } from '@hvppyplug/ui-components-v2/src/ui/actionsheet';

interface LocationOption {
  id: string;
  name: string;
  address: string;
}

interface LocationPickerProps {
  isOpen: boolean;
  onClose: () => void;
  locations: LocationOption[];
  onSelect: (location: LocationOption) => void;
  title?: string;
}

export const LocationPicker: React.FC<LocationPickerProps> = ({ 
  isOpen,
  onClose,
  locations,
  onSelect,
  title = "Select Location"
}) => {
  const handleSelect = (location: LocationOption) => {
    onSelect(location);
    onClose();
  };

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>
        <ActionsheetFlatList
          data={locations}
          ListHeaderComponent={<ActionsheetItemText style={{ textAlign: 'center', padding: 16, fontWeight: 'bold' }}>{title}</ActionsheetItemText>}
          renderItem={({ item }: { item: LocationOption }) => (
            <ActionsheetItem key={item.id} onPress={() => handleSelect(item)}>
              <ActionsheetItemText>{item.name}</ActionsheetItemText>
              <ActionsheetItemText size="xs" style={{ color: '#666' }}>{item.address}</ActionsheetItemText>
            </ActionsheetItem>
          )}
          keyExtractor={(item) => item.id}
        />
      </ActionsheetContent>
    </Actionsheet>
  );
};