import React from 'react';
import { Input as UIInput, InputField, InputIcon, InputSlot } from '@hvppyplug/ui-components-v2/src/ui/input';
import { Icon } from '@hvppyplug/ui-components-v2/src/ui/icon';
import { SearchIcon } from 'lucide-react-native';

interface SearchInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onClear?: () => void;
}

export const SearchInput: React.FC<SearchInputProps> = ({ 
  value,
  onChangeText,
  placeholder = "Search...",
  onClear
}) => {
  return (
    <UIInput>
      <InputSlot pl="$3">
        <InputIcon as={SearchIcon} />
      </InputSlot>
      <InputField
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
      />
      {value.length > 0 && onClear && (
        <InputSlot pr="$3">
          <Icon
            as={() => <Text onPress={onClear}>✕</Text>}
          />
        </InputSlot>
      )}
    </UIInput>
  );
};