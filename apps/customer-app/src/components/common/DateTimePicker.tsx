import React, { useState } from 'react';
import { AlertDialog, AlertDialogBackdrop, AlertDialogContent, AlertDialogHeader, AlertDialogFooter, AlertDialogBody, Heading, Button, ButtonText, ButtonGroup } from '@hvppyplug/ui-components-v2/src/ui/alert-dialog';
import { View, StyleSheet } from 'react-native';
import RNDateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';

interface DateTimePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  title?: string;
  confirmText?: string;
  cancelText?: string;
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({ 
  isOpen,
  onClose,
  onSelect,
  mode = 'date',
  title = "Select Date & Time",
  confirmText = "Confirm",
  cancelText = "Cancel"
}) => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  const handleChange = (event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  const handleConfirm = () => {
    onSelect(selectedDate);
    onClose();
  };

  return (
    <AlertDialog isOpen={isOpen} onClose={onClose}>
      <AlertDialogBackdrop />
      <AlertDialogContent>
        <AlertDialogHeader>
          <Heading>{title}</Heading>
        </AlertDialogHeader>
        <AlertDialogBody>
          <View style={styles.pickerContainer}>
            <RNDateTimePicker
              value={selectedDate}
              mode={mode as any}
              display="spinner"
              onChange={handleChange}
            />
          </View>
        </AlertDialogBody>
        <AlertDialogFooter>
          <ButtonGroup space="md">
            <Button variant="outline" onPress={onClose}>
              <ButtonText>{cancelText}</ButtonText>
            </Button>
            <Button action="primary" onPress={handleConfirm}>
              <ButtonText>{confirmText}</ButtonText>
            </Button>
          </ButtonGroup>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const styles = StyleSheet.create({
  pickerContainer: {
    alignItems: 'center',
  },
});