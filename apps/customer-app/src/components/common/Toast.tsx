import React from 'react';
import { Toast as UIToast, ToastTitle, ToastDescription, ToastAction, ToastActionProps } from '@hvppyplug/ui-components-v2/src/ui/toast';
import { Button, ButtonText } from '@hvppyplug/ui-components-v2/src/ui/button';

interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  action?: {
    label: string;
    onPress: () => void;
  };
  variant?: 'success' | 'error' | 'warning' | 'info';
}

export const Toast: React.FC<ToastProps> = ({ 
  id,
  title,
  description,
  action,
  variant = 'info'
}) => {
  return (
    <UIToast action={variant} nativeID={id}>
      {title && <ToastTitle>{title}</ToastTitle>}
      {description && <ToastDescription>{description}</ToastDescription>}
      {action && (
        <ToastAction asChild>
          <Button variant="link" onPress={action.onPress}>
            <ButtonText>{action.label}</ButtonText>
          </Button>
        </ToastAction>
      )}
    </UIToast>
  );
};