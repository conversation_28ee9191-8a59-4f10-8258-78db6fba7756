import React from 'react';
import { Actionsheet, ActionsheetBackdrop, ActionsheetContent, ActionsheetDragIndicator, ActionsheetDragIndicatorWrapper, ActionsheetItem, ActionsheetItemText } from '@hvppyplug/ui-components-v2/src/ui/actionsheet';
import { Icon } from '@hvppyplug/ui-components-v2/src/ui/icon';
import { Camera, Image as ImageIcon, File } from 'lucide-react-native';

interface ImagePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onCameraSelect: () => void;
  onGallerySelect: () => void;
  onFileSelect?: () => void;
  title?: string;
}

export const ImagePicker: React.FC<ImagePickerProps> = ({ 
  isOpen,
  onClose,
  onCameraSelect,
  onGallerySelect,
  onFileSelect,
  title = "Select Image Source"
}) => {
  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>
        <ActionsheetItemText style={{ textAlign: 'center', padding: 16, fontWeight: 'bold' }}>{title}</ActionsheetItemText>
        <ActionsheetItem onPress={onCameraSelect}>
          <Icon as={Camera} />
          <ActionsheetItemText>Take Photo</ActionsheetItemText>
        </ActionsheetItem>
        <ActionsheetItem onPress={onGallerySelect}>
          <Icon as={ImageIcon} />
          <ActionsheetItemText>Choose from Gallery</ActionsheetItemText>
        </ActionsheetItem>
        {onFileSelect && (
          <ActionsheetItem onPress={onFileSelect}>
            <Icon as={File} />
            <ActionsheetItemText>Select from Files</ActionsheetItemText>
          </ActionsheetItem>
        )}
      </ActionsheetContent>
    </Actionsheet>
  );
};