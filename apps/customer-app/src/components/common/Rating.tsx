import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Progress, ProgressFilledTrack } from '@hvppyplug/ui-components-v2/src/ui/progress';
import { Text } from '@hvppyplug/ui-components-v2/src/ui/text';

interface RatingProps {
  value: number; // 0-5
  max?: number; // Max rating (default 5)
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const Rating: React.FC<RatingProps> = ({ 
  value,
  max = 5,
  showValue = false,
  size = 'md'
}) => {
  const percentage = (value / max) * 100;
  
  const sizeStyles = {
    sm: { height: 8 },
    md: { height: 12 },
    lg: { height: 16 }
  };

  return (
    <View style={styles.container}>
      <Progress 
        value={percentage} 
        size={size}
        style={[styles.progress, sizeStyles[size]]}
      >
        <ProgressFilledTrack style={styles.filledTrack} />
      </Progress>
      {showValue && (
        <Text style={styles.valueText}>{value.toFixed(1)}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progress: {
    width: 80,
    marginHorizontal: 8,
  },
  filledTrack: {
    backgroundColor: '#f1c40f',
  },
  valueText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
});