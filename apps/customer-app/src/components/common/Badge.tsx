import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Badge as UIBadge, BadgeText } from '@hvppyplug/ui-components-v2/src/ui/badge';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
}

export const Badge: React.FC<BadgeProps> = ({ 
  children,
  variant = 'default',
  size = 'md'
}) => {
  return (
    <UIBadge 
      action={variant === 'success' ? 'success' : variant === 'warning' ? 'warning' : variant === 'error' ? 'error' : variant === 'info' ? 'info' : 'default'}
      size={size}
    >
      <BadgeText>{children}</BadgeText>
    </UIBadge>
  );
};