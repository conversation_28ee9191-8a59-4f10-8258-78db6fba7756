import React from 'react';
import { Button as UIButton, ButtonText } from '@hvppyplug/ui-components-v2/src/ui/button';

interface FilterChipProps {
  children: React.ReactNode;
  isSelected?: boolean;
  onPress: () => void;
  variant?: 'solid' | 'outline';
}

export const FilterChip: React.FC<FilterChipProps> = ({ 
  children,
  isSelected = false,
  onPress,
  variant = 'outline'
}) => {
  return (
    <UIButton
      variant={isSelected ? 'solid' : variant}
      action={isSelected ? 'primary' : 'secondary'}
      size="sm"
      onPress={onPress}
    >
      <ButtonText>{children}</ButtonText>
    </UIButton>
  );
};