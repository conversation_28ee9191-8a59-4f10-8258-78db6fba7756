import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Center } from '@hvppyplug/ui-components-v2/src/ui/center';
import { Button } from '@hvppyplug/ui-components-v2/src/ui/button';

interface EmptyStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryText?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ 
  title = "No data found", 
  message = "There's no data to display right now.",
  onRetry,
  retryText = "Retry"
}) => {
  return (
    <Center style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>
        {onRetry && (
          <Button onPress={onRetry} style={styles.button}>
            {retryText}
          </Button>
        )}
      </View>
    </Center>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    color: '#666',
  },
  button: {
    marginTop: 10,
  },
});