import { ReactNode } from 'react'
import { ViewStyle, TextStyle, ImageStyle } from 'react-native'
import type { IButtonProps, IButtonTextProps } from '@hvppyplug/ui-components-v2/src/ui/button'
import type { IInputProps, IInputFieldProps } from '@hvppyplug/ui-components-v2/src/ui/input'
import type { IModalProps } from '@hvppyplug/ui-components-v2/src/ui/modal'

// Re-exporting UI component types
export type ButtonProps = IButtonProps
export type InputProps = IInputProps
export type ModalProps = IModalProps

export interface ToastProps {
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  position?: 'top' | 'bottom'
  onHide?: () => void
}

export interface BadgeProps {
  text: string | number
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  size?: 'small' | 'medium' | 'large'
  style?: ViewStyle
  textStyle?: TextStyle
}

export interface AvatarProps {
  source?: string
  name?: string
  size?: number
  style?: ImageStyle
}

export interface RatingProps {
  rating: number
  maxRating?: number
  size?: number
  readonly?: boolean
  onRatingChange?: (rating: number) => void
  showText?: boolean
  style?: ViewStyle
}

export interface EmptyStateProps {
  title: string
  description?: string
  icon?: ReactNode
  action?: {
    title: string
    onPress: () => void
  }
  style?: ViewStyle
}

export interface ErrorStateProps {
  title: string
  description?: string
  onRetry?: () => void
  style?: ViewStyle
}