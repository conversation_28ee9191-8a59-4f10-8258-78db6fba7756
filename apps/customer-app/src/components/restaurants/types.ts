export interface Restaurant {
  id: string
  name: string
  description: string
  image: string
  images: string[]
  cuisine: string[]
  rating: number
  reviewCount: number
  deliveryTime: string
  deliveryFee: number
  minimumOrder: number
  isOpen: boolean
  isFeatured: boolean
  distance: number
  address: {
    street: string
    city: string
    province: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  contact: {
    phone: string
    email?: string
  }
  businessHours: BusinessHours[]
  menu: MenuCategory[]
  tags: string[]
  createdAt: string
  updatedAt: string
}

export interface MenuCategory {
  id: string
  name: string
  description?: string
  items: MenuItem[]
  isAvailable: boolean
  sortOrder: number
}

export interface MenuItem {
  id: string
  name: string
  description: string
  image?: string
  price: number
  originalPrice?: number
  isAvailable: boolean
  isPopular: boolean
  isVegetarian: boolean
  isVegan: boolean
  isGlutenFree: boolean
  isSpicy: boolean
  allergens: string[]
  nutritionInfo?: NutritionInfo
  customizations: MenuCustomization[]
  preparationTime: number
  tags: string[]
}

export interface MenuCustomization {
  id: string
  name: string
  type: 'single' | 'multiple'
  isRequired: boolean
  options: CustomizationOption[]
}

export interface CustomizationOption {
  id: string
  name: string
  price: number
  isDefault: boolean
  isAvailable: boolean
}

export interface NutritionInfo {
  calories: number
  protein: number
  carbs: number
  fat: number
  fiber: number
  sugar: number
  sodium: number
}

export interface BusinessHours {
  day: string
  openTime: string
  closeTime: string
  isOpen: boolean
}

export interface RestaurantReview {
  id: string
  customerId: string
  customerName: string
  customerAvatar?: string
  rating: number
  comment: string
  images?: string[]
  orderId?: string
  createdAt: string
  response?: {
    message: string
    createdAt: string
  }
}

export interface RestaurantFilters {
  cuisine: string[]
  priceRange: [number, number]
  rating: number
  deliveryTime: number
  isOpen: boolean
  isFeatured: boolean
  distance: number
  sortBy: 'distance' | 'rating' | 'delivery_time' | 'popularity'
}
