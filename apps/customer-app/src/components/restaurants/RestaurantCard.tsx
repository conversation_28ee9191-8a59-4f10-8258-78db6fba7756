import React from 'react'
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native'
import { StarIcon, ClockIcon, TruckIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { Restaurant } from './types'

interface RestaurantCardProps {
  restaurant: Restaurant
  onPress: (restaurant: Restaurant) => void
  variant?: 'default' | 'compact' | 'featured'
  showDistance?: boolean
  showDeliveryFee?: boolean
}

export function RestaurantCard({ 
  restaurant, 
  onPress, 
  variant = 'default',
  showDistance = true,
  showDeliveryFee = true 
}: RestaurantCardProps) {
  const { colors, typography } = useTheme()

  const handlePress = () => {
    onPress(restaurant)
  }

  const renderBadges = () => {
    const badges = []
    
    if (restaurant.isFeatured) {
      badges.push(
        <View key="featured" style={[styles.badge, { backgroundColor: colors.primary[500] }]}>
          <Text style={[styles.badgeText, { color: colors.white }]}>Featured</Text>
        </View>
      )
    }

    if (!restaurant.isOpen) {
      badges.push(
        <View key="closed" style={[styles.badge, { backgroundColor: colors.error[500] }]}>
          <Text style={[styles.badgeText, { color: colors.white }]}>Closed</Text>
        </View>
      )
    }

    return badges.length > 0 ? (
      <View style={styles.badgeContainer}>
        {badges}
      </View>
    ) : null
  }

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[
        styles.compactCard,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Image
        source={{ uri: restaurant.image }}
        style={styles.compactImage}
        defaultSource={require('../../../assets/placeholder-restaurant.png')}
      />
      <View style={styles.compactContent}>
        <Text style={[typography.body.medium, { color: colors.text.primary }]} numberOfLines={1}>
          {restaurant.name}
        </Text>
        <Text style={[typography.body.small, { color: colors.text.secondary }]} numberOfLines={1}>
          {restaurant.cuisine.join(', ')}
        </Text>
        <View style={styles.compactMeta}>
          <View style={styles.rating}>
            <StarIcon size={12} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 2 }]}>
              {restaurant.rating.toFixed(1)}
            </Text>
          </View>
          <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
            {restaurant.deliveryTime}
          </Text>
        </View>
      </View>
      {renderBadges()}
    </TouchableOpacity>
  )

  const renderFeaturedCard = () => (
    <TouchableOpacity
      style={[
        styles.featuredCard,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Image
        source={{ uri: restaurant.image }}
        style={styles.featuredImage}
        defaultSource={require('../../../assets/placeholder-restaurant.png')}
      />
      <View style={styles.featuredContent}>
        <Text style={[typography.heading.small, { color: colors.text.primary }]} numberOfLines={2}>
          {restaurant.name}
        </Text>
        <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 4 }]} numberOfLines={2}>
          {restaurant.description}
        </Text>
        <View style={[styles.metaRow, { marginTop: 8 }]}>
          <View style={styles.rating}>
            <StarIcon size={16} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={[typography.body.medium, { color: colors.text.primary, marginLeft: 4 }]}>
              {restaurant.rating.toFixed(1)} ({restaurant.reviewCount})
            </Text>
          </View>
          <View style={styles.metaItem}>
            <ClockIcon size={16} color={colors.text.secondary} />
            <Text style={[typography.body.medium, { color: colors.text.secondary, marginLeft: 4 }]}>
              {restaurant.deliveryTime}
            </Text>
          </View>
        </View>
        {(showDistance || showDeliveryFee) && (
          <View style={[styles.metaRow, { marginTop: 4 }]}>
            {showDistance && (
              <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
                {restaurant.distance.toFixed(1)} km away
              </Text>
            )}
            {showDeliveryFee && (
              <View style={styles.metaItem}>
                <TruckIcon size={14} color={colors.text.secondary} />
                <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 2 }]}>
                  R{restaurant.deliveryFee.toFixed(2)}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
      {renderBadges()}
    </TouchableOpacity>
  )

  const renderDefaultCard = () => (
    <TouchableOpacity
      style={[
        styles.defaultCard,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Image
        source={{ uri: restaurant.image }}
        style={styles.defaultImage}
        defaultSource={require('../../../assets/placeholder-restaurant.png')}
      />
      <View style={styles.defaultContent}>
        <View style={styles.header}>
          <Text style={[typography.body.large, { color: colors.text.primary }]} numberOfLines={1}>
            {restaurant.name}
          </Text>
          <View style={styles.rating}>
            <StarIcon size={14} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 2 }]}>
              {restaurant.rating.toFixed(1)}
            </Text>
          </View>
        </View>
        
        <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]} numberOfLines={1}>
          {restaurant.cuisine.join(', ')}
        </Text>

        <View style={[styles.metaRow, { marginTop: 8 }]}>
          <View style={styles.metaItem}>
            <ClockIcon size={14} color={colors.text.secondary} />
            <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 4 }]}>
              {restaurant.deliveryTime}
            </Text>
          </View>
          
          {showDistance && (
            <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
              {restaurant.distance.toFixed(1)} km
            </Text>
          )}
          
          {showDeliveryFee && (
            <View style={styles.metaItem}>
              <TruckIcon size={14} color={colors.text.secondary} />
              <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 2 }]}>
                R{restaurant.deliveryFee.toFixed(2)}
              </Text>
            </View>
          )}
        </View>
      </View>
      {renderBadges()}
    </TouchableOpacity>
  )

  switch (variant) {
    case 'compact':
      return renderCompactCard()
    case 'featured':
      return renderFeaturedCard()
    default:
      return renderDefaultCard()
  }
}

const styles = StyleSheet.create({
  defaultCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 16,
  },
  compactCard: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 8,
    height: 80,
  },
  featuredCard: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 20,
  },
  defaultImage: {
    width: '100%',
    height: 160,
  },
  compactImage: {
    width: 80,
    height: 80,
  },
  featuredImage: {
    width: '100%',
    height: 200,
  },
  defaultContent: {
    padding: 12,
  },
  compactContent: {
    flex: 1,
    padding: 8,
    justifyContent: 'space-between',
  },
  featuredContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  compactMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    gap: 4,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
})
