import React, { useState, useCallback } from 'react'
import { FlatList, View, Text, RefreshControl, StyleSheet } from 'react-native'
import { useTheme } from '../../hooks/useTheme'
import { RestaurantCard } from './RestaurantCard'
import { EmptyState } from '../common/EmptyState'
import { LoadingSpinner } from '../common/LoadingSpinner'
import type { Restaurant, RestaurantFilters } from './types'

interface RestaurantListProps {
  restaurants: Restaurant[]
  loading?: boolean
  refreshing?: boolean
  onRefresh?: () => void
  onLoadMore?: () => void
  onRestaurantPress: (restaurant: Restaurant) => void
  filters?: RestaurantFilters
  variant?: 'default' | 'compact' | 'featured'
  showDistance?: boolean
  showDeliveryFee?: boolean
  emptyTitle?: string
  emptyDescription?: string
  hasMore?: boolean
}

export function RestaurantList({
  restaurants,
  loading = false,
  refreshing = false,
  onRefresh,
  onLoadMore,
  onRestaurantPress,
  filters,
  variant = 'default',
  showDistance = true,
  showDeliveryFee = true,
  emptyTitle = 'No restaurants found',
  emptyDescription = 'Try adjusting your filters or search in a different area.',
  hasMore = false
}: RestaurantListProps) {
  const { colors, typography } = useTheme()
  const [loadingMore, setLoadingMore] = useState(false)

  const handleLoadMore = useCallback(async () => {
    if (loadingMore || !hasMore || !onLoadMore) return
    
    setLoadingMore(true)
    try {
      await onLoadMore()
    } finally {
      setLoadingMore(false)
    }
  }, [loadingMore, hasMore, onLoadMore])

  const renderRestaurant = useCallback(({ item }: { item: Restaurant }) => (
    <RestaurantCard
      restaurant={item}
      onPress={onRestaurantPress}
      variant={variant}
      showDistance={showDistance}
      showDeliveryFee={showDeliveryFee}
    />
  ), [onRestaurantPress, variant, showDistance, showDeliveryFee])

  const renderFooter = useCallback(() => {
    if (!loadingMore) return null
    
    return (
      <View style={styles.footer}>
        <LoadingSpinner size="small" />
        <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 8 }]}>
          Loading more restaurants...
        </Text>
      </View>
    )
  }, [loadingMore, colors.text.secondary, typography.body.small])

  const renderEmpty = useCallback(() => {
    if (loading) return null
    
    return (
      <EmptyState
        title={emptyTitle}
        description={emptyDescription}
        icon="🍽️"
        style={styles.emptyState}
      />
    )
  }, [loading, emptyTitle, emptyDescription])

  const keyExtractor = useCallback((item: Restaurant) => item.id, [])

  const getItemLayout = useCallback((data: any, index: number) => {
    const itemHeight = variant === 'compact' ? 88 : variant === 'featured' ? 280 : 220
    return {
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }
  }, [variant])

  if (loading && restaurants.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner />
        <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 16 }]}>
          Finding restaurants near you...
        </Text>
      </View>
    )
  }

  return (
    <FlatList
      data={restaurants}
      renderItem={renderRestaurant}
      keyExtractor={keyExtractor}
      ListEmptyComponent={renderEmpty}
      ListFooterComponent={renderFooter}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
            colors={[colors.primary[500]]}
          />
        ) : undefined
      }
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      getItemLayout={getItemLayout}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={5}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={[
        styles.container,
        restaurants.length === 0 && styles.emptyContainer
      ]}
    />
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  emptyState: {
    paddingVertical: 32,
  },
})
