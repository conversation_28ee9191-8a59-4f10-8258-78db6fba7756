import React from 'react'
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native'
import { PlusIcon, MinusIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { MenuItem as MenuItemType } from './types'

interface MenuItemProps {
  item: MenuItemType
  onPress: (item: MenuItemType) => void
  onAddToCart?: (item: MenuItemType) => void
  onRemoveFromCart?: (item: MenuItemType) => void
  cartQuantity?: number
  showAddButton?: boolean
  variant?: 'default' | 'compact'
}

export function MenuItem({
  item,
  onPress,
  onAddToCart,
  onRemoveFromCart,
  cartQuantity = 0,
  showAddButton = true,
  variant = 'default'
}: MenuItemProps) {
  const { colors, typography } = useTheme()

  const handlePress = () => {
    onPress(item)
  }

  const handleAddToCart = (e: any) => {
    e.stopPropagation()
    onAddToCart?.(item)
  }

  const handleRemoveFromCart = (e: any) => {
    e.stopPropagation()
    onRemoveFromCart?.(item)
  }

  const renderBadges = () => {
    const badges = []

    if (item.isPopular) {
      badges.push(
        <View key="popular" style={[styles.badge, { backgroundColor: colors.primary[500] }]}>
          <Text style={[styles.badgeText, { color: colors.white }]}>Popular</Text>
        </View>
      )
    }

    if (item.isVegetarian) {
      badges.push(
        <View key="veg" style={[styles.badge, { backgroundColor: colors.success[500] }]}>
          <Text style={[styles.badgeText, { color: colors.white }]}>🌱</Text>
        </View>
      )
    }

    if (item.isSpicy) {
      badges.push(
        <View key="spicy" style={[styles.badge, { backgroundColor: colors.error[500] }]}>
          <Text style={[styles.badgeText, { color: colors.white }]}>🌶️</Text>
        </View>
      )
    }

    return badges.length > 0 ? (
      <View style={styles.badgeContainer}>
        {badges}
      </View>
    ) : null
  }

  const renderPrice = () => (
    <View style={styles.priceContainer}>
      {item.originalPrice && item.originalPrice > item.price && (
        <Text style={[
          typography.body.small,
          { color: colors.text.tertiary, textDecorationLine: 'line-through', marginRight: 4 }
        ]}>
          R{item.originalPrice.toFixed(2)}
        </Text>
      )}
      <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600' }]}>
        R{item.price.toFixed(2)}
      </Text>
    </View>
  )

  const renderCartControls = () => {
    if (!showAddButton) return null

    if (cartQuantity === 0) {
      return (
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary[500] }]}
          onPress={handleAddToCart}
          activeOpacity={0.7}
        >
          <PlusIcon size={20} color={colors.white} />
        </TouchableOpacity>
      )
    }

    return (
      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={[styles.quantityButton, { backgroundColor: colors.primary[100] }]}
          onPress={handleRemoveFromCart}
          activeOpacity={0.7}
        >
          <MinusIcon size={16} color={colors.primary[600]} />
        </TouchableOpacity>
        <Text style={[typography.body.medium, { color: colors.text.primary, marginHorizontal: 12 }]}>
          {cartQuantity}
        </Text>
        <TouchableOpacity
          style={[styles.quantityButton, { backgroundColor: colors.primary[500] }]}
          onPress={handleAddToCart}
          activeOpacity={0.7}
        >
          <PlusIcon size={16} color={colors.white} />
        </TouchableOpacity>
      </View>
    )
  }

  const renderCompactItem = () => (
    <TouchableOpacity
      style={[
        styles.compactContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
      disabled={!item.isAvailable}
    >
      <View style={styles.compactContent}>
        <Text style={[typography.body.medium, { color: colors.text.primary }]} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={[styles.compactFooter, { marginTop: 8 }]}>
          {renderPrice()}
          {renderCartControls()}
        </View>
      </View>
      {item.image && (
        <Image
          source={{ uri: item.image }}
          style={styles.compactImage}
          defaultSource={require('../../../assets/placeholder-food.png')}
        />
      )}
      {renderBadges()}
      {!item.isAvailable && (
        <View style={[styles.unavailableOverlay, { backgroundColor: colors.background.primary + '80' }]}>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Unavailable
          </Text>
        </View>
      )}
    </TouchableOpacity>
  )

  const renderDefaultItem = () => (
    <TouchableOpacity
      style={[
        styles.defaultContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
      disabled={!item.isAvailable}
    >
      {item.image && (
        <Image
          source={{ uri: item.image }}
          style={styles.defaultImage}
          defaultSource={require('../../../assets/placeholder-food.png')}
        />
      )}
      <View style={styles.defaultContent}>
        <Text style={[typography.body.large, { color: colors.text.primary }]} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 4 }]} numberOfLines={3}>
          {item.description}
        </Text>
        
        {item.customizations.length > 0 && (
          <Text style={[typography.body.small, { color: colors.primary[600], marginTop: 4 }]}>
            Customizable
          </Text>
        )}

        <View style={[styles.defaultFooter, { marginTop: 8 }]}>
          {renderPrice()}
          {renderCartControls()}
        </View>
      </View>
      {renderBadges()}
      {!item.isAvailable && (
        <View style={[styles.unavailableOverlay, { backgroundColor: colors.background.primary + '80' }]}>
          <Text style={[typography.body.medium, { color: colors.text.secondary }]}>
            Unavailable
          </Text>
        </View>
      )}
    </TouchableOpacity>
  )

  return variant === 'compact' ? renderCompactItem() : renderDefaultItem()
}

const styles = StyleSheet.create({
  defaultContainer: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 16,
  },
  compactContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 12,
    minHeight: 100,
  },
  defaultImage: {
    width: '100%',
    height: 160,
  },
  compactImage: {
    width: 80,
    height: 80,
    margin: 10,
    borderRadius: 8,
  },
  defaultContent: {
    padding: 16,
  },
  compactContent: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  defaultFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  compactFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    gap: 4,
  },
  badge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
