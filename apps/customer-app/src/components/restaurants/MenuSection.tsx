import React, { useState } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, LayoutAnimation } from 'react-native'
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { MenuItem } from './MenuItem'
import type { MenuCategory, MenuItem as MenuItemType } from './types'

interface MenuSectionProps {
  category: MenuCategory
  onItemPress: (item: MenuItemType) => void
  onAddToCart?: (item: MenuItemType) => void
  onRemoveFromCart?: (item: MenuItemType) => void
  getCartQuantity?: (itemId: string) => number
  showAddButtons?: boolean
  variant?: 'default' | 'compact'
  isCollapsible?: boolean
  defaultExpanded?: boolean
}

export function MenuSection({
  category,
  onItemPress,
  onAddToCart,
  onRemoveFromCart,
  getCartQuantity,
  showAddButtons = true,
  variant = 'default',
  isCollapsible = true,
  defaultExpanded = true
}: MenuSectionProps) {
  const { colors, typography } = useTheme()
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const toggleExpanded = () => {
    if (!isCollapsible) return
    
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
    setIsExpanded(!isExpanded)
  }

  const availableItems = category.items.filter(item => item.isAvailable)
  const unavailableItems = category.items.filter(item => !item.isAvailable)

  const renderHeader = () => (
    <TouchableOpacity
      style={[
        styles.header,
        { backgroundColor: colors.background.tertiary, borderColor: colors.border.primary }
      ]}
      onPress={toggleExpanded}
      activeOpacity={isCollapsible ? 0.7 : 1}
      disabled={!isCollapsible}
    >
      <View style={styles.headerContent}>
        <View>
          <Text style={[typography.heading.small, { color: colors.text.primary }]}>
            {category.name}
          </Text>
          {category.description && (
            <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
              {category.description}
            </Text>
          )}
        </View>
        <View style={styles.headerRight}>
          <Text style={[typography.body.small, { color: colors.text.tertiary, marginRight: 8 }]}>
            {category.items.length} items
          </Text>
          {isCollapsible && (
            isExpanded ? (
              <ChevronUpIcon size={20} color={colors.text.secondary} />
            ) : (
              <ChevronDownIcon size={20} color={colors.text.secondary} />
            )
          )}
        </View>
      </View>
    </TouchableOpacity>
  )

  const renderItems = () => {
    if (!isExpanded) return null

    return (
      <View style={styles.itemsContainer}>
        {/* Available Items */}
        {availableItems.map((item) => (
          <MenuItem
            key={item.id}
            item={item}
            onPress={onItemPress}
            onAddToCart={onAddToCart}
            onRemoveFromCart={onRemoveFromCart}
            cartQuantity={getCartQuantity?.(item.id) || 0}
            showAddButton={showAddButtons}
            variant={variant}
          />
        ))}

        {/* Unavailable Items */}
        {unavailableItems.length > 0 && (
          <>
            {availableItems.length > 0 && (
              <View style={[styles.divider, { backgroundColor: colors.border.primary }]} />
            )}
            <Text style={[
              typography.body.small,
              { color: colors.text.tertiary, marginBottom: 8, marginLeft: 4 }
            ]}>
              Currently Unavailable
            </Text>
            {unavailableItems.map((item) => (
              <MenuItem
                key={item.id}
                item={item}
                onPress={onItemPress}
                onAddToCart={onAddToCart}
                onRemoveFromCart={onRemoveFromCart}
                cartQuantity={getCartQuantity?.(item.id) || 0}
                showAddButton={false}
                variant={variant}
              />
            ))}
          </>
        )}
      </View>
    )
  }

  if (!category.isAvailable) {
    return (
      <View style={[styles.container, styles.unavailableSection]}>
        {renderHeader()}
        <View style={[styles.unavailableOverlay, { backgroundColor: colors.background.primary + '60' }]}>
          <Text style={[typography.body.medium, { color: colors.text.secondary }]}>
            This section is currently unavailable
          </Text>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {renderHeader()}
      {renderItems()}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  unavailableSection: {
    opacity: 0.6,
  },
  header: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemsContainer: {
    paddingHorizontal: 4,
  },
  divider: {
    height: 1,
    marginVertical: 16,
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
})
