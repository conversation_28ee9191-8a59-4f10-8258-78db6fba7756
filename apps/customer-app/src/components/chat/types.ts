export interface ChatMessage {
  id: string
  conversationId: string
  senderId: string
  senderName: string
  senderAvatar?: string
  content: string
  type: 'text' | 'image' | 'file' | 'location' | 'system'
  timestamp: string
  isRead: boolean
  attachments?: ChatAttachment[]
  replyTo?: string
  isEdited?: boolean
  editedAt?: string
}

export interface ChatAttachment {
  id: string
  type: 'image' | 'file' | 'location'
  url: string
  name: string
  size?: number
  mimeType?: string
  thumbnail?: string
}

export interface Conversation {
  id: string
  type: 'customer_vendor' | 'customer_runner' | 'support'
  participants: ChatUser[]
  lastMessage?: ChatMessage
  unreadCount: number
  isActive: boolean
  orderId?: string
  createdAt: string
  updatedAt: string
}

export interface ChatUser {
  id: string
  name: string
  avatar?: string
  role: 'customer' | 'vendor' | 'runner' | 'support'
  isOnline: boolean
  lastSeen?: string
}

export interface TypingUser {
  id: string
  name: string
  timestamp: string
}
