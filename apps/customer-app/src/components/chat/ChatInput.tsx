import React, { useState, useRef } from 'react'
import { View, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native'
import { SendIcon, PlusIcon, ImageIcon, PaperclipIcon, MapPinIcon, XIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'

interface ChatInputProps {
  onSendMessage: (content: string, type?: 'text' | 'image' | 'file' | 'location', attachments?: any[]) => void
  onTyping?: (isTyping: boolean) => void
  placeholder?: string
  disabled?: boolean
  replyTo?: {
    id: string
    content: string
    senderName: string
  }
  onCancelReply?: () => void
}

export function ChatInput({
  onSendMessage,
  onTyping,
  placeholder = 'Type a message...',
  disabled = false,
  replyTo,
  onCancelReply
}: ChatInputProps) {
  const { colors, typography } = useTheme()
  const [message, setMessage] = useState('')
  const [showAttachments, setShowAttachments] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const inputRef = useRef<TextInput>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim())
      setMessage('')
      handleTypingStop()
    }
  }

  const handleTextChange = (text: string) => {
    setMessage(text)
    
    if (onTyping) {
      if (!isTyping && text.length > 0) {
        setIsTyping(true)
        onTyping(true)
      }
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
      
      // Set new timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        handleTypingStop()
      }, 1000)
    }
  }

  const handleTypingStop = () => {
    if (isTyping) {
      setIsTyping(false)
      onTyping?.(false)
    }
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
  }

  const handleAttachmentPress = () => {
    setShowAttachments(!showAttachments)
  }

  const handleImagePicker = () => {
    // TODO: Implement image picker
    Alert.alert('Image Picker', 'Image picker functionality will be implemented')
    setShowAttachments(false)
  }

  const handleFilePicker = () => {
    // TODO: Implement file picker
    Alert.alert('File Picker', 'File picker functionality will be implemented')
    setShowAttachments(false)
  }

  const handleLocationShare = () => {
    // TODO: Implement location sharing
    Alert.alert('Location Share', 'Location sharing functionality will be implemented')
    setShowAttachments(false)
  }

  const renderReplyPreview = () => {
    if (!replyTo) return null

    return (
      <View style={[
        styles.replyPreview,
        { backgroundColor: colors.background.tertiary, borderColor: colors.primary[300] }
      ]}>
        <View style={styles.replyContent}>
          <Text style={[typography.body.small, { color: colors.primary[600], fontWeight: '600' }]}>
            Replying to {replyTo.senderName}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]} numberOfLines={1}>
            {replyTo.content}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.cancelReplyButton}
          onPress={onCancelReply}
          activeOpacity={0.7}
        >
          <XIcon size={16} color={colors.text.secondary} />
        </TouchableOpacity>
      </View>
    )
  }

  const renderAttachmentOptions = () => {
    if (!showAttachments) return null

    return (
      <View style={[
        styles.attachmentOptions,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}>
        <TouchableOpacity
          style={[styles.attachmentButton, { backgroundColor: colors.primary[100] }]}
          onPress={handleImagePicker}
          activeOpacity={0.7}
        >
          <ImageIcon size={20} color={colors.primary[600]} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.attachmentButton, { backgroundColor: colors.info[100] }]}
          onPress={handleFilePicker}
          activeOpacity={0.7}
        >
          <PaperclipIcon size={20} color={colors.info[600]} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.attachmentButton, { backgroundColor: colors.success[100] }]}
          onPress={handleLocationShare}
          activeOpacity={0.7}
        >
          <MapPinIcon size={20} color={colors.success[600]} />
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {renderReplyPreview()}
      {renderAttachmentOptions()}
      
      <View style={[
        styles.inputContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}>
        <TouchableOpacity
          style={styles.attachmentToggle}
          onPress={handleAttachmentPress}
          activeOpacity={0.7}
        >
          <PlusIcon 
            size={20} 
            color={showAttachments ? colors.primary[500] : colors.text.secondary}
            style={{ transform: [{ rotate: showAttachments ? '45deg' : '0deg' }] }}
          />
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={[
            styles.textInput,
            typography.body.medium,
            { color: colors.text.primary }
          ]}
          value={message}
          onChangeText={handleTextChange}
          placeholder={placeholder}
          placeholderTextColor={colors.text.tertiary}
          multiline
          maxLength={1000}
          editable={!disabled}
          onBlur={handleTypingStop}
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: message.trim() && !disabled ? colors.primary[500] : colors.background.tertiary
            }
          ]}
          onPress={handleSend}
          disabled={!message.trim() || disabled}
          activeOpacity={0.7}
        >
          <SendIcon 
            size={18} 
            color={message.trim() && !disabled ? colors.white : colors.text.tertiary} 
          />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
  },
  replyContent: {
    flex: 1,
  },
  cancelReplyButton: {
    padding: 4,
    marginLeft: 8,
  },
  attachmentOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  attachmentButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: 24,
    borderWidth: 1,
    paddingHorizontal: 4,
    paddingVertical: 4,
    minHeight: 48,
  },
  attachmentToggle: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    paddingHorizontal: 12,
    paddingVertical: 8,
    textAlignVertical: 'center',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
