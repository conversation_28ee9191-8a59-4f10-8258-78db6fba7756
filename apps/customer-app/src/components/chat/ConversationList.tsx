import React from 'react'
import { View, Text, TouchableOpacity, Image, FlatList, StyleSheet } from 'react-native'
import { MessageCircleIcon, PhoneIcon, TruckIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { EmptyState } from '../common/EmptyState'
import { LoadingSpinner } from '../common/LoadingSpinner'
import type { Conversation } from './types'

interface ConversationListProps {
  conversations: Conversation[]
  onConversationPress: (conversation: Conversation) => void
  onCallPress?: (conversation: Conversation) => void
  loading?: boolean
  refreshing?: boolean
  onRefresh?: () => void
  currentUserId: string
}

export function ConversationList({
  conversations,
  onConversationPress,
  onCallPress,
  loading = false,
  refreshing = false,
  onRefresh,
  currentUserId
}: ConversationListProps) {
  const { colors, typography } = useTheme()

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-ZA', {
        hour: '2-digit',
        minute: '2-digit'
      })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-ZA', {
        weekday: 'short'
      })
    } else {
      return date.toLocaleDateString('en-ZA', {
        day: 'numeric',
        month: 'short'
      })
    }
  }

  const getConversationTitle = (conversation: Conversation) => {
    // Get the other participant's name (not the current user)
    const otherParticipant = conversation.participants.find(p => p.id !== currentUserId)
    return otherParticipant?.name || 'Unknown User'
  }

  const getConversationAvatar = (conversation: Conversation) => {
    const otherParticipant = conversation.participants.find(p => p.id !== currentUserId)
    return otherParticipant?.avatar
  }

  const getConversationIcon = (type: string) => {
    switch (type) {
      case 'customer_vendor':
        return <MessageCircleIcon size={16} color={colors.primary[500]} />
      case 'customer_runner':
        return <TruckIcon size={16} color={colors.info[500]} />
      case 'support':
        return <PhoneIcon size={16} color={colors.success[500]} />
      default:
        return <MessageCircleIcon size={16} color={colors.text.secondary} />
    }
  }

  const getLastMessagePreview = (conversation: Conversation) => {
    if (!conversation.lastMessage) {
      return 'No messages yet'
    }

    const { lastMessage } = conversation
    const isOwn = lastMessage.senderId === currentUserId
    const prefix = isOwn ? 'You: ' : ''

    switch (lastMessage.type) {
      case 'image':
        return `${prefix}📷 Photo`
      case 'file':
        return `${prefix}📎 File`
      case 'location':
        return `${prefix}📍 Location`
      case 'system':
        return lastMessage.content
      default:
        return `${prefix}${lastMessage.content}`
    }
  }

  const renderConversationItem = ({ item }: { item: Conversation }) => {
    const title = getConversationTitle(item)
    const avatar = getConversationAvatar(item)
    const lastMessagePreview = getLastMessagePreview(item)
    const hasUnread = item.unreadCount > 0

    return (
      <TouchableOpacity
        style={[
          styles.conversationItem,
          {
            backgroundColor: hasUnread ? colors.primary[50] : colors.background.secondary,
            borderColor: colors.border.primary
          }
        ]}
        onPress={() => onConversationPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.avatarContainer}>
          {avatar ? (
            <Image source={{ uri: avatar }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary[200] }]}>
              <Text style={[typography.body.medium, { color: colors.primary[700] }]}>
                {title.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          
          <View style={styles.typeIcon}>
            {getConversationIcon(item.type)}
          </View>
        </View>

        <View style={styles.conversationContent}>
          <View style={styles.conversationHeader}>
            <Text style={[
              typography.body.large,
              {
                color: colors.text.primary,
                fontWeight: hasUnread ? '600' : '400',
                flex: 1
              }
            ]} numberOfLines={1}>
              {title}
            </Text>
            
            {item.lastMessage && (
              <Text style={[
                typography.body.small,
                { color: colors.text.tertiary }
              ]}>
                {formatTime(item.lastMessage.timestamp)}
              </Text>
            )}
          </View>

          <View style={styles.conversationFooter}>
            <Text style={[
              typography.body.medium,
              {
                color: hasUnread ? colors.text.primary : colors.text.secondary,
                fontWeight: hasUnread ? '500' : '400',
                flex: 1
              }
            ]} numberOfLines={1}>
              {lastMessagePreview}
            </Text>

            <View style={styles.conversationMeta}>
              {hasUnread && (
                <View style={[styles.unreadBadge, { backgroundColor: colors.primary[500] }]}>
                  <Text style={[
                    typography.body.small,
                    { color: colors.white, fontSize: 10 }
                  ]}>
                    {item.unreadCount > 99 ? '99+' : item.unreadCount}
                  </Text>
                </View>
              )}

              {onCallPress && item.type !== 'support' && (
                <TouchableOpacity
                  style={[styles.callButton, { backgroundColor: colors.success[100] }]}
                  onPress={() => onCallPress(item)}
                  activeOpacity={0.7}
                >
                  <PhoneIcon size={14} color={colors.success[600]} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    )
  }

  const renderEmpty = () => (
    <EmptyState
      title="No conversations"
      description="Start a conversation by placing an order or contacting support."
      icon="💬"
      style={styles.emptyState}
    />
  )

  if (loading && conversations.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner />
        <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 16 }]}>
          Loading conversations...
        </Text>
      </View>
    )
  }

  return (
    <FlatList
      data={conversations}
      renderItem={renderConversationItem}
      keyExtractor={(item) => item.id}
      ListEmptyComponent={renderEmpty}
      refreshing={refreshing}
      onRefresh={onRefresh}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={[
        styles.container,
        conversations.length === 0 && styles.emptyContainer
      ]}
    />
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  conversationContent: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  conversationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  callButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    paddingVertical: 32,
  },
})
