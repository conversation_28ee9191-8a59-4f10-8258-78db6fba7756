import React, { useState } from 'react'
import { View, Text, TouchableOpacity, Image, StyleSheet, Linking } from 'react-native'
import { MoreVerticalIcon, ReplyIcon, EditIcon, TrashIcon, CheckIcon, CheckCheckIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { ChatMessage } from './types'

interface ChatBubbleProps {
  message: ChatMessage
  isOwn: boolean
  showAvatar?: boolean
  showTimestamp?: boolean
  onReply?: (message: ChatMessage) => void
  onEdit?: (message: ChatMessage) => void
  onDelete?: (message: ChatMessage) => void
  onImagePress?: (imageUrl: string) => void
}

export function ChatBubble({
  message,
  isOwn,
  showAvatar = true,
  showTimestamp = true,
  onReply,
  onEdit,
  onDelete,
  onImagePress
}: ChatBubbleProps) {
  const { colors, typography } = useTheme()
  const [showActions, setShowActions] = useState(false)

  const handleLongPress = () => {
    setShowActions(!showActions)
  }

  const handleReply = () => {
    onReply?.(message)
    setShowActions(false)
  }

  const handleEdit = () => {
    onEdit?.(message)
    setShowActions(false)
  }

  const handleDelete = () => {
    onDelete?.(message)
    setShowActions(false)
  }

  const handleImagePress = (imageUrl: string) => {
    onImagePress?.(imageUrl)
  }

  const handleLocationPress = () => {
    if (message.type === 'location' && message.attachments?.[0]) {
      const location = message.attachments[0]
      const url = `https://maps.google.com/?q=${location.url}`
      Linking.openURL(url)
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <View style={styles.imageContainer}>
            {message.attachments?.map((attachment, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleImagePress(attachment.url)}
                activeOpacity={0.8}
              >
                <Image
                  source={{ uri: attachment.url }}
                  style={styles.messageImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
            {message.content && (
              <Text style={[
                typography.body.medium,
                { color: isOwn ? colors.white : colors.text.primary, marginTop: 8 }
              ]}>
                {message.content}
              </Text>
            )}
          </View>
        )

      case 'file':
        return (
          <View style={styles.fileContainer}>
            {message.attachments?.map((attachment, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.fileItem,
                  { backgroundColor: isOwn ? colors.primary[600] : colors.background.tertiary }
                ]}
                onPress={() => Linking.openURL(attachment.url)}
                activeOpacity={0.7}
              >
                <Text style={[
                  typography.body.small,
                  { color: isOwn ? colors.white : colors.text.primary, fontWeight: '600' }
                ]}>
                  📎 {attachment.name}
                </Text>
                {attachment.size && (
                  <Text style={[
                    typography.body.small,
                    { color: isOwn ? colors.primary[100] : colors.text.secondary }
                  ]}>
                    {(attachment.size / 1024 / 1024).toFixed(1)} MB
                  </Text>
                )}
              </TouchableOpacity>
            ))}
            {message.content && (
              <Text style={[
                typography.body.medium,
                { color: isOwn ? colors.white : colors.text.primary, marginTop: 8 }
              ]}>
                {message.content}
              </Text>
            )}
          </View>
        )

      case 'location':
        return (
          <TouchableOpacity
            style={[
              styles.locationContainer,
              { backgroundColor: isOwn ? colors.primary[600] : colors.background.tertiary }
            ]}
            onPress={handleLocationPress}
            activeOpacity={0.7}
          >
            <Text style={[
              typography.body.medium,
              { color: isOwn ? colors.white : colors.text.primary }
            ]}>
              📍 Location Shared
            </Text>
            {message.content && (
              <Text style={[
                typography.body.small,
                { color: isOwn ? colors.primary[100] : colors.text.secondary, marginTop: 4 }
              ]}>
                {message.content}
              </Text>
            )}
          </TouchableOpacity>
        )

      case 'system':
        return (
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, fontStyle: 'italic', textAlign: 'center' }
          ]}>
            {message.content}
          </Text>
        )

      default: // text
        return (
          <Text style={[
            typography.body.medium,
            { color: isOwn ? colors.white : colors.text.primary }
          ]}>
            {message.content}
          </Text>
        )
    }
  }

  const renderReadStatus = () => {
    if (!isOwn) return null

    return (
      <View style={styles.readStatus}>
        {message.isRead ? (
          <CheckCheckIcon size={14} color={colors.success[500]} />
        ) : (
          <CheckIcon size={14} color={colors.text.tertiary} />
        )}
      </View>
    )
  }

  const renderActions = () => {
    if (!showActions) return null

    return (
      <View style={[
        styles.actionsContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}>
        {onReply && (
          <TouchableOpacity style={styles.actionButton} onPress={handleReply}>
            <ReplyIcon size={16} color={colors.text.primary} />
            <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 4 }]}>
              Reply
            </Text>
          </TouchableOpacity>
        )}
        
        {isOwn && onEdit && message.type === 'text' && (
          <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
            <EditIcon size={16} color={colors.text.primary} />
            <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 4 }]}>
              Edit
            </Text>
          </TouchableOpacity>
        )}
        
        {isOwn && onDelete && (
          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <TrashIcon size={16} color={colors.error[500]} />
            <Text style={[typography.body.small, { color: colors.error[500], marginLeft: 4 }]}>
              Delete
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  if (message.type === 'system') {
    return (
      <View style={styles.systemMessage}>
        {renderMessageContent()}
      </View>
    )
  }

  return (
    <View style={[styles.container, isOwn && styles.ownMessage]}>
      {!isOwn && showAvatar && (
        <View style={styles.avatarContainer}>
          {message.senderAvatar ? (
            <Image source={{ uri: message.senderAvatar }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary[200] }]}>
              <Text style={[typography.body.small, { color: colors.primary[700] }]}>
                {message.senderName.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>
      )}

      <View style={styles.messageContainer}>
        {!isOwn && (
          <Text style={[typography.body.small, { color: colors.text.secondary, marginBottom: 4 }]}>
            {message.senderName}
          </Text>
        )}

        <TouchableOpacity
          style={[
            styles.bubble,
            {
              backgroundColor: isOwn ? colors.primary[500] : colors.background.secondary,
              borderColor: isOwn ? colors.primary[500] : colors.border.primary
            }
          ]}
          onLongPress={handleLongPress}
          activeOpacity={0.8}
        >
          {renderMessageContent()}
          
          <View style={[styles.messageFooter, { marginTop: 4 }]}>
            {showTimestamp && (
              <Text style={[
                typography.body.small,
                { color: isOwn ? colors.primary[100] : colors.text.tertiary }
              ]}>
                {formatTime(message.timestamp)}
                {message.isEdited && ' (edited)'}
              </Text>
            )}
            {renderReadStatus()}
          </View>
        </TouchableOpacity>

        {renderActions()}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  ownMessage: {
    justifyContent: 'flex-end',
  },
  avatarContainer: {
    marginRight: 8,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageContainer: {
    maxWidth: '80%',
  },
  bubble: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 12,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  readStatus: {
    marginLeft: 8,
  },
  imageContainer: {
    minWidth: 200,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  fileContainer: {
    minWidth: 200,
  },
  fileItem: {
    padding: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  locationContainer: {
    padding: 12,
    borderRadius: 8,
    minWidth: 150,
  },
  systemMessage: {
    alignItems: 'center',
    marginVertical: 8,
    paddingHorizontal: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginTop: 8,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    flex: 1,
    justifyContent: 'center',
  },
})
