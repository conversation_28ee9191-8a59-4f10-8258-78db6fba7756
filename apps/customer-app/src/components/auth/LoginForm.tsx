import React, { useState } from 'react'
import { Alert } from 'react-native'
import {
  <PERSON><PERSON>tack,
  HStack,
  Box,
  Text,
  Button,
  ButtonText,
  ButtonSpinner,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  FormControlErrorIcon,
  Link,
  LinkText,
  Divider,
  Pressable,
} from '@hvppyplug/ui-components-v2'
import { EyeIcon, EyeOffIcon, MailIcon, LockIcon, AlertCircleIcon } from 'lucide-react-native'
import { useAuthStore } from '../../stores/authStore'
import { BiometricAuth, QuickBiometricButton } from './BiometricAuth'

export interface LoginFormProps {
  onForgotPassword?: () => void
  onRegister?: () => void
  onSuccess?: () => void
  showBiometric?: boolean
  showSocialLogin?: boolean
}

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
}

export function LoginForm({
  onForgotPassword,
  onRegister,
  onSuccess,
  showBiometric = true,
  showSocialLogin = true,
}: LoginFormProps) {
  const { login, isLoading, error } = useAuthStore()
  const [formData, setFormData] = useState<FormData>({ email: '', password: '' })
  const [errors, setErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({})

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validateForm = () => {
    const newErrors: FormErrors = {}

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleInputBlur = (field: keyof FormData) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    validateForm()
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      await login(formData.email, formData.password)
      onSuccess?.()
    } catch (error: any) {
      Alert.alert('Login Failed', error.message || 'Please check your credentials and try again.')
    }
  }

  const handleBiometricSuccess = () => {
    // In a real app, you'd retrieve stored credentials and auto-login
    Alert.alert('Biometric Success', 'Biometric authentication successful!')
    onSuccess?.()
  }

  const handleBiometricError = (error: string) => {
    if (error !== 'fallback_requested') {
      Alert.alert('Authentication Failed', error)
    }
  }

  const handleSocialLogin = (provider: string) => {
    Alert.alert('Social Login', `${provider} login will be implemented with Appwrite OAuth`)
  }

  return (
    <VStack space="lg" className="w-full">
      {/* Biometric Authentication */}
      {showBiometric && (
        <HStack className="justify-between items-center mb-4">
          <Text className="text-typography-600 text-sm">Quick Login</Text>
          <QuickBiometricButton
            onSuccess={handleBiometricSuccess}
            onError={handleBiometricError}
          />
        </HStack>
      )}

      {/* Email Input */}
      <FormControl isInvalid={!!errors.email && touched.email}>
        <FormControlLabel>
          <FormControlLabelText>Email Address</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={MailIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            onBlur={() => handleInputBlur('email')}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
          />
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.email}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Password Input */}
      <FormControl isInvalid={!!errors.password && touched.password}>
        <FormControlLabel>
          <FormControlLabelText>Password</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={LockIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Enter your password"
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            onBlur={() => handleInputBlur('password')}
            secureTextEntry={!showPassword}
            autoComplete="current-password"
          />
          <InputSlot className="pr-3">
            <Pressable onPress={() => setShowPassword(!showPassword)}>
              <InputIcon
                as={showPassword ? EyeOffIcon : EyeIcon}
                className="text-typography-400"
              />
            </Pressable>
          </InputSlot>
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.password}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Forgot Password Link */}
      <HStack className="justify-end">
        <Link onPress={onForgotPassword}>
          <LinkText className="text-primary-600 text-sm font-medium">
            Forgot Password?
          </LinkText>
        </Link>
      </HStack>

      {/* Login Button */}
      <Button
        size="lg"
        action="primary"
        onPress={handleSubmit}
        isDisabled={isLoading}
        className="mt-2"
      >
        {isLoading && <ButtonSpinner className="mr-2" />}
        <ButtonText>{isLoading ? 'Signing In...' : 'Sign In'}</ButtonText>
      </Button>

      {/* Error Message */}
      {error && (
        <Box className="bg-error-50 border border-error-200 rounded-lg p-3">
          <HStack space="sm" className="items-center">
            <AlertCircleIcon size={16} color="#DC2626" />
            <Text className="text-error-700 text-sm flex-1">{error}</Text>
          </HStack>
        </Box>
      )}

      {/* Social Login */}
      {showSocialLogin && (
        <>
          <HStack className="items-center my-4">
            <Divider className="flex-1" />
            <Text className="mx-4 text-typography-400 text-sm">or continue with</Text>
            <Divider className="flex-1" />
          </HStack>

          <VStack space="sm">
            <Button
              variant="outline"
              size="lg"
              onPress={() => handleSocialLogin('Google')}
            >
              <ButtonText className="text-typography-700">Continue with Google</ButtonText>
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              onPress={() => handleSocialLogin('Apple')}
            >
              <ButtonText className="text-typography-700">Continue with Apple</ButtonText>
            </Button>
          </VStack>
        </>
      )}

      {/* Register Link */}
      <HStack className="justify-center mt-4">
        <Text className="text-typography-600 text-sm">Don't have an account? </Text>
        <Link onPress={onRegister}>
          <LinkText className="text-primary-600 text-sm font-medium">
            Sign Up
          </LinkText>
        </Link>
      </HStack>
    </VStack>
  )
}
