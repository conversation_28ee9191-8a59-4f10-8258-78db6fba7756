import React, { useState } from 'react'
import { Alert } from 'react-native'
import {
  <PERSON><PERSON>ta<PERSON>,
  HStack,
  Box,
  Text,
  Button,
  ButtonText,
  ButtonSpinner,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  FormControlErrorIcon,
  Link,
  LinkText,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
  Progress,
  ProgressFilledTrack,
  Pressable,
} from '@hvppyplug/ui-components-v2'
import { 
  EyeIcon, 
  EyeOffIcon, 
  MailIcon, 
  LockIcon, 
  UserIcon, 
  PhoneIcon,
  AlertCircleIcon,
  CheckIcon,
  XIcon 
} from 'lucide-react-native'
import { useAuthStore } from '../../stores/authStore'

export interface RegisterFormProps {
  onLogin?: () => void
  onSuccess?: () => void
  showTermsAndConditions?: boolean
}

interface FormData {
  name: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  acceptTerms: boolean
}

interface FormErrors {
  name?: string
  email?: string
  phone?: string
  password?: string
  confirmPassword?: string
  acceptTerms?: string
}

interface PasswordStrength {
  score: number
  feedback: string[]
  color: string
}

export function RegisterForm({
  onLogin,
  onSuccess,
  showTermsAndConditions = true,
}: RegisterFormProps) {
  const { register, isLoading, error } = useAuthStore()
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({})

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone: string) => {
    const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/
    return phoneRegex.test(phone.replace(/\s/g, ''))
  }

  const getPasswordStrength = (password: string): PasswordStrength => {
    let score = 0
    const feedback: string[] = []

    if (password.length >= 8) score += 1
    else feedback.push('At least 8 characters')

    if (/[a-z]/.test(password)) score += 1
    else feedback.push('One lowercase letter')

    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('One uppercase letter')

    if (/[0-9]/.test(password)) score += 1
    else feedback.push('One number')

    if (/[^A-Za-z0-9]/.test(password)) score += 1
    else feedback.push('One special character')

    const colors = ['#EF4444', '#F59E0B', '#F59E0B', '#10B981', '#10B981']
    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong']

    return {
      score,
      feedback,
      color: colors[score] || colors[0],
    }
  }

  const validateForm = () => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid South African phone number'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (getPasswordStrength(formData.password).score < 3) {
      newErrors.password = 'Password is too weak'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    if (showTermsAndConditions && !formData.acceptTerms) {
      newErrors.acceptTerms = 'You must accept the terms and conditions'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleInputBlur = (field: keyof FormData) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    validateForm()
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      await register(formData.email, formData.password, formData.name)
      onSuccess?.()
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message || 'Please try again.')
    }
  }

  const passwordStrength = getPasswordStrength(formData.password)

  return (
    <VStack space="lg" className="w-full">
      {/* Full Name Input */}
      <FormControl isInvalid={!!errors.name && touched.name}>
        <FormControlLabel>
          <FormControlLabelText>Full Name</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={UserIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Enter your full name"
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            onBlur={() => handleInputBlur('name')}
            autoComplete="name"
          />
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.name}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Email Input */}
      <FormControl isInvalid={!!errors.email && touched.email}>
        <FormControlLabel>
          <FormControlLabelText>Email Address</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={MailIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            onBlur={() => handleInputBlur('email')}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
          />
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.email}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Phone Input */}
      <FormControl isInvalid={!!errors.phone && touched.phone}>
        <FormControlLabel>
          <FormControlLabelText>Phone Number</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={PhoneIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="e.g., 0123456789"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            onBlur={() => handleInputBlur('phone')}
            keyboardType="phone-pad"
            autoComplete="tel"
          />
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.phone}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Password Input */}
      <FormControl isInvalid={!!errors.password && touched.password}>
        <FormControlLabel>
          <FormControlLabelText>Password</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={LockIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Create a strong password"
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            onBlur={() => handleInputBlur('password')}
            secureTextEntry={!showPassword}
            autoComplete="new-password"
          />
          <InputSlot className="pr-3">
            <Pressable onPress={() => setShowPassword(!showPassword)}>
              <InputIcon
                as={showPassword ? EyeOffIcon : EyeIcon}
                className="text-typography-400"
              />
            </Pressable>
          </InputSlot>
        </Input>
        
        {/* Password Strength Indicator */}
        {formData.password && (
          <VStack space="xs" className="mt-2">
            <Progress value={(passwordStrength.score / 5) * 100} size="sm">
              <ProgressFilledTrack style={{ backgroundColor: passwordStrength.color }} />
            </Progress>
            {passwordStrength.feedback.length > 0 && (
              <Text className="text-typography-500 text-xs">
                Missing: {passwordStrength.feedback.join(', ')}
              </Text>
            )}
          </VStack>
        )}
        
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.password}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Confirm Password Input */}
      <FormControl isInvalid={!!errors.confirmPassword && touched.confirmPassword}>
        <FormControlLabel>
          <FormControlLabelText>Confirm Password</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={LockIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Confirm your password"
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            onBlur={() => handleInputBlur('confirmPassword')}
            secureTextEntry={!showConfirmPassword}
            autoComplete="new-password"
          />
          <InputSlot className="pr-3">
            <Pressable onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
              <InputIcon
                as={showConfirmPassword ? EyeOffIcon : EyeIcon}
                className="text-typography-400"
              />
            </Pressable>
          </InputSlot>
          {formData.confirmPassword && (
            <InputSlot className="pr-10">
              <InputIcon
                as={formData.password === formData.confirmPassword ? CheckIcon : XIcon}
                className={formData.password === formData.confirmPassword ? "text-success-600" : "text-error-600"}
              />
            </InputSlot>
          )}
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.confirmPassword}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Terms and Conditions */}
      {showTermsAndConditions && (
        <FormControl isInvalid={!!errors.acceptTerms}>
          <HStack space="sm" className="items-start">
            <Checkbox
              value="terms"
              isChecked={formData.acceptTerms}
              onChange={(isChecked) => handleInputChange('acceptTerms', isChecked)}
              className="mt-1"
            >
              <CheckboxIndicator>
                <CheckboxIcon as={CheckIcon} />
              </CheckboxIndicator>
            </Checkbox>
            <VStack className="flex-1">
              <Text className="text-typography-700 text-sm">
                I agree to the{' '}
                <Link>
                  <LinkText className="text-primary-600">Terms of Service</LinkText>
                </Link>
                {' '}and{' '}
                <Link>
                  <LinkText className="text-primary-600">Privacy Policy</LinkText>
                </Link>
              </Text>
              {errors.acceptTerms && (
                <Text className="text-error-700 text-xs mt-1">{errors.acceptTerms}</Text>
              )}
            </VStack>
          </HStack>
        </FormControl>
      )}

      {/* Register Button */}
      <Button
        size="lg"
        action="primary"
        onPress={handleSubmit}
        isDisabled={isLoading}
        className="mt-2"
      >
        {isLoading && <ButtonSpinner className="mr-2" />}
        <ButtonText>{isLoading ? 'Creating Account...' : 'Create Account'}</ButtonText>
      </Button>

      {/* Error Message */}
      {error && (
        <Box className="bg-error-50 border border-error-200 rounded-lg p-3">
          <HStack space="sm" className="items-center">
            <AlertCircleIcon size={16} color="#DC2626" />
            <Text className="text-error-700 text-sm flex-1">{error}</Text>
          </HStack>
        </Box>
      )}

      {/* Login Link */}
      <HStack className="justify-center mt-4">
        <Text className="text-typography-600 text-sm">Already have an account? </Text>
        <Link onPress={onLogin}>
          <LinkText className="text-primary-600 text-sm font-medium">
            Sign In
          </LinkText>
        </Link>
      </HStack>
    </VStack>
  )
}
