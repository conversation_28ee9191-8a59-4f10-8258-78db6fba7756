import React, { useState } from 'react'
import { Alert } from 'react-native'
import {
  VStack,
  HStack,
  Box,
  Text,
  Button,
  ButtonText,
  ButtonSpinner,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  FormControlErrorIcon,
  Link,
  LinkText,
} from '@hvppyplug/ui-components-v2'
import { MailIcon, AlertCircleIcon, CheckCircleIcon, ArrowLeftIcon } from 'lucide-react-native'
import { useAuthStore } from '../../stores/authStore'

export interface ForgotPasswordFormProps {
  onBack?: () => void
  onSuccess?: () => void
}

interface FormData {
  email: string
}

interface FormErrors {
  email?: string
}

export function ForgotPasswordForm({
  onBack,
  onSuccess,
}: ForgotPasswordFormProps) {
  const { forgotPassword, isLoading, error } = useAuthStore()
  const [formData, setFormData] = useState<FormData>({ email: '' })
  const [errors, setErrors] = useState<FormErrors>({})
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({})
  const [isEmailSent, setIsEmailSent] = useState(false)

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validateForm = () => {
    const newErrors: FormErrors = {}

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleInputBlur = (field: keyof FormData) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    validateForm()
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      await forgotPassword(formData.email)
      setIsEmailSent(true)
      Alert.alert(
        'Reset Email Sent',
        'We\'ve sent a password reset link to your email address. Please check your inbox and follow the instructions.',
        [
          {
            text: 'OK',
            onPress: () => onSuccess?.(),
          },
        ]
      )
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send reset email. Please try again.')
    }
  }

  const handleResendEmail = async () => {
    try {
      await forgotPassword(formData.email)
      Alert.alert('Email Resent', 'We\'ve sent another password reset email to your address.')
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to resend email. Please try again.')
    }
  }

  if (isEmailSent) {
    return (
      <VStack space="lg" className="w-full items-center">
        {/* Success Icon */}
        <Box className="bg-success-50 rounded-full p-4 mb-4">
          <CheckCircleIcon size={48} color="#10B981" />
        </Box>

        {/* Success Message */}
        <VStack space="sm" className="items-center">
          <Text className="text-typography-900 text-xl font-semibold text-center">
            Check Your Email
          </Text>
          <Text className="text-typography-600 text-center max-w-sm">
            We've sent a password reset link to{' '}
            <Text className="font-medium">{formData.email}</Text>
          </Text>
        </VStack>

        {/* Instructions */}
        <Box className="bg-primary-50 border border-primary-200 rounded-lg p-4 w-full">
          <VStack space="sm">
            <Text className="text-primary-800 font-medium text-sm">
              What's next?
            </Text>
            <VStack space="xs">
              <Text className="text-primary-700 text-sm">
                • Check your email inbox (and spam folder)
              </Text>
              <Text className="text-primary-700 text-sm">
                • Click the reset link in the email
              </Text>
              <Text className="text-primary-700 text-sm">
                • Create a new password
              </Text>
            </VStack>
          </VStack>
        </Box>

        {/* Action Buttons */}
        <VStack space="sm" className="w-full">
          <Button
            variant="outline"
            size="lg"
            onPress={handleResendEmail}
            isDisabled={isLoading}
          >
            {isLoading && <ButtonSpinner className="mr-2" />}
            <ButtonText>Resend Email</ButtonText>
          </Button>

          <Button
            variant="outline"
            size="lg"
            onPress={onBack}
          >
            <ButtonText>Back to Sign In</ButtonText>
          </Button>
        </VStack>
      </VStack>
    )
  }

  return (
    <VStack space="lg" className="w-full">
      {/* Header */}
      <VStack space="sm" className="items-center">
        <Text className="text-typography-900 text-2xl font-bold text-center">
          Forgot Password?
        </Text>
        <Text className="text-typography-600 text-center max-w-sm">
          No worries! Enter your email address and we'll send you a link to reset your password.
        </Text>
      </VStack>

      {/* Email Input */}
      <FormControl isInvalid={!!errors.email && touched.email}>
        <FormControlLabel>
          <FormControlLabelText>Email Address</FormControlLabelText>
        </FormControlLabel>
        <Input variant="outline" size="lg">
          <InputSlot className="pl-3">
            <InputIcon as={MailIcon} className="text-typography-400" />
          </InputSlot>
          <InputField
            placeholder="Enter your email address"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            onBlur={() => handleInputBlur('email')}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            autoFocus
          />
        </Input>
        <FormControlError>
          <FormControlErrorIcon as={AlertCircleIcon} />
          <FormControlErrorText>{errors.email}</FormControlErrorText>
        </FormControlError>
      </FormControl>

      {/* Send Reset Link Button */}
      <Button
        size="lg"
        action="primary"
        onPress={handleSubmit}
        isDisabled={isLoading}
        className="mt-2"
      >
        {isLoading && <ButtonSpinner className="mr-2" />}
        <ButtonText>{isLoading ? 'Sending...' : 'Send Reset Link'}</ButtonText>
      </Button>

      {/* Error Message */}
      {error && (
        <Box className="bg-error-50 border border-error-200 rounded-lg p-3">
          <HStack space="sm" className="items-center">
            <AlertCircleIcon size={16} color="#DC2626" />
            <Text className="text-error-700 text-sm flex-1">{error}</Text>
          </HStack>
        </Box>
      )}

      {/* Back to Login Link */}
      <HStack className="justify-center mt-4">
        <Link onPress={onBack}>
          <HStack space="xs" className="items-center">
            <ArrowLeftIcon size={16} color="#F97316" />
            <LinkText className="text-primary-600 text-sm font-medium">
              Back to Sign In
            </LinkText>
          </HStack>
        </Link>
      </HStack>

      {/* Help Text */}
      <Box className="bg-background-50 rounded-lg p-4 mt-4">
        <VStack space="sm">
          <Text className="text-typography-700 font-medium text-sm">
            Need help?
          </Text>
          <Text className="text-typography-600 text-sm">
            If you don't receive the email within a few minutes, check your spam folder or{' '}
            <Link>
              <LinkText className="text-primary-600">contact support</LinkText>
            </Link>
            .
          </Text>
        </VStack>
      </Box>
    </VStack>
  )
}
