import React, { useState, useEffect } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native'
import * as LocalAuthentication from 'expo-local-authentication'
import { FingerprintIcon, ScanFaceIcon, ShieldCheckIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { EnhancedButton } from '../ui/EnhancedButton'

export interface BiometricAuthProps {
  onSuccess: () => void
  onError?: (error: string) => void
  title?: string
  subtitle?: string
  fallbackTitle?: string
  showFallback?: boolean
}

export function BiometricAuth({
  onSuccess,
  onError,
  title = 'Biometric Authentication',
  subtitle = 'Use your fingerprint or face to authenticate',
  fallbackTitle = 'Use Password Instead',
  showFallback = true,
}: BiometricAuthProps) {
  const { colors, typography, spacing } = useTheme()
  const [isSupported, setIsSupported] = useState(false)
  const [biometricType, setBiometricType] = useState<LocalAuthentication.AuthenticationType[]>([])
  const [isAuthenticating, setIsAuthenticating] = useState(false)

  useEffect(() => {
    checkBiometricSupport()
  }, [])

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync()
      const enrolled = await LocalAuthentication.isEnrolledAsync()
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync()

      setIsSupported(compatible && enrolled)
      setBiometricType(supportedTypes)
    } catch (error) {
      console.error('Error checking biometric support:', error)
      setIsSupported(false)
    }
  }

  const authenticate = async () => {
    if (!isSupported) {
      onError?.('Biometric authentication is not available on this device')
      return
    }

    setIsAuthenticating(true)

    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: title,
        subtitle: subtitle,
        cancelLabel: 'Cancel',
        fallbackLabel: showFallback ? fallbackTitle : undefined,
        disableDeviceFallback: !showFallback,
      })

      if (result.success) {
        onSuccess()
      } else if (result.error === 'user_cancel') {
        // User cancelled, don't show error
      } else if (result.error === 'user_fallback') {
        // User chose fallback, handle in parent component
        onError?.('fallback_requested')
      } else {
        onError?.(result.error || 'Authentication failed')
      }
    } catch (error: any) {
      onError?.(error.message || 'Authentication failed')
    } finally {
      setIsAuthenticating(false)
    }
  }

  const getBiometricIcon = () => {
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
      return <ScanFaceIcon size={48} color={colors.primary[500]} />
    }
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
      return <FingerprintIcon size={48} color={colors.primary[500]} />
    }
    return <ShieldCheckIcon size={48} color={colors.primary[500]} />
  }

  const getBiometricLabel = () => {
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
      return 'Face ID'
    }
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
      return 'Fingerprint'
    }
    return 'Biometric'
  }

  if (!isSupported) {
    return null
  }

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        {getBiometricIcon()}
      </View>

      <Text
        style={[
          typography.heading.h3,
          {
            color: colors.text.primary,
            textAlign: 'center',
            marginBottom: spacing[2],
          },
        ]}
      >
        {title}
      </Text>

      <Text
        style={[
          typography.body.medium,
          {
            color: colors.text.secondary,
            textAlign: 'center',
            marginBottom: spacing[6],
          },
        ]}
      >
        {subtitle}
      </Text>

      <EnhancedButton
        title={`Authenticate with ${getBiometricLabel()}`}
        onPress={authenticate}
        loading={isAuthenticating}
        loadingText="Authenticating..."
        leftIcon={getBiometricIcon()}
        style={styles.authButton}
      />
    </View>
  )
}

// Quick Biometric Button Component
export interface QuickBiometricButtonProps {
  onSuccess: () => void
  onError?: (error: string) => void
  size?: 'sm' | 'md' | 'lg'
  style?: any
}

export function QuickBiometricButton({
  onSuccess,
  onError,
  size = 'md',
  style,
}: QuickBiometricButtonProps) {
  const { colors } = useTheme()
  const [isSupported, setIsSupported] = useState(false)
  const [biometricType, setBiometricType] = useState<LocalAuthentication.AuthenticationType[]>([])

  useEffect(() => {
    checkSupport()
  }, [])

  const checkSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync()
      const enrolled = await LocalAuthentication.isEnrolledAsync()
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync()

      setIsSupported(compatible && enrolled)
      setBiometricType(supportedTypes)
    } catch (error) {
      setIsSupported(false)
    }
  }

  const authenticate = async () => {
    if (!isSupported) return

    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to continue',
        cancelLabel: 'Cancel',
        disableDeviceFallback: true,
      })

      if (result.success) {
        onSuccess()
      } else if (result.error && result.error !== 'user_cancel') {
        onError?.(result.error)
      }
    } catch (error: any) {
      onError?.(error.message || 'Authentication failed')
    }
  }

  const getIcon = () => {
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
      return <ScanFaceIcon size={getIconSize()} color={colors.primary[500]} />
    }
    if (biometricType.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
      return <FingerprintIcon size={getIconSize()} color={colors.primary[500]} />
    }
    return <ShieldCheckIcon size={getIconSize()} color={colors.primary[500]} />
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 20
      case 'lg': return 32
      default: return 24
    }
  }

  const getButtonSize = () => {
    switch (size) {
      case 'sm': return 40
      case 'lg': return 56
      default: return 48
    }
  }

  if (!isSupported) {
    return null
  }

  return (
    <TouchableOpacity
      onPress={authenticate}
      style={[
        styles.quickButton,
        {
          width: getButtonSize(),
          height: getButtonSize(),
          borderColor: colors.border.primary,
          backgroundColor: colors.background.secondary,
        },
        style,
      ]}
      activeOpacity={0.7}
    >
      {getIcon()}
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  iconContainer: {
    marginBottom: 24,
    padding: 16,
    borderRadius: 50,
    backgroundColor: 'rgba(249, 115, 22, 0.1)',
  },
  authButton: {
    minWidth: 200,
  },
  quickButton: {
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
