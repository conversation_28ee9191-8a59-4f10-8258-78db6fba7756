import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native'
import { useTheme } from '../../hooks/useTheme'

export interface OTPInputProps {
  length?: number
  value: string
  onChange: (otp: string) => void
  onComplete?: (otp: string) => void
  error?: boolean
  disabled?: boolean
  autoFocus?: boolean
  secureTextEntry?: boolean
  placeholder?: string
}

export function OTPInput({
  length = 6,
  value,
  onChange,
  onComplete,
  error = false,
  disabled = false,
  autoFocus = true,
  secureTextEntry = false,
  placeholder = '○',
}: OTPInputProps) {
  const { colors, typography, borderRadius, spacing } = useTheme()
  const [focusedIndex, setFocusedIndex] = useState(autoFocus ? 0 : -1)
  const inputRefs = useRef<(TextInput | null)[]>([])

  useEffect(() => {
    if (value.length === length && onComplete) {
      onComplete(value)
    }
  }, [value, length, onComplete])

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus()
    }
  }, [autoFocus])

  const handleChangeText = (text: string, index: number) => {
    if (disabled) return

    // Only allow numeric input
    const numericText = text.replace(/[^0-9]/g, '')
    
    if (numericText.length > 1) {
      // Handle paste operation
      const pastedText = numericText.slice(0, length)
      onChange(pastedText)
      
      // Focus the next empty input or the last input
      const nextIndex = Math.min(pastedText.length, length - 1)
      if (inputRefs.current[nextIndex]) {
        inputRefs.current[nextIndex]?.focus()
        setFocusedIndex(nextIndex)
      }
      return
    }

    // Handle single character input
    const newValue = value.split('')
    newValue[index] = numericText
    const newOTP = newValue.join('').slice(0, length)
    
    onChange(newOTP)

    // Move to next input if character was entered
    if (numericText && index < length - 1) {
      const nextIndex = index + 1
      if (inputRefs.current[nextIndex]) {
        inputRefs.current[nextIndex]?.focus()
        setFocusedIndex(nextIndex)
      }
    }
  }

  const handleKeyPress = (key: string, index: number) => {
    if (disabled) return

    if (key === 'Backspace') {
      if (!value[index] && index > 0) {
        // Move to previous input if current is empty
        const prevIndex = index - 1
        if (inputRefs.current[prevIndex]) {
          inputRefs.current[prevIndex]?.focus()
          setFocusedIndex(prevIndex)
        }
      }
    }
  }

  const handleFocus = (index: number) => {
    setFocusedIndex(index)
  }

  const handleBlur = () => {
    setFocusedIndex(-1)
  }

  const handleInputPress = (index: number) => {
    if (disabled) return
    
    if (inputRefs.current[index]) {
      inputRefs.current[index]?.focus()
      setFocusedIndex(index)
    }
  }

  const getInputStyle = (index: number) => {
    const isFocused = focusedIndex === index
    const hasValue = !!value[index]
    
    return [
      styles.input,
      {
        borderColor: error 
          ? colors.error[500] 
          : isFocused 
            ? colors.primary[500] 
            : hasValue 
              ? colors.success[500] 
              : colors.border.primary,
        backgroundColor: disabled 
          ? colors.background.tertiary 
          : colors.background.primary,
        borderRadius: borderRadius.md,
      },
    ]
  }

  const getTextStyle = () => {
    return [
      typography.heading.h2,
      {
        color: error 
          ? colors.error[500] 
          : disabled 
            ? colors.text.disabled 
            : colors.text.primary,
        textAlign: 'center' as const,
      },
    ]
  }

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {Array.from({ length }, (_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handleInputPress(index)}
            activeOpacity={0.7}
            disabled={disabled}
          >
            <TextInput
              ref={(ref) => (inputRefs.current[index] = ref)}
              style={getInputStyle(index)}
              value={value[index] || ''}
              onChangeText={(text) => handleChangeText(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              onFocus={() => handleFocus(index)}
              onBlur={handleBlur}
              keyboardType="numeric"
              maxLength={1}
              selectTextOnFocus
              secureTextEntry={secureTextEntry}
              placeholder={placeholder}
              placeholderTextColor={colors.text.tertiary}
              editable={!disabled}
              textContentType="oneTimeCode"
              autoComplete="sms-otp"
              textStyle={getTextStyle()}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}

// OTP Timer Component
export interface OTPTimerProps {
  duration: number // in seconds
  onExpire: () => void
  onResend: () => void
  isResending?: boolean
}

export function OTPTimer({
  duration,
  onExpire,
  onResend,
  isResending = false,
}: OTPTimerProps) {
  const { colors, typography } = useTheme()
  const [timeLeft, setTimeLeft] = useState(duration)
  const [isExpired, setIsExpired] = useState(false)

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (!isExpired) {
      setIsExpired(true)
      onExpire()
    }
  }, [timeLeft, isExpired, onExpire])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleResend = () => {
    if (!isExpired || isResending) return
    
    setTimeLeft(duration)
    setIsExpired(false)
    onResend()
  }

  return (
    <View style={styles.timerContainer}>
      {!isExpired ? (
        <Text
          style={[
            typography.body.medium,
            { color: colors.text.secondary, textAlign: 'center' },
          ]}
        >
          Resend code in {formatTime(timeLeft)}
        </Text>
      ) : (
        <TouchableOpacity
          onPress={handleResend}
          disabled={isResending}
          style={styles.resendButton}
        >
          <Text
            style={[
              typography.body.medium,
              {
                color: isResending ? colors.text.disabled : colors.primary[500],
                textAlign: 'center',
                fontWeight: '600',
              },
            ]}
          >
            {isResending ? 'Sending...' : 'Resend Code'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  )
}

const { width } = Dimensions.get('window')
const inputWidth = Math.min((width - 120) / 6, 50)

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  input: {
    width: inputWidth,
    height: inputWidth,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: 24,
    fontWeight: '600',
  },
  timerContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  resendButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
})
