import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native'
import { ShoppingCartIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { useCartStore } from '../../stores/cartStore'

interface CartButtonProps {
  onPress: () => void
  style?: any
}

export function CartButton({ onPress, style }: CartButtonProps) {
  const { colors, typography } = useTheme()
  const { cart, getTotalItems } = useCartStore()

  const totalItems = getTotalItems()

  if (!cart || totalItems === 0) {
    return null
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: colors.primary[500] },
        style
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <View style={styles.iconContainer}>
            <ShoppingCartIcon size={20} color={colors.white} />
            {totalItems > 0 && (
              <View style={[styles.badge, { backgroundColor: colors.white }]}>
                <Text style={[styles.badgeText, { color: colors.primary[500] }]}>
                  {totalItems > 99 ? '99+' : totalItems}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.textContainer}>
            <Text style={[typography.body.small, { color: colors.white }]}>
              {totalItems} {totalItems === 1 ? 'item' : 'items'}
            </Text>
            <Text style={[typography.body.medium, { color: colors.white, fontWeight: '600' }]}>
              R{cart.total.toFixed(2)}
            </Text>
          </View>
        </View>
        
        <View style={styles.rightSection}>
          <Text style={[typography.label.medium, { color: colors.white }]}>
            View Cart
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    position: 'relative',
    marginRight: 12,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 12,
  },
  textContainer: {
    flex: 1,
  },
  rightSection: {
    paddingLeft: 16,
  },
})
