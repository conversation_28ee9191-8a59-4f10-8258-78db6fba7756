import React from 'react'
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native'
import { PlusIcon, MinusIcon, TrashIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { CartItem as CartItemType } from './types'

interface CartItemProps {
  item: CartItemType
  onUpdateQuantity: (itemId: string, quantity: number) => void
  onRemove: (itemId: string) => void
  showImage?: boolean
  variant?: 'default' | 'compact'
}

export function CartItem({
  item,
  onUpdateQuantity,
  onRemove,
  showImage = true,
  variant = 'default'
}: CartItemProps) {
  const { colors, typography } = useTheme()

  const handleIncrease = () => {
    onUpdateQuantity(item.id, item.quantity + 1)
  }

  const handleDecrease = () => {
    if (item.quantity > 1) {
      onUpdateQuantity(item.id, item.quantity - 1)
    } else {
      onRemove(item.id)
    }
  }

  const handleRemove = () => {
    onRemove(item.id)
  }

  const renderCustomizations = () => {
    if (!item.customizations || item.customizations.length === 0) {
      return null
    }

    return (
      <View style={styles.customizations}>
        {item.customizations.map((customization, index) => (
          <Text
            key={index}
            style={[typography.body.small, { color: colors.text.tertiary }]}
          >
            {customization.name}: {customization.selectedOptions.map(opt => opt.name).join(', ')}
          </Text>
        ))}
      </View>
    )
  }

  const renderSpecialInstructions = () => {
    if (!item.specialInstructions) return null

    return (
      <Text style={[
        typography.body.small,
        { color: colors.text.tertiary, fontStyle: 'italic', marginTop: 4 }
      ]}>
        Note: {item.specialInstructions}
      </Text>
    )
  }

  const renderQuantityControls = () => (
    <View style={styles.quantityControls}>
      <TouchableOpacity
        style={[
          styles.quantityButton,
          { backgroundColor: colors.background.tertiary, borderColor: colors.border.primary }
        ]}
        onPress={handleDecrease}
        activeOpacity={0.7}
      >
        {item.quantity === 1 ? (
          <TrashIcon size={16} color={colors.error[500]} />
        ) : (
          <MinusIcon size={16} color={colors.text.primary} />
        )}
      </TouchableOpacity>
      
      <Text style={[
        typography.body.medium,
        { color: colors.text.primary, marginHorizontal: 16, minWidth: 20, textAlign: 'center' }
      ]}>
        {item.quantity}
      </Text>
      
      <TouchableOpacity
        style={[
          styles.quantityButton,
          { backgroundColor: colors.primary[500] }
        ]}
        onPress={handleIncrease}
        activeOpacity={0.7}
      >
        <PlusIcon size={16} color={colors.white} />
      </TouchableOpacity>
    </View>
  )

  const renderCompactItem = () => (
    <View style={[
      styles.compactContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      <View style={styles.compactContent}>
        <Text style={[typography.body.medium, { color: colors.text.primary }]} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={[typography.body.small, { color: colors.primary[600], marginTop: 2 }]}>
          R{item.totalPrice.toFixed(2)}
        </Text>
      </View>
      {renderQuantityControls()}
    </View>
  )

  const renderDefaultItem = () => (
    <View style={[
      styles.defaultContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      {showImage && item.image && (
        <Image
          source={{ uri: item.image }}
          style={styles.image}
          defaultSource={require('../../../assets/placeholder-food.png')}
        />
      )}
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[typography.body.large, { color: colors.text.primary }]} numberOfLines={2}>
            {item.name}
          </Text>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={handleRemove}
            activeOpacity={0.7}
          >
            <TrashIcon size={16} color={colors.error[500]} />
          </TouchableOpacity>
        </View>

        {item.description && (
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 4 }
          ]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        {renderCustomizations()}
        {renderSpecialInstructions()}

        <View style={[styles.footer, { marginTop: 12 }]}>
          <Text style={[typography.body.large, { color: colors.primary[600], fontWeight: '600' }]}>
            R{item.totalPrice.toFixed(2)}
          </Text>
          {renderQuantityControls()}
        </View>
      </View>
    </View>
  )

  return variant === 'compact' ? renderCompactItem() : renderDefaultItem()
}

const styles = StyleSheet.create({
  defaultContainer: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 12,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  image: {
    width: 80,
    height: 80,
    margin: 12,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    padding: 12,
  },
  compactContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  removeButton: {
    padding: 4,
    marginLeft: 8,
  },
  customizations: {
    marginTop: 6,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
})
