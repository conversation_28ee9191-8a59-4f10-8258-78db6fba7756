import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { useTheme } from '../../hooks/useTheme'
import type { Cart } from './types'

interface CartSummaryProps {
  cart: Cart
  showDeliveryFee?: boolean
  showTax?: boolean
  variant?: 'default' | 'compact'
}

export function CartSummary({
  cart,
  showDeliveryFee = true,
  showTax = true,
  variant = 'default'
}: CartSummaryProps) {
  const { colors, typography } = useTheme()

  const renderSummaryRow = (label: string, amount: number, isTotal = false, isSubtle = false) => (
    <View style={[
      styles.summaryRow,
      isTotal && styles.totalRow,
      isTotal && { borderTopWidth: 1, borderTopColor: colors.border.primary }
    ]}>
      <Text style={[
        isTotal ? typography.body.large : typography.body.medium,
        {
          color: isSubtle ? colors.text.secondary : colors.text.primary,
          fontWeight: isTotal ? '600' : '400'
        }
      ]}>
        {label}
      </Text>
      <Text style={[
        isTotal ? typography.body.large : typography.body.medium,
        {
          color: isTotal ? colors.primary[600] : colors.text.primary,
          fontWeight: isTotal ? '600' : '400'
        }
      ]}>
        R{amount.toFixed(2)}
      </Text>
    </View>
  )

  const renderCompactSummary = () => (
    <View style={[
      styles.compactContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      <View style={styles.compactContent}>
        <Text style={[typography.body.small, { color: colors.text.secondary }]}>
          {cart.itemCount} {cart.itemCount === 1 ? 'item' : 'items'}
        </Text>
        <Text style={[typography.body.large, { color: colors.primary[600], fontWeight: '600' }]}>
          R{cart.total.toFixed(2)}
        </Text>
      </View>
    </View>
  )

  const renderDefaultSummary = () => (
    <View style={[
      styles.defaultContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      <Text style={[
        typography.heading.small,
        { color: colors.text.primary, marginBottom: 16 }
      ]}>
        Order Summary
      </Text>

      {renderSummaryRow('Subtotal', cart.subtotal)}
      
      {showDeliveryFee && renderSummaryRow('Delivery Fee', cart.deliveryFee, false, true)}
      
      {showTax && cart.tax > 0 && renderSummaryRow('Tax', cart.tax, false, true)}
      
      {renderSummaryRow('Total', cart.total, true)}

      <View style={[styles.itemCount, { marginTop: 12 }]}>
        <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
          {cart.itemCount} {cart.itemCount === 1 ? 'item' : 'items'} from {cart.restaurantName}
        </Text>
      </View>
    </View>
  )

  return variant === 'compact' ? renderCompactSummary() : renderDefaultSummary()
}

const styles = StyleSheet.create({
  defaultContainer: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  compactContainer: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  compactContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  totalRow: {
    paddingTop: 12,
    marginTop: 8,
  },
  itemCount: {
    alignItems: 'center',
  },
})
