export interface CartItem {
  id: string
  menuItemId: string
  restaurantId: string
  name: string
  description: string
  image?: string
  price: number
  quantity: number
  customizations: SelectedCustomization[]
  specialInstructions?: string
  totalPrice: number
}

export interface SelectedCustomization {
  customizationId: string
  name: string
  selectedOptions: {
    id: string
    name: string
    price: number
  }[]
  totalPrice: number
}

export interface Cart {
  id: string
  restaurantId: string
  restaurantName: string
  items: CartItem[]
  subtotal: number
  deliveryFee: number
  tax: number
  total: number
  itemCount: number
  createdAt: string
  updatedAt: string
}

export interface CheckoutData {
  deliveryAddress: DeliveryAddress
  paymentMethod: PaymentMethod
  deliveryInstructions?: string
  contactPhone: string
  scheduledDelivery?: {
    date: string
    time: string
  }
}

export interface DeliveryAddress {
  id: string
  label: string
  street: string
  city: string
  province: string
  postalCode: string
  coordinates: {
    latitude: number
    longitude: number
  }
  instructions?: string
  isDefault: boolean
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'payfast' | 'mpesa' | 'cash'
  name: string
  details: any
  isDefault: boolean
}
