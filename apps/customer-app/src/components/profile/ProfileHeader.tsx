import React from 'react'
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native'
import { EditIcon, CameraIcon, StarIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { UserProfile } from './types'

interface ProfileHeaderProps {
  profile: UserProfile
  onEditPress: () => void
  onAvatarPress: () => void
  variant?: 'default' | 'compact'
  showStats?: boolean
  editable?: boolean
}

export function ProfileHeader({
  profile,
  onEditPress,
  onAvatarPress,
  variant = 'default',
  showStats = true,
  editable = true
}: ProfileHeaderProps) {
  const { colors, typography } = useTheme()

  const renderAvatar = () => (
    <TouchableOpacity
      style={[
        styles.avatarContainer,
        variant === 'compact' ? styles.avatarCompact : styles.avatarDefault
      ]}
      onPress={onAvatarPress}
      activeOpacity={0.8}
      disabled={!editable}
    >
      {profile.avatar ? (
        <Image
          source={{ uri: profile.avatar }}
          style={[
            styles.avatar,
            variant === 'compact' ? styles.avatarCompact : styles.avatarDefault
          ]}
        />
      ) : (
        <View style={[
          styles.avatarPlaceholder,
          {
            backgroundColor: colors.primary[200],
            ...(variant === 'compact' ? styles.avatarCompact : styles.avatarDefault)
          }
        ]}>
          <Text style={[
            variant === 'compact' ? typography.heading.small : typography.heading.medium,
            { color: colors.primary[700] }
          ]}>
            {profile.name.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}
      
      {editable && (
        <View style={[
          styles.cameraIcon,
          { backgroundColor: colors.primary[500] }
        ]}>
          <CameraIcon size={16} color={colors.white} />
        </View>
      )}
    </TouchableOpacity>
  )

  const renderStats = () => {
    if (!showStats) return null

    return (
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={[typography.heading.small, { color: colors.text.primary }]}>
            {profile.stats.totalOrders}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Orders
          </Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={[typography.heading.small, { color: colors.text.primary }]}>
            R{profile.stats.totalSpent.toFixed(0)}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Spent
          </Text>
        </View>
        
        <View style={styles.statItem}>
          <View style={styles.ratingContainer}>
            <StarIcon size={16} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={[
              typography.heading.small,
              { color: colors.text.primary, marginLeft: 4 }
            ]}>
              {profile.stats.averageRating.toFixed(1)}
            </Text>
          </View>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Rating
          </Text>
        </View>
      </View>
    )
  }

  const renderCompactHeader = () => (
    <View style={[
      styles.compactContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      {renderAvatar()}
      
      <View style={styles.compactInfo}>
        <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600' }]}>
          {profile.name}
        </Text>
        {profile.email && (
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            {profile.email}
          </Text>
        )}
        <Text style={[typography.body.small, { color: colors.text.secondary }]}>
          {profile.phone}
        </Text>
      </View>

      {editable && (
        <TouchableOpacity
          style={[styles.editButton, { backgroundColor: colors.primary[100] }]}
          onPress={onEditPress}
          activeOpacity={0.7}
        >
          <EditIcon size={16} color={colors.primary[600]} />
        </TouchableOpacity>
      )}
    </View>
  )

  const renderDefaultHeader = () => (
    <View style={[
      styles.defaultContainer,
      { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
    ]}>
      <View style={styles.headerTop}>
        {renderAvatar()}
        
        {editable && (
          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: colors.primary[100] }]}
            onPress={onEditPress}
            activeOpacity={0.7}
          >
            <EditIcon size={18} color={colors.primary[600]} />
            <Text style={[
              typography.body.small,
              { color: colors.primary[600], marginLeft: 4 }
            ]}>
              Edit
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={[styles.profileInfo, { marginTop: 16 }]}>
        <Text style={[typography.heading.medium, { color: colors.text.primary }]}>
          {profile.name}
        </Text>
        
        {profile.email && (
          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, marginTop: 4 }
          ]}>
            {profile.email}
          </Text>
        )}
        
        <Text style={[
          typography.body.medium,
          { color: colors.text.secondary, marginTop: 2 }
        ]}>
          {profile.phone}
        </Text>

        {profile.dateOfBirth && (
          <Text style={[
            typography.body.small,
            { color: colors.text.tertiary, marginTop: 8 }
          ]}>
            Member since {new Date(profile.createdAt).toLocaleDateString('en-ZA', {
              month: 'long',
              year: 'numeric'
            })}
          </Text>
        )}
      </View>

      {renderStats()}
    </View>
  )

  return variant === 'compact' ? renderCompactHeader() : renderDefaultHeader()
}

const styles = StyleSheet.create({
  defaultContainer: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    marginBottom: 16,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatarDefault: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarCompact: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  avatar: {
    // Dimensions set by variant styles above
  },
  avatarPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    // Dimensions set by variant styles above
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  compactInfo: {
    flex: 1,
    marginLeft: 16,
  },
  profileInfo: {
    alignItems: 'center',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statItem: {
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
})
