import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { MapPinIcon, HomeIcon, BriefcaseIcon, MoreVerticalIcon, StarIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { Address } from './types'

interface AddressCardProps {
  address: Address
  onPress: (address: Address) => void
  onEdit?: (address: Address) => void
  onDelete?: (address: Address) => void
  onSetDefault?: (address: Address) => void
  variant?: 'default' | 'compact' | 'selectable'
  selected?: boolean
  showActions?: boolean
}

export function AddressCard({
  address,
  onPress,
  onEdit,
  onDelete,
  onSetDefault,
  variant = 'default',
  selected = false,
  showActions = true
}: AddressCardProps) {
  const { colors, typography } = useTheme()

  const getAddressIcon = () => {
    switch (address.type) {
      case 'home':
        return <HomeIcon size={20} color={colors.primary[500]} />
      case 'work':
        return <BriefcaseIcon size={20} color={colors.info[500]} />
      default:
        return <MapPinIcon size={20} color={colors.text.secondary} />
    }
  }

  const formatAddress = () => {
    return `${address.street}, ${address.city}, ${address.province} ${address.postalCode}`
  }

  const handlePress = () => {
    onPress(address)
  }

  const handleEdit = (e: any) => {
    e.stopPropagation()
    onEdit?.(address)
  }

  const handleDelete = (e: any) => {
    e.stopPropagation()
    onDelete?.(address)
  }

  const handleSetDefault = (e: any) => {
    e.stopPropagation()
    onSetDefault?.(address)
  }

  const renderActions = () => {
    if (!showActions) return null

    return (
      <View style={styles.actions}>
        {!address.isDefault && onSetDefault && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.warning[100] }]}
            onPress={handleSetDefault}
            activeOpacity={0.7}
          >
            <StarIcon size={14} color={colors.warning[600]} />
            <Text style={[
              typography.body.small,
              { color: colors.warning[600], marginLeft: 4 }
            ]}>
              Set Default
            </Text>
          </TouchableOpacity>
        )}

        {onEdit && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary[100] }]}
            onPress={handleEdit}
            activeOpacity={0.7}
          >
            <Text style={[typography.body.small, { color: colors.primary[600] }]}>
              Edit
            </Text>
          </TouchableOpacity>
        )}

        {onDelete && !address.isDefault && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.error[100] }]}
            onPress={handleDelete}
            activeOpacity={0.7}
          >
            <Text style={[typography.body.small, { color: colors.error[600] }]}>
              Delete
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[
        styles.compactContainer,
        {
          backgroundColor: selected ? colors.primary[50] : colors.background.secondary,
          borderColor: selected ? colors.primary[300] : colors.border.primary
        }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.compactHeader}>
        <View style={styles.iconContainer}>
          {getAddressIcon()}
        </View>
        
        <View style={styles.compactContent}>
          <View style={styles.labelRow}>
            <Text style={[
              typography.body.medium,
              { color: colors.text.primary, fontWeight: '600', flex: 1 }
            ]}>
              {address.label}
            </Text>
            {address.isDefault && (
              <View style={[styles.defaultBadge, { backgroundColor: colors.success[100] }]}>
                <Text style={[typography.body.small, { color: colors.success[700] }]}>
                  Default
                </Text>
              </View>
            )}
          </View>
          
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 2 }
          ]} numberOfLines={1}>
            {formatAddress()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )

  const renderSelectableCard = () => (
    <TouchableOpacity
      style={[
        styles.selectableContainer,
        {
          backgroundColor: selected ? colors.primary[50] : colors.background.secondary,
          borderColor: selected ? colors.primary[500] : colors.border.primary,
          borderWidth: selected ? 2 : 1
        }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.selectableHeader}>
        <View style={styles.iconContainer}>
          {getAddressIcon()}
        </View>
        
        <View style={styles.selectableContent}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {address.label}
          </Text>
          
          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, marginTop: 4 }
          ]}>
            {formatAddress()}
          </Text>

          {address.instructions && (
            <Text style={[
              typography.body.small,
              { color: colors.text.tertiary, marginTop: 4, fontStyle: 'italic' }
            ]}>
              Note: {address.instructions}
            </Text>
          )}
        </View>

        {selected && (
          <View style={[styles.selectedIndicator, { backgroundColor: colors.primary[500] }]}>
            <Text style={[typography.body.small, { color: colors.white }]}>✓</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  )

  const renderDefaultCard = () => (
    <TouchableOpacity
      style={[
        styles.defaultContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.iconContainer}>
            {getAddressIcon()}
          </View>
          
          <View style={styles.headerContent}>
            <View style={styles.labelRow}>
              <Text style={[
                typography.body.large,
                { color: colors.text.primary, fontWeight: '600' }
              ]}>
                {address.label}
              </Text>
              {address.isDefault && (
                <View style={[styles.defaultBadge, { backgroundColor: colors.success[100] }]}>
                  <StarIcon size={12} color={colors.success[600]} />
                  <Text style={[
                    typography.body.small,
                    { color: colors.success[700], marginLeft: 4 }
                  ]}>
                    Default
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={[
              typography.body.medium,
              { color: colors.text.secondary, marginTop: 4 }
            ]}>
              {formatAddress()}
            </Text>

            {address.instructions && (
              <Text style={[
                typography.body.small,
                { color: colors.text.tertiary, marginTop: 4, fontStyle: 'italic' }
              ]}>
                Delivery instructions: {address.instructions}
              </Text>
            )}
          </View>
        </View>

        {showActions && (
          <TouchableOpacity style={styles.moreButton} activeOpacity={0.7}>
            <MoreVerticalIcon size={20} color={colors.text.secondary} />
          </TouchableOpacity>
        )}
      </View>

      {renderActions()}
    </TouchableOpacity>
  )

  switch (variant) {
    case 'compact':
      return renderCompactCard()
    case 'selectable':
      return renderSelectableCard()
    default:
      return renderDefaultCard()
  }
}

const styles = StyleSheet.create({
  defaultContainer: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
  },
  compactContainer: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 12,
    marginBottom: 8,
  },
  selectableContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flexDirection: 'row',
    flex: 1,
  },
  headerContent: {
    flex: 1,
    marginLeft: 12,
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactContent: {
    flex: 1,
    marginLeft: 12,
  },
  selectableHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  selectableContent: {
    flex: 1,
    marginLeft: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  defaultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  moreButton: {
    padding: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
})
