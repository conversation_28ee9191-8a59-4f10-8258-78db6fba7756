export interface UserProfile {
  id: string
  name: string
  email?: string
  phone: string
  avatar?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  preferences: UserPreferences
  addresses: Address[]
  paymentMethods: PaymentMethod[]
  stats: UserStats
  createdAt: string
  updatedAt: string
}

export interface UserPreferences {
  notifications: NotificationPreferences
  dietary: DietaryPreferences
  language: string
  currency: string
  theme: 'light' | 'dark' | 'system'
}

export interface NotificationPreferences {
  orderUpdates: boolean
  promotions: boolean
  newRestaurants: boolean
  recommendations: boolean
  chat: boolean
  email: boolean
  sms: boolean
  push: boolean
}

export interface DietaryPreferences {
  vegetarian: boolean
  vegan: boolean
  glutenFree: boolean
  halal: boolean
  kosher: boolean
  allergies: string[]
  dislikes: string[]
}

export interface Address {
  id: string
  label: string
  type: 'home' | 'work' | 'other'
  street: string
  city: string
  province: string
  postalCode: string
  country: string
  coordinates: {
    latitude: number
    longitude: number
  }
  instructions?: string
  isDefault: boolean
  isActive: boolean
  createdAt: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'payfast' | 'mpesa' | 'cash'
  name: string
  isDefault: boolean
  isActive: boolean
  details: CardDetails | PayfastDetails | MpesaDetails | CashDetails
  createdAt: string
}

export interface CardDetails {
  last4: string
  brand: string
  expiryMonth: number
  expiryYear: number
  holderName: string
}

export interface PayfastDetails {
  email: string
}

export interface MpesaDetails {
  phoneNumber: string
}

export interface CashDetails {
  // No additional details needed for cash
}

export interface UserStats {
  totalOrders: number
  totalSpent: number
  favoriteRestaurants: string[]
  averageRating: number
  reviewsCount: number
}
