import React, { useCallback } from 'react'
import { FlatList, View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native'
import { StarIcon, ClockIcon, MapPinIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { EmptyState } from '../common/EmptyState'
import { LoadingSpinner } from '../common/LoadingSpinner'
import type { SearchResult } from './types'

interface SearchResultsProps {
  results: SearchResult[]
  loading?: boolean
  onResultPress: (result: SearchResult) => void
  onLoadMore?: () => void
  hasMore?: boolean
  query?: string
}

export function SearchResults({
  results,
  loading = false,
  onResultPress,
  onLoadMore,
  hasMore = false,
  query = ''
}: SearchResultsProps) {
  const { colors, typography } = useTheme()

  const renderResultItem = useCallback(({ item }: { item: SearchResult }) => {
    return (
      <TouchableOpacity
        style={[
          styles.resultItem,
          { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
        ]}
        onPress={() => onResultPress(item)}
        activeOpacity={0.7}
      >
        {item.image && (
          <Image
            source={{ uri: item.image }}
            style={styles.resultImage}
            defaultSource={require('../../../assets/placeholder-restaurant.png')}
          />
        )}
        
        <View style={styles.resultContent}>
          <View style={styles.resultHeader}>
            <Text style={[
              typography.body.large,
              { color: colors.text.primary, fontWeight: '600', flex: 1 }
            ]} numberOfLines={1}>
              {highlightQuery(item.name, query)}
            </Text>
            
            <View style={styles.rating}>
              <StarIcon size={14} color={colors.warning[500]} fill={colors.warning[500]} />
              <Text style={[
                typography.body.small,
                { color: colors.text.secondary, marginLeft: 2 }
              ]}>
                {item.rating.toFixed(1)}
              </Text>
            </View>
          </View>

          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, marginTop: 2 }
          ]} numberOfLines={2}>
            {highlightQuery(item.description, query)}
          </Text>

          {item.restaurant && (
            <Text style={[
              typography.body.small,
              { color: colors.primary[600], marginTop: 4 }
            ]}>
              {item.restaurant.cuisine.join(', ')}
            </Text>
          )}

          <View style={[styles.resultMeta, { marginTop: 8 }]}>
            <View style={styles.metaItem}>
              <ClockIcon size={12} color={colors.text.tertiary} />
              <Text style={[
                typography.body.small,
                { color: colors.text.tertiary, marginLeft: 4 }
              ]}>
                {item.deliveryTime}
              </Text>
            </View>

            <View style={styles.metaItem}>
              <MapPinIcon size={12} color={colors.text.tertiary} />
              <Text style={[
                typography.body.small,
                { color: colors.text.tertiary, marginLeft: 4 }
              ]}>
                {item.distance.toFixed(1)} km
              </Text>
            </View>

            {item.price && (
              <Text style={[
                typography.body.small,
                { color: colors.primary[600], fontWeight: '600' }
              ]}>
                R{item.price.toFixed(2)}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    )
  }, [colors, typography, query, onResultPress])

  const renderFooter = useCallback(() => {
    if (!loading || results.length === 0) return null
    
    return (
      <View style={styles.footer}>
        <LoadingSpinner size="small" />
        <Text style={[
          typography.body.small,
          { color: colors.text.secondary, marginLeft: 8 }
        ]}>
          Loading more results...
        </Text>
      </View>
    )
  }, [loading, results.length, colors.text.secondary, typography.body.small])

  const renderEmpty = useCallback(() => {
    if (loading) return null
    
    return (
      <EmptyState
        title={query ? `No results for "${query}"` : 'No results found'}
        description={query 
          ? 'Try adjusting your search terms or filters.'
          : 'Start typing to search for restaurants and food.'
        }
        icon="🔍"
        style={styles.emptyState}
      />
    )
  }, [loading, query])

  const keyExtractor = useCallback((item: SearchResult) => `${item.type}_${item.id}`, [])

  const handleEndReached = useCallback(() => {
    if (hasMore && !loading && onLoadMore) {
      onLoadMore()
    }
  }, [hasMore, loading, onLoadMore])

  if (loading && results.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner />
        <Text style={[
          typography.body.medium,
          { color: colors.text.secondary, marginTop: 16 }
        ]}>
          Searching...
        </Text>
      </View>
    )
  }

  return (
    <FlatList
      data={results}
      renderItem={renderResultItem}
      keyExtractor={keyExtractor}
      ListEmptyComponent={renderEmpty}
      ListFooterComponent={renderFooter}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.1}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={[
        styles.container,
        results.length === 0 && styles.emptyContainer
      ]}
    />
  )
}

// Helper function to highlight search query in text
function highlightQuery(text: string, query: string): string {
  if (!query.trim()) return text
  
  // For now, just return the original text
  // In a real implementation, you might use a library like react-native-highlight-words
  return text
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  resultItem: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 12,
  },
  resultImage: {
    width: 80,
    height: 80,
    margin: 12,
    borderRadius: 8,
  },
  resultContent: {
    flex: 1,
    padding: 12,
    paddingLeft: 0,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  resultMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  emptyState: {
    paddingVertical: 32,
  },
})
