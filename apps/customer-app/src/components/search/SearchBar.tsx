import React, { useState, useRef, useEffect } from 'react'
import { View, TextInput, TouchableOpacity, StyleSheet, Animated } from 'react-native'
import { SearchIcon, XIcon, FilterIcon, MicIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'

interface SearchBarProps {
  value: string
  onChangeText: (text: string) => void
  onSearch: (query: string) => void
  onClear: () => void
  onFilterPress?: () => void
  onVoiceSearch?: () => void
  placeholder?: string
  showFilter?: boolean
  showVoice?: boolean
  autoFocus?: boolean
  disabled?: boolean
  filterActive?: boolean
}

export function SearchBar({
  value,
  onChangeText,
  onSearch,
  onClear,
  onFilterPress,
  onVoiceSearch,
  placeholder = 'Search restaurants, food...',
  showFilter = true,
  showVoice = false,
  autoFocus = false,
  disabled = false,
  filterActive = false
}: SearchBarProps) {
  const { colors, typography } = useTheme()
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<TextInput>(null)
  const animatedValue = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start()
  }, [isFocused])

  const handleFocus = () => {
    setIsFocused(true)
  }

  const handleBlur = () => {
    setIsFocused(false)
  }

  const handleSubmit = () => {
    if (value.trim()) {
      onSearch(value.trim())
      inputRef.current?.blur()
    }
  }

  const handleClear = () => {
    onClear()
    inputRef.current?.focus()
  }

  const borderColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.border.primary, colors.primary[300]],
  })

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.background.secondary, colors.background.primary],
  })

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.searchContainer,
          {
            borderColor,
            backgroundColor,
          }
        ]}
      >
        <View style={styles.searchIcon}>
          <SearchIcon size={20} color={colors.text.secondary} />
        </View>

        <TextInput
          ref={inputRef}
          style={[
            styles.textInput,
            typography.body.medium,
            { color: colors.text.primary }
          ]}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSubmit}
          placeholder={placeholder}
          placeholderTextColor={colors.text.tertiary}
          autoFocus={autoFocus}
          editable={!disabled}
          returnKeyType="search"
          autoCapitalize="none"
          autoCorrect={false}
        />

        {value.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
            activeOpacity={0.7}
          >
            <XIcon size={18} color={colors.text.secondary} />
          </TouchableOpacity>
        )}

        {showVoice && value.length === 0 && onVoiceSearch && (
          <TouchableOpacity
            style={styles.voiceButton}
            onPress={onVoiceSearch}
            activeOpacity={0.7}
          >
            <MicIcon size={18} color={colors.text.secondary} />
          </TouchableOpacity>
        )}
      </Animated.View>

      {showFilter && onFilterPress && (
        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: filterActive ? colors.primary[500] : colors.background.secondary,
              borderColor: filterActive ? colors.primary[500] : colors.border.primary
            }
          ]}
          onPress={onFilterPress}
          activeOpacity={0.7}
        >
          <FilterIcon 
            size={20} 
            color={filterActive ? colors.white : colors.text.secondary} 
          />
        </TouchableOpacity>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 24,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  voiceButton: {
    padding: 4,
    marginLeft: 8,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
