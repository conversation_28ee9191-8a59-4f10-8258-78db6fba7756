export interface SearchQuery {
  query: string
  filters: SearchFilters
  location?: {
    latitude: number
    longitude: number
    radius: number
  }
  sortBy: 'relevance' | 'distance' | 'rating' | 'delivery_time' | 'price'
  sortOrder: 'asc' | 'desc'
}

export interface SearchFilters {
  categories: string[]
  cuisines: string[]
  priceRange: [number, number]
  rating: number
  deliveryTime: number
  isOpen: boolean
  isFeatured: boolean
  hasDeals: boolean
  dietary: {
    vegetarian: boolean
    vegan: boolean
    glutenFree: boolean
    halal: boolean
  }
}

export interface SearchResult {
  type: 'restaurant' | 'menu_item'
  id: string
  name: string
  description: string
  image?: string
  rating: number
  distance: number
  deliveryTime: string
  price?: number
  restaurant?: {
    id: string
    name: string
    cuisine: string[]
  }
  relevanceScore: number
}

export interface SearchSuggestion {
  id: string
  text: string
  type: 'restaurant' | 'cuisine' | 'dish' | 'location'
  count?: number
}

export interface RecentSearch {
  id: string
  query: string
  filters: SearchFilters
  timestamp: string
}

export interface PopularSearch {
  id: string
  query: string
  count: number
  trending: boolean
}
