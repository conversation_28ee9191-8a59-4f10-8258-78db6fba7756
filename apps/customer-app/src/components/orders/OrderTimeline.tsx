import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { CheckCircleIcon, ClockIcon, CircleIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { OrderStatus, TrackingEvent } from './types'

interface OrderTimelineProps {
  currentStatus: OrderStatus
  timeline?: TrackingEvent[]
  variant?: 'default' | 'compact'
}

export function OrderTimeline({
  currentStatus,
  timeline = [],
  variant = 'default'
}: OrderTimelineProps) {
  const { colors, typography } = useTheme()

  const defaultTimeline: { status: OrderStatus; title: string; description: string }[] = [
    {
      status: 'pending',
      title: 'Order Placed',
      description: 'Your order has been received'
    },
    {
      status: 'confirmed',
      title: 'Order Confirmed',
      description: 'Restaurant has confirmed your order'
    },
    {
      status: 'preparing',
      title: 'Preparing',
      description: 'Your food is being prepared'
    },
    {
      status: 'ready_for_pickup',
      title: 'Ready for Pickup',
      description: 'Your order is ready for delivery'
    },
    {
      status: 'picked_up',
      title: 'Picked Up',
      description: 'Driver has picked up your order'
    },
    {
      status: 'on_the_way',
      title: 'On the Way',
      description: 'Your order is on its way to you'
    },
    {
      status: 'delivered',
      title: 'Delivered',
      description: 'Your order has been delivered'
    }
  ]

  const getStatusIndex = (status: OrderStatus) => {
    return defaultTimeline.findIndex(item => item.status === status)
  }

  const currentStatusIndex = getStatusIndex(currentStatus)
  const isCancelled = currentStatus === 'cancelled'
  const isRefunded = currentStatus === 'refunded'

  const renderTimelineItem = (item: any, index: number, isFromTimeline = false) => {
    const isCompleted = !isCancelled && !isRefunded && (
      isFromTimeline || index <= currentStatusIndex
    )
    const isCurrent = !isFromTimeline && index === currentStatusIndex
    const isLast = index === (isFromTimeline ? timeline.length - 1 : defaultTimeline.length - 1)

    const iconColor = isCompleted ? colors.success[500] : 
                     isCurrent ? colors.primary[500] : 
                     colors.text.tertiary

    const textColor = isCompleted || isCurrent ? colors.text.primary : colors.text.tertiary

    return (
      <View key={isFromTimeline ? item.id : item.status} style={styles.timelineItem}>
        <View style={styles.iconContainer}>
          {isCompleted ? (
            <CheckCircleIcon size={20} color={iconColor} fill={iconColor} />
          ) : isCurrent ? (
            <ClockIcon size={20} color={iconColor} />
          ) : (
            <CircleIcon size={20} color={iconColor} />
          )}
          {!isLast && (
            <View style={[
              styles.connector,
              { backgroundColor: isCompleted ? colors.success[300] : colors.border.primary }
            ]} />
          )}
        </View>
        
        <View style={styles.content}>
          <Text style={[
            typography.body.medium,
            { color: textColor, fontWeight: isCompleted || isCurrent ? '600' : '400' }
          ]}>
            {item.title}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 2 }
          ]}>
            {item.description}
          </Text>
          {isFromTimeline && item.timestamp && (
            <Text style={[
              typography.body.small,
              { color: colors.text.tertiary, marginTop: 4 }
            ]}>
              {new Date(item.timestamp).toLocaleString('en-ZA', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Text>
          )}
        </View>
      </View>
    )
  }

  const renderCompactTimeline = () => {
    const currentItem = defaultTimeline[currentStatusIndex]
    if (!currentItem) return null

    return (
      <View style={[
        styles.compactContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}>
        <View style={styles.compactContent}>
          <CheckCircleIcon size={16} color={colors.success[500]} />
          <Text style={[
            typography.body.small,
            { color: colors.text.primary, marginLeft: 8 }
          ]}>
            {currentItem.title}
          </Text>
        </View>
        <Text style={[
          typography.body.small,
          { color: colors.text.secondary, marginTop: 4 }
        ]}>
          {currentItem.description}
        </Text>
      </View>
    )
  }

  if (variant === 'compact') {
    return renderCompactTimeline()
  }

  // Handle cancelled or refunded orders
  if (isCancelled || isRefunded) {
    const statusItem = {
      title: isCancelled ? 'Order Cancelled' : 'Order Refunded',
      description: isCancelled ? 'Your order has been cancelled' : 'Your order has been refunded',
      timestamp: new Date().toISOString()
    }

    return (
      <View style={styles.container}>
        <Text style={[
          typography.heading.small,
          { color: colors.text.primary, marginBottom: 16 }
        ]}>
          Order Status
        </Text>
        {renderTimelineItem(statusItem, 0, true)}
      </View>
    )
  }

  // Use custom timeline if provided, otherwise use default
  const timelineToRender = timeline.length > 0 ? timeline : defaultTimeline

  return (
    <View style={styles.container}>
      <Text style={[
        typography.heading.small,
        { color: colors.text.primary, marginBottom: 16 }
      ]}>
        Order Progress
      </Text>
      {timelineToRender.map((item, index) => 
        renderTimelineItem(item, index, timeline.length > 0)
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  compactContainer: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 12,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  iconContainer: {
    alignItems: 'center',
    marginRight: 12,
    position: 'relative',
  },
  connector: {
    position: 'absolute',
    top: 24,
    width: 2,
    height: 32,
  },
  content: {
    flex: 1,
    paddingTop: 2,
  },
})
