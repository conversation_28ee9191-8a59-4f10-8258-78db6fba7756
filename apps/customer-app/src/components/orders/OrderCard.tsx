import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { ClockIcon, MapPinIcon, PhoneIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import { OrderStatusBadge } from './OrderStatusBadge'
import type { Order } from './types'

interface OrderCardProps {
  order: Order
  onPress: (order: Order) => void
  onTrack?: (order: Order) => void
  onContact?: (order: Order) => void
  variant?: 'default' | 'compact'
  showActions?: boolean
}

export function OrderCard({
  order,
  onPress,
  onTrack,
  onContact,
  variant = 'default',
  showActions = true
}: OrderCardProps) {
  const { colors, typography } = useTheme()

  const handlePress = () => {
    onPress(order)
  }

  const handleTrack = (e: any) => {
    e.stopPropagation()
    onTrack?.(order)
  }

  const handleContact = (e: any) => {
    e.stopPropagation()
    onContact?.(order)
  }

  const isActiveOrder = !['delivered', 'cancelled', 'refunded'].includes(order.status)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[
        styles.compactContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.compactHeader}>
        <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
          {order.vendor.name}
        </Text>
        <OrderStatusBadge status={order.status} size="small" />
      </View>
      
      <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
        Order #{order.orderNumber}
      </Text>
      
      <View style={[styles.compactFooter, { marginTop: 8 }]}>
        <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
          {formatDate(order.createdAt)}
        </Text>
        <Text style={[typography.body.medium, { color: colors.primary[600], fontWeight: '600' }]}>
          R{order.total.toFixed(2)}
        </Text>
      </View>
    </TouchableOpacity>
  )

  const renderDefaultCard = () => (
    <TouchableOpacity
      style={[
        styles.defaultContainer,
        { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600' }]}>
            {order.vendor.name}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
            Order #{order.orderNumber}
          </Text>
        </View>
        <OrderStatusBadge status={order.status} />
      </View>

      <View style={[styles.orderInfo, { marginTop: 12 }]}>
        <Text style={[typography.body.small, { color: colors.text.secondary }]}>
          {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
        </Text>
        <Text style={[typography.body.small, { color: colors.text.secondary }]}>
          {formatDate(order.createdAt)}
        </Text>
      </View>

      {isActiveOrder && order.estimatedDeliveryTime && (
        <View style={[styles.deliveryInfo, { marginTop: 8 }]}>
          <ClockIcon size={14} color={colors.primary[500]} />
          <Text style={[typography.body.small, { color: colors.primary[600], marginLeft: 4 }]}>
            Estimated delivery: {new Date(order.estimatedDeliveryTime).toLocaleTimeString('en-ZA', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
        </View>
      )}

      <View style={[styles.footer, { marginTop: 12 }]}>
        <Text style={[typography.body.large, { color: colors.primary[600], fontWeight: '600' }]}>
          R{order.total.toFixed(2)}
        </Text>
        
        {showActions && isActiveOrder && (
          <View style={styles.actions}>
            {onTrack && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.primary[100] }]}
                onPress={handleTrack}
                activeOpacity={0.7}
              >
                <MapPinIcon size={16} color={colors.primary[600]} />
                <Text style={[typography.body.small, { color: colors.primary[600], marginLeft: 4 }]}>
                  Track
                </Text>
              </TouchableOpacity>
            )}
            
            {onContact && order.vendor.phone && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.background.tertiary, marginLeft: 8 }]}
                onPress={handleContact}
                activeOpacity={0.7}
              >
                <PhoneIcon size={16} color={colors.text.primary} />
                <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 4 }]}>
                  Call
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  )

  return variant === 'compact' ? renderCompactCard() : renderDefaultCard()
}

const styles = StyleSheet.create({
  defaultContainer: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
  },
  compactContainer: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 12,
    marginBottom: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
    marginRight: 12,
  },
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  compactFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
})
