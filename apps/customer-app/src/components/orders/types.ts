export interface Order {
  id: string
  orderNumber: string
  customerId: string
  vendorId: string
  vendor: {
    id: string
    name: string
    image?: string
    phone?: string
  }
  status: OrderStatus
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  tax: number
  total: number
  currency: string
  paymentStatus: PaymentStatus
  paymentMethod?: PaymentMethod
  deliveryAddress: DeliveryAddress
  estimatedDeliveryTime: string
  actualDeliveryTime?: string
  specialInstructions?: string
  tracking?: OrderTracking
  review?: OrderReview
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  menuItemId: string
  name: string
  description?: string
  image?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  customizations?: ItemCustomization[]
  specialInstructions?: string
}

export interface ItemCustomization {
  id: string
  name: string
  options: string[]
  additionalCost: number
}

export interface DeliveryAddress {
  id: string
  label: string
  street: string
  city: string
  province: string
  postalCode: string
  coordinates: {
    latitude: number
    longitude: number
  }
  instructions?: string
}

export interface OrderTracking {
  currentStatus: OrderStatus
  estimatedDeliveryTime: string
  runnerLocation?: {
    latitude: number
    longitude: number
  }
  runner?: {
    id: string
    name: string
    phone: string
    avatar?: string
  }
  timeline: TrackingEvent[]
}

export interface TrackingEvent {
  id: string
  status: OrderStatus
  title: string
  description: string
  timestamp: string
  location?: {
    latitude: number
    longitude: number
  }
}

export interface OrderReview {
  id: string
  rating: number
  comment?: string
  images?: string[]
  createdAt: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'payfast' | 'mpesa' | 'cash'
  details: any
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready_for_pickup'
  | 'picked_up'
  | 'on_the_way'
  | 'delivered'
  | 'cancelled'
  | 'refunded'

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'paid'
  | 'failed'
  | 'refunded'
