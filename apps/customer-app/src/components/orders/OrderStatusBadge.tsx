import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { useTheme } from '../../hooks/useTheme'
import type { OrderStatus } from './types'

interface OrderStatusBadgeProps {
  status: OrderStatus
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'outline'
}

export function OrderStatusBadge({
  status,
  size = 'medium',
  variant = 'default'
}: OrderStatusBadgeProps) {
  const { colors, typography } = useTheme()

  const getStatusConfig = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          backgroundColor: colors.warning[100],
          textColor: colors.warning[700],
          borderColor: colors.warning[300],
          icon: '⏳'
        }
      case 'confirmed':
        return {
          label: 'Confirmed',
          backgroundColor: colors.info[100],
          textColor: colors.info[700],
          borderColor: colors.info[300],
          icon: '✅'
        }
      case 'preparing':
        return {
          label: 'Preparing',
          backgroundColor: colors.primary[100],
          textColor: colors.primary[700],
          borderColor: colors.primary[300],
          icon: '👨‍🍳'
        }
      case 'ready_for_pickup':
        return {
          label: 'Ready',
          backgroundColor: colors.success[100],
          textColor: colors.success[700],
          borderColor: colors.success[300],
          icon: '📦'
        }
      case 'picked_up':
        return {
          label: 'Picked Up',
          backgroundColor: colors.primary[100],
          textColor: colors.primary[700],
          borderColor: colors.primary[300],
          icon: '🚚'
        }
      case 'on_the_way':
        return {
          label: 'On the Way',
          backgroundColor: colors.primary[100],
          textColor: colors.primary[700],
          borderColor: colors.primary[300],
          icon: '🛣️'
        }
      case 'delivered':
        return {
          label: 'Delivered',
          backgroundColor: colors.success[100],
          textColor: colors.success[700],
          borderColor: colors.success[300],
          icon: '🎉'
        }
      case 'cancelled':
        return {
          label: 'Cancelled',
          backgroundColor: colors.error[100],
          textColor: colors.error[700],
          borderColor: colors.error[300],
          icon: '❌'
        }
      case 'refunded':
        return {
          label: 'Refunded',
          backgroundColor: colors.error[100],
          textColor: colors.error[700],
          borderColor: colors.error[300],
          icon: '💰'
        }
      default:
        return {
          label: 'Unknown',
          backgroundColor: colors.background.tertiary,
          textColor: colors.text.secondary,
          borderColor: colors.border.primary,
          icon: '❓'
        }
    }
  }

  const config = getStatusConfig(status)
  
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: 6,
          paddingVertical: 2,
          borderRadius: 8,
          fontSize: 10,
          iconSize: 10
        }
      case 'large':
        return {
          paddingHorizontal: 12,
          paddingVertical: 6,
          borderRadius: 16,
          fontSize: 14,
          iconSize: 14
        }
      default: // medium
        return {
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 12,
          fontSize: 12,
          iconSize: 12
        }
    }
  }

  const sizeStyles = getSizeStyles()

  const containerStyle = [
    styles.container,
    {
      paddingHorizontal: sizeStyles.paddingHorizontal,
      paddingVertical: sizeStyles.paddingVertical,
      borderRadius: sizeStyles.borderRadius,
      backgroundColor: variant === 'outline' ? 'transparent' : config.backgroundColor,
      borderWidth: variant === 'outline' ? 1 : 0,
      borderColor: config.borderColor,
    }
  ]

  const textStyle = [
    styles.text,
    {
      color: config.textColor,
      fontSize: sizeStyles.fontSize,
      fontWeight: '600' as const
    }
  ]

  return (
    <View style={containerStyle}>
      <Text style={textStyle}>
        {size !== 'small' && `${config.icon} `}{config.label}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    lineHeight: 16,
  },
})
