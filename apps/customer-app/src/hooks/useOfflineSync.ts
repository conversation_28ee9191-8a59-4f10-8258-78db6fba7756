import { useState, useEffect, useCallback } from 'react'
import { OfflineService, SyncStatus } from '../services/offline/OfflineService'

export interface UseOfflineSyncReturn {
  syncStatus: SyncStatus
  isOnline: boolean
  pendingActions: number
  isSyncing: boolean
  syncErrors: string[]
  lastSyncTime: string | null
  forceSync: () => Promise<void>
  clearPendingActions: () => Promise<void>
}

export function useOfflineSync(): UseOfflineSyncReturn {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(OfflineService.getSyncStatus())

  useEffect(() => {
    // Subscribe to sync status changes
    const unsubscribe = OfflineService.onSyncStatusChange((status) => {
      setSyncStatus(status)
    })

    // Initial status
    setSyncStatus(OfflineService.getSyncStatus())

    return unsubscribe
  }, [])

  const forceSync = useCallback(async () => {
    try {
      await OfflineService.forcSync()
    } catch (error) {
      console.error('Force sync failed:', error)
      throw error
    }
  }, [])

  const clearPendingActions = useCallback(async () => {
    try {
      await OfflineService.clearPendingActions()
    } catch (error) {
      console.error('Clear pending actions failed:', error)
      throw error
    }
  }, [])

  return {
    syncStatus,
    isOnline: syncStatus.isOnline,
    pendingActions: syncStatus.pendingActions,
    isSyncing: syncStatus.isSyncing,
    syncErrors: syncStatus.syncErrors,
    lastSyncTime: syncStatus.lastSyncTime,
    forceSync,
    clearPendingActions
  }
}
