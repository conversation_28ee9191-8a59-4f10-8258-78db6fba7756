import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { userService } from '../services/database'
import type { UserProfile, Address, PaymentMethod } from '../components/profile/types'

interface ProfileState {
  // State
  profile: UserProfile | null
  addresses: Address[]
  paymentMethods: PaymentMethod[]
  isLoading: boolean
  isUpdating: boolean
  error: string | null

  // Actions
  loadProfile: (userId: string) => Promise<void>
  updateProfile: (userId: string, updates: Partial<UserProfile>) => Promise<void>
  updatePreferences: (userId: string, preferences: Partial<UserProfile['preferences']>) => Promise<void>
  
  // Address management
  loadAddresses: (userId: string) => Promise<void>
  addAddress: (userId: string, address: Omit<Address, 'id' | 'createdAt'>) => Promise<void>
  updateAddress: (addressId: string, updates: Partial<Address>) => Promise<void>
  deleteAddress: (addressId: string) => Promise<void>
  setDefaultAddress: (userId: string, addressId: string) => Promise<void>
  
  // Payment method management
  loadPaymentMethods: (userId: string) => Promise<void>
  addPaymentMethod: (userId: string, paymentMethod: Omit<PaymentMethod, 'id' | 'createdAt'>) => Promise<void>
  deletePaymentMethod: (paymentMethodId: string) => Promise<void>
  setDefaultPaymentMethod: (userId: string, paymentMethodId: string) => Promise<void>
  
  // Utility actions
  clearError: () => void
  clearProfile: () => void
}

export const useProfileStore = create<ProfileState>()(
  persist(
    (set, get) => ({
      // Initial State
      profile: null,
      addresses: [],
      paymentMethods: [],
      isLoading: false,
      isUpdating: false,
      error: null,

      // Actions
      loadProfile: async (userId) => {
        set({ isLoading: true, error: null })
        
        try {
          const profile = await userService.getUserProfile(userId)
          if (profile) {
            set({
              profile,
              addresses: profile.addresses,
              paymentMethods: profile.paymentMethods,
              isLoading: false,
              error: null
            })
          } else {
            set({
              error: 'Profile not found',
              isLoading: false
            })
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load profile',
            isLoading: false
          })
        }
      },

      updateProfile: async (userId, updates) => {
        set({ isUpdating: true, error: null })
        
        try {
          const updatedProfile = await userService.updateUserProfile(userId, updates)
          
          set({
            profile: updatedProfile,
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update profile',
            isUpdating: false
          })
        }
      },

      updatePreferences: async (userId, preferences) => {
        const { profile } = get()
        if (!profile) return

        const updates = {
          preferences: {
            ...profile.preferences,
            ...preferences
          }
        }

        await get().updateProfile(userId, updates)
      },

      // Address management
      loadAddresses: async (userId) => {
        try {
          const addresses = await userService.getUserAddresses(userId)
          set({ addresses })
        } catch (error: any) {
          set({ error: error.message || 'Failed to load addresses' })
        }
      },

      addAddress: async (userId, addressData) => {
        set({ isUpdating: true, error: null })
        
        try {
          const newAddress = await userService.addUserAddress(userId, addressData)
          const { addresses } = get()
          
          set({
            addresses: [...addresses, newAddress],
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to add address',
            isUpdating: false
          })
        }
      },

      updateAddress: async (addressId, updates) => {
        set({ isUpdating: true, error: null })
        
        try {
          const updatedAddress = await userService.updateUserAddress(addressId, updates)
          const { addresses } = get()
          
          const updatedAddresses = addresses.map(address =>
            address.id === addressId ? updatedAddress : address
          )
          
          set({
            addresses: updatedAddresses,
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update address',
            isUpdating: false
          })
        }
      },

      deleteAddress: async (addressId) => {
        set({ isUpdating: true, error: null })
        
        try {
          await userService.deleteUserAddress(addressId)
          const { addresses } = get()
          
          set({
            addresses: addresses.filter(address => address.id !== addressId),
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to delete address',
            isUpdating: false
          })
        }
      },

      setDefaultAddress: async (userId, addressId) => {
        set({ isUpdating: true, error: null })
        
        try {
          await userService.setDefaultAddress(userId, addressId)
          const { addresses } = get()
          
          const updatedAddresses = addresses.map(address => ({
            ...address,
            isDefault: address.id === addressId
          }))
          
          set({
            addresses: updatedAddresses,
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to set default address',
            isUpdating: false
          })
        }
      },

      // Payment method management
      loadPaymentMethods: async (userId) => {
        try {
          const paymentMethods = await userService.getUserPaymentMethods(userId)
          set({ paymentMethods })
        } catch (error: any) {
          set({ error: error.message || 'Failed to load payment methods' })
        }
      },

      addPaymentMethod: async (userId, paymentMethodData) => {
        set({ isUpdating: true, error: null })
        
        try {
          const newPaymentMethod = await userService.addPaymentMethod(userId, paymentMethodData)
          const { paymentMethods } = get()
          
          set({
            paymentMethods: [...paymentMethods, newPaymentMethod],
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to add payment method',
            isUpdating: false
          })
        }
      },

      deletePaymentMethod: async (paymentMethodId) => {
        set({ isUpdating: true, error: null })
        
        try {
          await userService.deletePaymentMethod(paymentMethodId)
          const { paymentMethods } = get()
          
          set({
            paymentMethods: paymentMethods.filter(method => method.id !== paymentMethodId),
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to delete payment method',
            isUpdating: false
          })
        }
      },

      setDefaultPaymentMethod: async (userId, paymentMethodId) => {
        set({ isUpdating: true, error: null })
        
        try {
          await userService.setDefaultPaymentMethod(userId, paymentMethodId)
          const { paymentMethods } = get()
          
          const updatedPaymentMethods = paymentMethods.map(method => ({
            ...method,
            isDefault: method.id === paymentMethodId
          }))
          
          set({
            paymentMethods: updatedPaymentMethods,
            isUpdating: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to set default payment method',
            isUpdating: false
          })
        }
      },

      // Utility actions
      clearError: () => {
        set({ error: null })
      },

      clearProfile: () => {
        set({
          profile: null,
          addresses: [],
          paymentMethods: [],
          error: null
        })
      }
    }),
    {
      name: 'profile-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        profile: state.profile,
        addresses: state.addresses,
        paymentMethods: state.paymentMethods
      })
    }
  )
)
