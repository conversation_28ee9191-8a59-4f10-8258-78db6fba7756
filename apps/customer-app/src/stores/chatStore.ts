import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { ChatService } from '../services/database/ChatService'
import type { ChatMessage, Conversation, TypingUser } from '../components/chat/types'
import type { PaginatedResult } from '../services/database/BaseService'

interface ChatState {
  // State
  conversations: Conversation[]
  currentConversation: Conversation | null
  messages: Record<string, ChatMessage[]>
  typingUsers: Record<string, TypingUser[]>
  isLoading: boolean
  isLoadingMessages: boolean
  isSending: boolean
  error: string | null
  hasMoreMessages: Record<string, boolean>

  // Actions
  getConversations: (userId: string) => Promise<void>
  getConversation: (conversationId: string) => Promise<Conversation | null>
  createConversation: (type: 'customer_vendor' | 'customer_runner' | 'support', participants: string[], orderId?: string) => Promise<Conversation>
  setCurrentConversation: (conversation: Conversation | null) => void
  getMessages: (conversationId: string, options?: { loadMore?: boolean }) => Promise<void>
  sendMessage: (conversationId: string, senderId: string, content: string, type?: 'text' | 'image' | 'file' | 'location', attachments?: any[], replyTo?: string) => Promise<void>
  markMessagesAsRead: (conversationId: string, userId: string) => Promise<void>
  deleteMessage: (messageId: string) => Promise<void>
  editMessage: (messageId: string, newContent: string) => Promise<void>
  addTypingUser: (conversationId: string, user: TypingUser) => void
  removeTypingUser: (conversationId: string, userId: string) => void
  clearError: () => void
  clearMessages: (conversationId: string) => void
}

const chatService = new ChatService()

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      // Initial State
      conversations: [],
      currentConversation: null,
      messages: {},
      typingUsers: {},
      isLoading: false,
      isLoadingMessages: false,
      isSending: false,
      error: null,
      hasMoreMessages: {},

      // Actions
      getConversations: async (userId) => {
        set({ isLoading: true, error: null })
        
        try {
          const conversations = await chatService.getConversations(userId)
          set({
            conversations,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load conversations',
            isLoading: false
          })
        }
      },

      getConversation: async (conversationId) => {
        try {
          const conversation = await chatService.getConversationById(conversationId)
          if (conversation) {
            const { conversations } = get()
            const existingIndex = conversations.findIndex(c => c.id === conversationId)
            
            if (existingIndex >= 0) {
              const updatedConversations = [...conversations]
              updatedConversations[existingIndex] = conversation
              set({ conversations: updatedConversations })
            } else {
              set({ conversations: [...conversations, conversation] })
            }
          }
          return conversation
        } catch (error: any) {
          set({ error: error.message || 'Failed to load conversation' })
          return null
        }
      },

      createConversation: async (type, participants, orderId) => {
        set({ isLoading: true, error: null })
        
        try {
          const conversation = await chatService.createConversation(type, participants, orderId)
          const { conversations } = get()
          
          set({
            conversations: [conversation, ...conversations],
            currentConversation: conversation,
            isLoading: false,
            error: null
          })
          
          return conversation
        } catch (error: any) {
          set({
            error: error.message || 'Failed to create conversation',
            isLoading: false
          })
          throw error
        }
      },

      setCurrentConversation: (conversation) => {
        set({ currentConversation: conversation })
      },

      getMessages: async (conversationId, options = {}) => {
        const { loadMore = false } = options
        const { messages, hasMoreMessages } = get()
        
        if (!loadMore) {
          set({ isLoadingMessages: true, error: null })
        }

        try {
          const existingMessages = messages[conversationId] || []
          const offset = loadMore ? existingMessages.length : 0

          const result: PaginatedResult<ChatMessage> = await chatService.getMessages(conversationId, {
            limit: 50,
            offset
          })

          const newMessages = loadMore 
            ? [...existingMessages, ...result.documents]
            : result.documents

          set({
            messages: {
              ...messages,
              [conversationId]: newMessages
            },
            hasMoreMessages: {
              ...hasMoreMessages,
              [conversationId]: result.hasMore
            },
            isLoadingMessages: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load messages',
            isLoadingMessages: false
          })
        }
      },

      sendMessage: async (conversationId, senderId, content, type = 'text', attachments = [], replyTo) => {
        set({ isSending: true, error: null })
        
        try {
          const message = await chatService.sendMessage(
            conversationId,
            senderId,
            content,
            type,
            attachments,
            replyTo
          )

          const { messages, conversations } = get()
          const existingMessages = messages[conversationId] || []

          // Add message to local state
          set({
            messages: {
              ...messages,
              [conversationId]: [...existingMessages, message]
            },
            isSending: false,
            error: null
          })

          // Update conversation's last message
          const updatedConversations = conversations.map(conv => 
            conv.id === conversationId 
              ? { ...conv, lastMessage: message, updatedAt: message.timestamp }
              : conv
          )
          
          set({ conversations: updatedConversations })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send message',
            isSending: false
          })
        }
      },

      markMessagesAsRead: async (conversationId, userId) => {
        try {
          await chatService.markMessagesAsRead(conversationId, userId)
          
          const { messages, conversations } = get()
          const conversationMessages = messages[conversationId] || []
          
          // Mark messages as read in local state
          const updatedMessages = conversationMessages.map(message => 
            message.senderId !== userId ? { ...message, isRead: true } : message
          )

          // Update conversation unread count
          const updatedConversations = conversations.map(conv =>
            conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
          )

          set({
            messages: {
              ...messages,
              [conversationId]: updatedMessages
            },
            conversations: updatedConversations
          })
        } catch (error: any) {
          set({ error: error.message || 'Failed to mark messages as read' })
        }
      },

      deleteMessage: async (messageId) => {
        try {
          await chatService.deleteMessage(messageId)
          
          const { messages } = get()
          const updatedMessages = { ...messages }
          
          // Remove message from all conversations
          Object.keys(updatedMessages).forEach(conversationId => {
            updatedMessages[conversationId] = updatedMessages[conversationId].filter(
              message => message.id !== messageId
            )
          })

          set({ messages: updatedMessages })
        } catch (error: any) {
          set({ error: error.message || 'Failed to delete message' })
        }
      },

      editMessage: async (messageId, newContent) => {
        try {
          const updatedMessage = await chatService.editMessage(messageId, newContent)
          
          const { messages } = get()
          const updatedMessages = { ...messages }
          
          // Update message in all conversations
          Object.keys(updatedMessages).forEach(conversationId => {
            updatedMessages[conversationId] = updatedMessages[conversationId].map(
              message => message.id === messageId ? updatedMessage : message
            )
          })

          set({ messages: updatedMessages })
        } catch (error: any) {
          set({ error: error.message || 'Failed to edit message' })
        }
      },

      addTypingUser: (conversationId, user) => {
        const { typingUsers } = get()
        const existingUsers = typingUsers[conversationId] || []
        const filteredUsers = existingUsers.filter(u => u.id !== user.id)
        
        set({
          typingUsers: {
            ...typingUsers,
            [conversationId]: [...filteredUsers, user]
          }
        })
      },

      removeTypingUser: (conversationId, userId) => {
        const { typingUsers } = get()
        const existingUsers = typingUsers[conversationId] || []
        
        set({
          typingUsers: {
            ...typingUsers,
            [conversationId]: existingUsers.filter(u => u.id !== userId)
          }
        })
      },

      clearError: () => {
        set({ error: null })
      },

      clearMessages: (conversationId) => {
        const { messages } = get()
        const updatedMessages = { ...messages }
        delete updatedMessages[conversationId]
        
        set({ messages: updatedMessages })
      }
    }),
    {
      name: 'chat-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        conversations: state.conversations
      })
    }
  )
)
