import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { databases, DATABASE_ID } from '../lib/appwrite'

// Types
export interface Order {
  id: string
  customerId: string
  providerId: string
  serviceId: string
  status: OrderStatus
  items: OrderItem[]
  totalAmount: number
  currency: string
  paymentStatus: PaymentStatus
  paymentMethod?: PaymentMethod
  deliveryAddress: Address
  estimatedDuration: number
  scheduledDate?: string
  notes?: string
  tracking?: OrderTracking
  review?: OrderReview
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  serviceId: string
  serviceName: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
  options?: ServiceOption[]
}

export interface ServiceOption {
  id: string
  name: string
  value: string
  price: number
}

export interface Address {
  id: string
  label: string
  street: string
  city: string
  province: string
  postalCode: string
  coordinates: {
    latitude: number
    longitude: number
  }
}

export interface OrderTracking {
  status: OrderStatus
  currentLocation?: {
    latitude: number
    longitude: number
  }
  estimatedArrival?: string
  providerLocation?: {
    latitude: number
    longitude: number
  }
  updates: TrackingUpdate[]
}

export interface TrackingUpdate {
  id: string
  status: OrderStatus
  message: string
  timestamp: string
  location?: {
    latitude: number
    longitude: number
  }
}

export interface OrderReview {
  rating: number
  comment?: string
  photos?: string[]
  createdAt: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'payfast' | 'mpesa' | 'cash'
  details: any
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'on_the_way'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded'

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'paid'
  | 'failed'
  | 'refunded'

export interface OrdersState {
  // State
  orders: Order[]
  activeOrders: Order[]
  orderHistory: Order[]
  currentOrder: Order | null
  isLoading: boolean
  error: string | null

  // Actions
  createOrder: (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Order>
  updateOrder: (orderId: string, updates: Partial<Order>) => Promise<void>
  cancelOrder: (orderId: string, reason?: string) => Promise<void>
  getOrder: (orderId: string) => Promise<Order | null>
  getOrders: (filters?: OrderFilters) => Promise<void>
  trackOrder: (orderId: string) => Promise<OrderTracking | null>
  submitReview: (orderId: string, review: Omit<OrderReview, 'createdAt'>) => Promise<void>
  reorderItems: (orderId: string) => Promise<Order>
  clearError: () => void
  setCurrentOrder: (order: Order | null) => void
}

export interface OrderFilters {
  status?: OrderStatus[]
  dateFrom?: string
  dateTo?: string
  providerId?: string
  limit?: number
  offset?: number
}

export const useOrdersStore = create<OrdersState>()(
  persist(
    (set, get) => ({
      // Initial State
      orders: [],
      activeOrders: [],
      orderHistory: [],
      currentOrder: null,
      isLoading: false,
      error: null,

      // Actions
      createOrder: async (orderData) => {
        set({ isLoading: true, error: null })
        try {
          const newOrder: Order = {
            ...orderData,
            id: `order_${Date.now()}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          // Save to Appwrite
          await databases.createDocument(
            DATABASE_ID,
            'orders',
            newOrder.id,
            newOrder
          )

          const { orders } = get()
          const updatedOrders = [newOrder, ...orders]

          set({
            orders: updatedOrders,
            activeOrders: updatedOrders.filter(order => 
              !['completed', 'cancelled', 'refunded'].includes(order.status)
            ),
            currentOrder: newOrder,
            isLoading: false,
          })

          return newOrder
        } catch (error: any) {
          set({
            error: error.message || 'Failed to create order',
            isLoading: false,
          })
          throw error
        }
      },

      updateOrder: async (orderId, updates) => {
        set({ isLoading: true, error: null })
        try {
          const updatedOrder = {
            ...updates,
            updatedAt: new Date().toISOString(),
          }

          await databases.updateDocument(
            DATABASE_ID,
            'orders',
            orderId,
            updatedOrder
          )

          const { orders } = get()
          const updatedOrders = orders.map(order =>
            order.id === orderId ? { ...order, ...updatedOrder } : order
          )

          set({
            orders: updatedOrders,
            activeOrders: updatedOrders.filter(order => 
              !['completed', 'cancelled', 'refunded'].includes(order.status)
            ),
            orderHistory: updatedOrders.filter(order => 
              ['completed', 'cancelled', 'refunded'].includes(order.status)
            ),
            isLoading: false,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update order',
            isLoading: false,
          })
          throw error
        }
      },

      cancelOrder: async (orderId, reason) => {
        await get().updateOrder(orderId, {
          status: 'cancelled',
          notes: reason ? `Cancelled: ${reason}` : 'Cancelled by customer',
        })
      },

      getOrder: async (orderId) => {
        set({ isLoading: true, error: null })
        try {
          const order = await databases.getDocument(DATABASE_ID, 'orders', orderId)
          
          set({ isLoading: false })
          return order as Order
        } catch (error: any) {
          set({
            error: error.message || 'Failed to get order',
            isLoading: false,
          })
          return null
        }
      },

      getOrders: async (filters) => {
        set({ isLoading: true, error: null })
        try {
          // Build query based on filters
          const queries = []
          if (filters?.status) {
            queries.push(`status IN [${filters.status.map(s => `"${s}"`).join(',')}]`)
          }
          if (filters?.dateFrom) {
            queries.push(`createdAt >= "${filters.dateFrom}"`)
          }
          if (filters?.dateTo) {
            queries.push(`createdAt <= "${filters.dateTo}"`)
          }
          if (filters?.providerId) {
            queries.push(`providerId = "${filters.providerId}"`)
          }

          const response = await databases.listDocuments(
            DATABASE_ID,
            'orders',
            queries,
            filters?.limit || 50,
            filters?.offset || 0
          )

          const orders = response.documents as Order[]

          set({
            orders,
            activeOrders: orders.filter(order => 
              !['completed', 'cancelled', 'refunded'].includes(order.status)
            ),
            orderHistory: orders.filter(order => 
              ['completed', 'cancelled', 'refunded'].includes(order.status)
            ),
            isLoading: false,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to get orders',
            isLoading: false,
          })
        }
      },

      trackOrder: async (orderId) => {
        try {
          const order = await get().getOrder(orderId)
          return order?.tracking || null
        } catch (error) {
          return null
        }
      },

      submitReview: async (orderId, review) => {
        const reviewWithTimestamp: OrderReview = {
          ...review,
          createdAt: new Date().toISOString(),
        }

        await get().updateOrder(orderId, {
          review: reviewWithTimestamp,
        })
      },

      reorderItems: async (orderId) => {
        const { orders } = get()
        const originalOrder = orders.find(order => order.id === orderId)
        
        if (!originalOrder) {
          throw new Error('Original order not found')
        }

        const newOrderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
          customerId: originalOrder.customerId,
          providerId: originalOrder.providerId,
          serviceId: originalOrder.serviceId,
          status: 'pending',
          items: originalOrder.items,
          totalAmount: originalOrder.totalAmount,
          currency: originalOrder.currency,
          paymentStatus: 'pending',
          deliveryAddress: originalOrder.deliveryAddress,
          estimatedDuration: originalOrder.estimatedDuration,
        }

        return await get().createOrder(newOrderData)
      },

      clearError: () => {
        set({ error: null })
      },

      setCurrentOrder: (order) => {
        set({ currentOrder: order })
      },
    }),
    {
      name: 'orders-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        orders: state.orders,
        activeOrders: state.activeOrders,
        orderHistory: state.orderHistory,
      }),
    }
  )
)
