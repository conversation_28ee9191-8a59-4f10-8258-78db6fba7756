import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import type { CartItem, Cart, CheckoutData } from '../components/cart/types'
import type { MenuItem } from '../components/restaurants/types'

interface CartState {
  // State
  cart: Cart | null
  isLoading: boolean
  error: string | null

  // Actions
  addItem: (menuItem: MenuItem, restaurantId: string, restaurantName: string, customizations?: any[], specialInstructions?: string) => void
  removeItem: (itemId: string) => void
  updateItemQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void
  getItemQuantity: (menuItemId: string) => number
  getTotalItems: () => number
  canAddItem: (restaurantId: string) => boolean
  replaceCart: (restaurantId: string, restaurantName: string) => void
  calculateTotals: () => void
  setDeliveryFee: (fee: number) => void
  setTax: (tax: number) => void
  clearError: () => void
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      // Initial State
      cart: null,
      isLoading: false,
      error: null,

      // Actions
      addItem: (menuItem, restaurantId, restaurantName, customizations = [], specialInstructions) => {
        const { cart, canAddItem } = get()

        // Check if we can add items from this restaurant
        if (!canAddItem(restaurantId)) {
          set({ error: 'You can only order from one restaurant at a time. Clear your cart to order from a different restaurant.' })
          return
        }

        const customizationPrice = customizations.reduce((total, customization) => {
          return total + customization.selectedOptions.reduce((optionTotal: number, option: any) => optionTotal + option.price, 0)
        }, 0)

        const itemPrice = menuItem.price + customizationPrice
        const cartItemId = `${menuItem.id}_${Date.now()}`

        const newCartItem: CartItem = {
          id: cartItemId,
          menuItemId: menuItem.id,
          restaurantId,
          name: menuItem.name,
          description: menuItem.description,
          image: menuItem.image,
          price: itemPrice,
          quantity: 1,
          customizations: customizations.map(c => ({
            customizationId: c.id,
            name: c.name,
            selectedOptions: c.selectedOptions,
            totalPrice: c.selectedOptions.reduce((total: number, option: any) => total + option.price, 0)
          })),
          specialInstructions,
          totalPrice: itemPrice
        }

        let updatedCart: Cart

        if (!cart) {
          // Create new cart
          updatedCart = {
            id: `cart_${Date.now()}`,
            restaurantId,
            restaurantName,
            items: [newCartItem],
            subtotal: itemPrice,
            deliveryFee: 0,
            tax: 0,
            total: itemPrice,
            itemCount: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        } else {
          // Check if item already exists with same customizations
          const existingItemIndex = cart.items.findIndex(item => 
            item.menuItemId === menuItem.id && 
            JSON.stringify(item.customizations) === JSON.stringify(newCartItem.customizations) &&
            item.specialInstructions === specialInstructions
          )

          if (existingItemIndex >= 0) {
            // Update existing item quantity
            const updatedItems = [...cart.items]
            updatedItems[existingItemIndex] = {
              ...updatedItems[existingItemIndex],
              quantity: updatedItems[existingItemIndex].quantity + 1,
              totalPrice: updatedItems[existingItemIndex].totalPrice + itemPrice
            }

            updatedCart = {
              ...cart,
              items: updatedItems,
              updatedAt: new Date().toISOString()
            }
          } else {
            // Add new item
            updatedCart = {
              ...cart,
              items: [...cart.items, newCartItem],
              updatedAt: new Date().toISOString()
            }
          }
        }

        set({ cart: updatedCart, error: null })
        get().calculateTotals()
      },

      removeItem: (itemId) => {
        const { cart } = get()
        if (!cart) return

        const updatedItems = cart.items.filter(item => item.id !== itemId)

        if (updatedItems.length === 0) {
          set({ cart: null })
        } else {
          const updatedCart = {
            ...cart,
            items: updatedItems,
            updatedAt: new Date().toISOString()
          }
          set({ cart: updatedCart })
          get().calculateTotals()
        }
      },

      updateItemQuantity: (itemId, quantity) => {
        const { cart } = get()
        if (!cart) return

        if (quantity <= 0) {
          get().removeItem(itemId)
          return
        }

        const updatedItems = cart.items.map(item => {
          if (item.id === itemId) {
            const unitPrice = item.price
            return {
              ...item,
              quantity,
              totalPrice: unitPrice * quantity
            }
          }
          return item
        })

        const updatedCart = {
          ...cart,
          items: updatedItems,
          updatedAt: new Date().toISOString()
        }

        set({ cart: updatedCart })
        get().calculateTotals()
      },

      clearCart: () => {
        set({ cart: null, error: null })
      },

      getItemQuantity: (menuItemId) => {
        const { cart } = get()
        if (!cart) return 0

        return cart.items
          .filter(item => item.menuItemId === menuItemId)
          .reduce((total, item) => total + item.quantity, 0)
      },

      getTotalItems: () => {
        const { cart } = get()
        if (!cart) return 0

        return cart.items.reduce((total, item) => total + item.quantity, 0)
      },

      canAddItem: (restaurantId) => {
        const { cart } = get()
        return !cart || cart.restaurantId === restaurantId
      },

      replaceCart: (restaurantId, restaurantName) => {
        set({
          cart: {
            id: `cart_${Date.now()}`,
            restaurantId,
            restaurantName,
            items: [],
            subtotal: 0,
            deliveryFee: 0,
            tax: 0,
            total: 0,
            itemCount: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          error: null
        })
      },

      calculateTotals: () => {
        const { cart } = get()
        if (!cart) return

        const subtotal = cart.items.reduce((total, item) => total + item.totalPrice, 0)
        const itemCount = cart.items.reduce((total, item) => total + item.quantity, 0)
        const total = subtotal + cart.deliveryFee + cart.tax

        const updatedCart = {
          ...cart,
          subtotal,
          total,
          itemCount
        }

        set({ cart: updatedCart })
      },

      setDeliveryFee: (fee) => {
        const { cart } = get()
        if (!cart) return

        const updatedCart = {
          ...cart,
          deliveryFee: fee
        }

        set({ cart: updatedCart })
        get().calculateTotals()
      },

      setTax: (tax) => {
        const { cart } = get()
        if (!cart) return

        const updatedCart = {
          ...cart,
          tax
        }

        set({ cart: updatedCart })
        get().calculateTotals()
      },

      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        cart: state.cart
      })
    }
  )
)
