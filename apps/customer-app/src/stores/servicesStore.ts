import { create } from 'zustand'
import { databases, DATABASE_ID } from '../lib/appwrite'

// Types
export interface Service {
  id: string
  name: string
  description: string
  category: ServiceCategory
  providerId: string
  provider: ServiceProvider
  price: {
    amount: number
    currency: string
    type: 'fixed' | 'hourly' | 'per_item'
  }
  duration: number // in minutes
  images: string[]
  availability: ServiceAvailability
  location: ServiceLocation
  rating: {
    average: number
    count: number
  }
  features: string[]
  requirements?: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ServiceCategory {
  id: string
  name: string
  icon: string
  color: string
  description: string
}

export interface ServiceProvider {
  id: string
  name: string
  avatar?: string
  rating: {
    average: number
    count: number
  }
  verified: boolean
  responseTime: number // in minutes
  completedJobs: number
  joinedDate: string
}

export interface ServiceAvailability {
  schedule: WeeklySchedule
  exceptions: DateException[]
  advanceBooking: {
    min: number // minimum hours in advance
    max: number // maximum days in advance
  }
}

export interface WeeklySchedule {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

export interface DaySchedule {
  isAvailable: boolean
  slots: TimeSlot[]
}

export interface TimeSlot {
  start: string // HH:mm format
  end: string // HH:mm format
}

export interface DateException {
  date: string // YYYY-MM-DD format
  isAvailable: boolean
  reason?: string
  slots?: TimeSlot[]
}

export interface ServiceLocation {
  type: 'at_customer' | 'at_provider' | 'both'
  address?: {
    street: string
    city: string
    province: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  serviceRadius?: number // in kilometers
}

export interface ServicesState {
  // State
  services: Service[]
  categories: ServiceCategory[]
  featuredServices: Service[]
  nearbyServices: Service[]
  favorites: string[] // service IDs
  searchResults: Service[]
  isLoading: boolean
  error: string | null

  // Actions
  getServices: (filters?: ServiceFilters) => Promise<void>
  getService: (serviceId: string) => Promise<Service | null>
  getCategories: () => Promise<void>
  getFeaturedServices: () => Promise<void>
  getNearbyServices: (location: { latitude: number; longitude: number }, radius?: number) => Promise<void>
  searchServices: (query: string, filters?: ServiceFilters) => Promise<void>
  toggleFavorite: (serviceId: string) => void
  clearSearchResults: () => void
  clearError: () => void
}

export interface ServiceFilters {
  categoryId?: string
  priceRange?: {
    min: number
    max: number
  }
  rating?: number
  location?: {
    latitude: number
    longitude: number
    radius: number
  }
  availability?: {
    date: string
    timeSlot?: TimeSlot
  }
  sortBy?: 'price_asc' | 'price_desc' | 'rating' | 'distance' | 'popularity'
  limit?: number
  offset?: number
}

export const useServicesStore = create<ServicesState>((set, get) => ({
  // Initial State
  services: [],
  categories: [],
  featuredServices: [],
  nearbyServices: [],
  favorites: [],
  searchResults: [],
  isLoading: false,
  error: null,

  // Actions
  getServices: async (filters) => {
    set({ isLoading: true, error: null })
    try {
      // Build query based on filters
      const queries = []
      queries.push('isActive = true')

      if (filters?.categoryId) {
        queries.push(`category.id = "${filters.categoryId}"`)
      }
      if (filters?.priceRange) {
        queries.push(`price.amount >= ${filters.priceRange.min}`)
        queries.push(`price.amount <= ${filters.priceRange.max}`)
      }
      if (filters?.rating) {
        queries.push(`rating.average >= ${filters.rating}`)
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        'services',
        queries
      )

      const services = response.documents as Service[]

      set({
        services,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to get services',
        isLoading: false,
      })
    }
  },

  getService: async (serviceId) => {
    set({ isLoading: true, error: null })
    try {
      const service = await databases.getDocument(DATABASE_ID, 'services', serviceId)
      
      set({ isLoading: false })
      return service as Service
    } catch (error: any) {
      set({
        error: error.message || 'Failed to get service',
        isLoading: false,
      })
      return null
    }
  },

  getCategories: async () => {
    set({ isLoading: true, error: null })
    try {
      const response = await databases.listDocuments(DATABASE_ID, 'service_categories')
      
      const categories = response.documents as ServiceCategory[]

      set({
        categories,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to get categories',
        isLoading: false,
      })
    }
  },

  getFeaturedServices: async () => {
    set({ isLoading: true, error: null })
    try {
      // Get services with high ratings and popularity
      const queries = [
        'isActive = true',
        'rating.average >= 4.5',
      ]

      const response = await databases.listDocuments(
        DATABASE_ID,
        'services',
        queries
      )

      const featuredServices = response.documents as Service[]

      set({
        featuredServices,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to get featured services',
        isLoading: false,
      })
    }
  },

  getNearbyServices: async (location, radius = 10) => {
    set({ isLoading: true, error: null })
    try {
      // This would require geospatial queries in Appwrite
      // For now, we'll get all services and filter client-side
      const response = await databases.listDocuments(DATABASE_ID, 'services', ['isActive = true'])
      const allServices = response.documents as Service[]

      // Filter by distance (simplified calculation)
      const nearbyServices = allServices.filter(service => {
        if (!service.location.address?.coordinates) return false
        
        const distance = calculateDistance(
          location.latitude,
          location.longitude,
          service.location.address.coordinates.latitude,
          service.location.address.coordinates.longitude
        )
        
        return distance <= radius
      })

      set({
        nearbyServices,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to get nearby services',
        isLoading: false,
      })
    }
  },

  searchServices: async (query, filters) => {
    set({ isLoading: true, error: null })
    try {
      // Build search queries
      const queries = [
        'isActive = true',
        `name LIKE "%${query}%" OR description LIKE "%${query}%"`,
      ]

      if (filters?.categoryId) {
        queries.push(`category.id = "${filters.categoryId}"`)
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        'services',
        queries
      )

      const searchResults = response.documents as Service[]

      set({
        searchResults,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        error: error.message || 'Search failed',
        isLoading: false,
      })
    }
  },

  toggleFavorite: (serviceId) => {
    const { favorites } = get()
    const isFavorite = favorites.includes(serviceId)
    
    const updatedFavorites = isFavorite
      ? favorites.filter(id => id !== serviceId)
      : [...favorites, serviceId]

    set({ favorites: updatedFavorites })
  },

  clearSearchResults: () => {
    set({ searchResults: [] })
  },

  clearError: () => {
    set({ error: null })
  },
}))

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  const d = R * c // Distance in kilometers
  return d
}
