import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { restaurantService } from '../services/database'
import type { Restaurant } from '../components/restaurants/types'
import type { SearchQuery, SearchFilters, SearchResult, RecentSearch, PopularSearch } from '../components/search/types'

interface SearchState {
  // State
  searchQuery: string
  searchResults: SearchResult[]
  recentSearches: RecentSearch[]
  popularSearches: PopularSearch[]
  suggestions: string[]
  filters: SearchFilters
  isLoading: boolean
  isLoadingSuggestions: boolean
  error: string | null
  hasMore: boolean
  currentPage: number

  // Actions
  setSearchQuery: (query: string) => void
  search: (query: string, filters?: Partial<SearchFilters>) => Promise<void>
  loadMore: () => Promise<void>
  setFilters: (filters: Partial<SearchFilters>) => void
  clearSearch: () => void
  addRecentSearch: (query: string, filters: SearchFilters) => void
  removeRecentSearch: (searchId: string) => void
  clearRecentSearches: () => void
  getPopularSearches: () => Promise<void>
  getSuggestions: (query: string) => Promise<void>
  clearError: () => void
}

const defaultFilters: SearchFilters = {
  categories: [],
  cuisines: [],
  priceRange: [0, 1000],
  rating: 0,
  deliveryTime: 120,
  isOpen: false,
  isFeatured: false,
  hasDeals: false,
  dietary: {
    vegetarian: false,
    vegan: false,
    glutenFree: false,
    halal: false
  }
}

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      // Initial State
      searchQuery: '',
      searchResults: [],
      recentSearches: [],
      popularSearches: [],
      suggestions: [],
      filters: defaultFilters,
      isLoading: false,
      isLoadingSuggestions: false,
      error: null,
      hasMore: false,
      currentPage: 0,

      // Actions
      setSearchQuery: (query) => {
        set({ searchQuery: query })
      },

      search: async (query, newFilters = {}) => {
        const { filters, currentPage } = get()
        const searchFilters = { ...filters, ...newFilters }
        
        set({
          isLoading: true,
          error: null,
          searchQuery: query,
          filters: searchFilters,
          currentPage: 0
        })

        try {
          // Search restaurants
          const restaurantResults = await restaurantService.searchRestaurants(query, {
            limit: 20,
            filters: {
              isOpen: searchFilters.isOpen || undefined,
              isFeatured: searchFilters.isFeatured || undefined,
              minRating: searchFilters.rating > 0 ? searchFilters.rating : undefined
            }
          })

          // Transform restaurant results to search results
          const searchResults: SearchResult[] = restaurantResults.documents.map(restaurant => ({
            type: 'restaurant',
            id: restaurant.id,
            name: restaurant.name,
            description: restaurant.description,
            image: restaurant.image,
            rating: restaurant.rating,
            distance: restaurant.distance,
            deliveryTime: restaurant.deliveryTime,
            restaurant: {
              id: restaurant.id,
              name: restaurant.name,
              cuisine: restaurant.cuisine
            },
            relevanceScore: calculateRelevanceScore(query, restaurant)
          }))

          // Sort by relevance score
          searchResults.sort((a, b) => b.relevanceScore - a.relevanceScore)

          set({
            searchResults,
            hasMore: restaurantResults.hasMore,
            isLoading: false,
            error: null
          })

          // Add to recent searches if query is not empty
          if (query.trim()) {
            get().addRecentSearch(query, searchFilters)
          }
        } catch (error: any) {
          set({
            error: error.message || 'Search failed',
            isLoading: false
          })
        }
      },

      loadMore: async () => {
        const { searchQuery, filters, currentPage, hasMore, isLoading } = get()
        
        if (!hasMore || isLoading || !searchQuery) return

        set({ isLoading: true })

        try {
          const nextPage = currentPage + 1
          
          const restaurantResults = await restaurantService.searchRestaurants(searchQuery, {
            limit: 20,
            offset: nextPage * 20,
            filters: {
              isOpen: filters.isOpen || undefined,
              isFeatured: filters.isFeatured || undefined,
              minRating: filters.rating > 0 ? filters.rating : undefined
            }
          })

          const newResults: SearchResult[] = restaurantResults.documents.map(restaurant => ({
            type: 'restaurant',
            id: restaurant.id,
            name: restaurant.name,
            description: restaurant.description,
            image: restaurant.image,
            rating: restaurant.rating,
            distance: restaurant.distance,
            deliveryTime: restaurant.deliveryTime,
            restaurant: {
              id: restaurant.id,
              name: restaurant.name,
              cuisine: restaurant.cuisine
            },
            relevanceScore: calculateRelevanceScore(searchQuery, restaurant)
          }))

          const { searchResults } = get()
          
          set({
            searchResults: [...searchResults, ...newResults],
            hasMore: restaurantResults.hasMore,
            currentPage: nextPage,
            isLoading: false
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load more results',
            isLoading: false
          })
        }
      },

      setFilters: (newFilters) => {
        const { filters } = get()
        set({ filters: { ...filters, ...newFilters } })
      },

      clearSearch: () => {
        set({
          searchQuery: '',
          searchResults: [],
          suggestions: [],
          error: null,
          hasMore: false,
          currentPage: 0
        })
      },

      addRecentSearch: (query, filters) => {
        const { recentSearches } = get()
        
        // Remove existing search with same query
        const filteredSearches = recentSearches.filter(search => search.query !== query)
        
        // Add new search at the beginning
        const newSearch: RecentSearch = {
          id: `search_${Date.now()}`,
          query,
          filters,
          timestamp: new Date().toISOString()
        }
        
        // Keep only last 10 searches
        const updatedSearches = [newSearch, ...filteredSearches].slice(0, 10)
        
        set({ recentSearches: updatedSearches })
      },

      removeRecentSearch: (searchId) => {
        const { recentSearches } = get()
        set({
          recentSearches: recentSearches.filter(search => search.id !== searchId)
        })
      },

      clearRecentSearches: () => {
        set({ recentSearches: [] })
      },

      getPopularSearches: async () => {
        try {
          // Mock popular searches - in real app, this would come from analytics
          const popularSearches: PopularSearch[] = [
            { id: '1', query: 'pizza', count: 1250, trending: true },
            { id: '2', query: 'burger', count: 980, trending: false },
            { id: '3', query: 'sushi', count: 750, trending: true },
            { id: '4', query: 'indian food', count: 650, trending: false },
            { id: '5', query: 'chinese', count: 580, trending: false },
            { id: '6', query: 'mexican', count: 520, trending: true },
            { id: '7', query: 'healthy', count: 480, trending: true },
            { id: '8', query: 'dessert', count: 420, trending: false }
          ]

          set({ popularSearches })
        } catch (error: any) {
          console.error('Failed to load popular searches:', error)
        }
      },

      getSuggestions: async (query) => {
        if (!query.trim()) {
          set({ suggestions: [] })
          return
        }

        set({ isLoadingSuggestions: true })

        try {
          // Mock suggestions - in real app, this would be from search API
          const mockSuggestions = [
            'pizza delivery',
            'pizza hut',
            'pizza margherita',
            'burger king',
            'burger and fries',
            'sushi restaurant',
            'sushi rolls',
            'indian curry',
            'chinese takeaway',
            'mexican tacos'
          ]

          const filteredSuggestions = mockSuggestions
            .filter(suggestion => 
              suggestion.toLowerCase().includes(query.toLowerCase())
            )
            .slice(0, 5)

          set({
            suggestions: filteredSuggestions,
            isLoadingSuggestions: false
          })
        } catch (error: any) {
          set({
            suggestions: [],
            isLoadingSuggestions: false
          })
        }
      },

      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'search-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        recentSearches: state.recentSearches,
        filters: state.filters
      })
    }
  )
)

// Helper function to calculate relevance score
function calculateRelevanceScore(query: string, restaurant: Restaurant): number {
  const queryLower = query.toLowerCase()
  let score = 0

  // Name match (highest weight)
  if (restaurant.name.toLowerCase().includes(queryLower)) {
    score += 100
  }

  // Cuisine match
  if (restaurant.cuisine.some(cuisine => cuisine.toLowerCase().includes(queryLower))) {
    score += 80
  }

  // Description match
  if (restaurant.description.toLowerCase().includes(queryLower)) {
    score += 60
  }

  // Tags match
  if (restaurant.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
    score += 40
  }

  // Boost for high ratings
  score += restaurant.rating * 10

  // Boost for featured restaurants
  if (restaurant.isFeatured) {
    score += 20
  }

  // Boost for open restaurants
  if (restaurant.isOpen) {
    score += 15
  }

  return score
}
