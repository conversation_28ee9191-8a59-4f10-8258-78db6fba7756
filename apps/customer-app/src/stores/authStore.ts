import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Models } from 'appwrite'
import { enhancedAuthService, UserProfile } from '../services/enhancedAuthService'

// Types
export interface User extends UserProfile {
  addresses: Address[]
}

export interface Address {
  id: string
  label: string
  street: string
  city: string
  province: string
  postalCode: string
  country: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  isDefault: boolean
}

export interface UserPreferences {
  notifications: {
    push: boolean
    email: boolean
    sms: boolean
    orderUpdates: boolean
    promotions: boolean
  }
  location: {
    shareLocation: boolean
    backgroundLocation: boolean
  }
  language: string
  currency: string
}

export interface AuthState {
  // State
  user: User | null
  isAuthenticated: boolean
  hasCompletedOnboarding: boolean
  isLoading: boolean
  error: string | null
  session: Models.Session | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name: string, phone?: string) => Promise<void>
  logout: () => Promise<void>
  logoutAll: () => Promise<void>
  forgotPassword: (email: string) => Promise<void>
  resetPassword: (userId: string, secret: string, password: string, confirmPassword: string) => Promise<void>
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>
  sendEmailVerification: () => Promise<void>
  verifyEmail: (userId: string, secret: string) => Promise<void>
  sendPhoneVerification: (phone: string) => Promise<void>
  verifyPhone: (userId: string, secret: string) => Promise<void>
  loginWithGoogle: () => Promise<void>
  loginWithApple: () => Promise<void>
  loginWithFacebook: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  addAddress: (address: Omit<Address, 'id'>) => Promise<void>
  updateAddress: (addressId: string, updates: Partial<Address>) => Promise<void>
  deleteAddress: (addressId: string) => Promise<void>
  setDefaultAddress: (addressId: string) => Promise<void>
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>
  completeOnboarding: () => void
  clearError: () => void
  refreshUser: () => Promise<void>
  initializeAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial State
      user: null,
      isAuthenticated: false,
      hasCompletedOnboarding: false,
      isLoading: false,
      error: null,
      session: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })

        try {
          // Login with enhanced auth service
          const session = await enhancedAuthService.login({ email, password })
          const userData = await enhancedAuthService.getCurrentUser()

          if (!userData) {
            throw new Error('Failed to get user data')
          }

          // Transform to our User interface
          const userProfile = enhancedAuthService.transformUserToProfile(userData)
          const user: User = {
            ...userProfile,
            addresses: userData.prefs?.addresses || [],
          }

          set({
            user,
            session,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Login failed',
          })
          throw error
        }
      },

      register: async (email: string, password: string, name: string, phone?: string) => {
        set({ isLoading: true, error: null })
        try {
          const userData = await enhancedAuthService.register({
            email,
            password,
            name,
            phone,
          })

          // Get session and user data after registration
          const session = await enhancedAuthService.getCurrentSession()
          const userProfile = enhancedAuthService.transformUserToProfile(userData)
          const user: User = {
            ...userProfile,
            addresses: [],
          }

          set({
            user,
            session,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed',
            isLoading: false,
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          await enhancedAuthService.logout()

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Logout failed',
            isLoading: false,
          })
        }
      },

      logoutAll: async () => {
        set({ isLoading: true })
        try {
          await enhancedAuthService.logoutAll()

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Logout failed',
            isLoading: false,
          })
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.forgotPassword(email)
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send reset email',
            isLoading: false,
          })
          throw error
        }
      },

      resetPassword: async (userId: string, secret: string, password: string, confirmPassword: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.resetPassword(userId, secret, password, confirmPassword)
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Password reset failed',
            isLoading: false,
          })
          throw error
        }
      },

      updatePassword: async (currentPassword: string, newPassword: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.updatePassword(currentPassword, newPassword)
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update password',
            isLoading: false,
          })
          throw error
        }
      },

      sendEmailVerification: async () => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.sendEmailVerification()
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send email verification',
            isLoading: false,
          })
          throw error
        }
      },

      verifyEmail: async (userId: string, secret: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.verifyEmail(userId, secret)
          // Refresh user data to update verification status
          await get().refreshUser()
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Email verification failed',
            isLoading: false,
          })
          throw error
        }
      },

      sendPhoneVerification: async (phone: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.sendPhoneVerification(phone)
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send phone verification',
            isLoading: false,
          })
          throw error
        }
      },

      verifyPhone: async (userId: string, secret: string) => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.verifyPhone(userId, secret)
          // Refresh user data to update verification status
          await get().refreshUser()
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Phone verification failed',
            isLoading: false,
          })
          throw error
        }
      },

      loginWithGoogle: async () => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.loginWithGoogle()
          // After OAuth redirect, the user should be logged in
          await get().refreshUser()
        } catch (error: any) {
          set({
            error: error.message || 'Google login failed',
            isLoading: false,
          })
          throw error
        }
      },

      loginWithApple: async () => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.loginWithApple()
          // After OAuth redirect, the user should be logged in
          await get().refreshUser()
        } catch (error: any) {
          set({
            error: error.message || 'Apple login failed',
            isLoading: false,
          })
          throw error
        }
      },

      loginWithFacebook: async () => {
        set({ isLoading: true, error: null })
        try {
          await enhancedAuthService.loginWithFacebook()
          // After OAuth redirect, the user should be logged in
          await get().refreshUser()
        } catch (error: any) {
          set({
            error: error.message || 'Facebook login failed',
            isLoading: false,
          })
          throw error
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        set({ isLoading: true, error: null })
        try {
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          // Update name if provided
          if (updates.name) {
            await account.updateName(updates.name)
          }

          // Update email if provided
          if (updates.email) {
            await account.updateEmail(updates.email, user.email)
          }

          // Update phone if provided
          if (updates.phone) {
            await account.updatePhone(updates.phone, '')
          }

          const updatedUser = { ...user, ...updates }
          set({ user: updatedUser, isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Profile update failed',
            isLoading: false,
          })
          throw error
        }
      },

      addAddress: async (address: Omit<Address, 'id'>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const newAddress: Address = {
          ...address,
          id: `addr_${Date.now()}`,
        }

        const updatedUser = {
          ...user,
          addresses: [...user.addresses, newAddress],
        }

        set({ user: updatedUser })
      },

      updateAddress: async (addressId: string, updates: Partial<Address>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.map(addr =>
          addr.id === addressId ? { ...addr, ...updates } : addr
        )

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      deleteAddress: async (addressId: string) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.filter(addr => addr.id !== addressId)

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      setDefaultAddress: async (addressId: string) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.map(addr => ({
          ...addr,
          isDefault: addr.id === addressId,
        }))

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      updatePreferences: async (preferences: Partial<UserPreferences>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedPreferences = {
          ...user.preferences,
          ...preferences,
        }

        set({
          user: {
            ...user,
            preferences: updatedPreferences,
          },
        })
      },

      completeOnboarding: () => {
        set({ hasCompletedOnboarding: true })
      },

      clearError: () => {
        set({ error: null })
      },

      refreshUser: async () => {
        set({ isLoading: true })
        try {
          const user = await account.get()
          
          const { user: currentUser } = get()
          if (currentUser) {
            set({
              user: {
                ...currentUser,
                email: user.email,
                name: user.name,
                phone: user.phone,
                updatedAt: user.$updatedAt,
              },
              isLoading: false,
            })
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to refresh user data',
            isLoading: false,
          })
        }
      },

      initializeAuth: async () => {
        try {
          const session = await enhancedAuthService.getCurrentSession()
          const userData = await enhancedAuthService.getCurrentUser()

          if (session && userData) {
            const userProfile = enhancedAuthService.transformUserToProfile(userData)
            const user: User = {
              ...userProfile,
              addresses: userData.prefs?.addresses || [],
            }

            set({
              user,
              session,
              isAuthenticated: true,
              error: null,
            })
          } else {
            set({
              user: null,
              session: null,
              isAuthenticated: false,
              error: null,
            })
          }
        } catch (error: any) {
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            error: null,
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
        hasCompletedOnboarding: state.hasCompletedOnboarding,
      }),
    }
  )
)
