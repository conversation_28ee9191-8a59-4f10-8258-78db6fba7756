import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { restaurantService } from '../services/database'
import type { Restaurant, MenuCategory, RestaurantFilters } from '../components/restaurants/types'
import type { PaginatedResult } from '../services/database/BaseService'

interface RestaurantState {
  // State
  restaurants: Restaurant[]
  featuredRestaurants: Restaurant[]
  nearbyRestaurants: Restaurant[]
  currentRestaurant: Restaurant | null
  currentMenu: MenuCategory[]
  searchResults: Restaurant[]
  isLoading: boolean
  isLoadingMenu: boolean
  error: string | null
  hasMore: boolean
  currentPage: number
  filters: RestaurantFilters
  searchQuery: string

  // Actions
  getRestaurants: (options?: { refresh?: boolean; loadMore?: boolean }) => Promise<void>
  getFeaturedRestaurants: () => Promise<void>
  getNearbyRestaurants: (latitude: number, longitude: number, radius?: number) => Promise<void>
  getRestaurantById: (id: string) => Promise<Restaurant | null>
  getRestaurantMenu: (restaurantId: string) => Promise<void>
  searchRestaurants: (query: string, filters?: Partial<RestaurantFilters>) => Promise<void>
  setFilters: (filters: Partial<RestaurantFilters>) => void
  clearSearch: () => void
  clearError: () => void
  setCurrentRestaurant: (restaurant: Restaurant | null) => void
}

const defaultFilters: RestaurantFilters = {
  categories: [],
  cuisines: [],
  priceRange: [0, 1000],
  rating: 0,
  deliveryTime: 120,
  isOpen: false,
  isFeatured: false,
  hasDeals: false,
  distance: 50,
  sortBy: 'distance',
  dietary: {
    vegetarian: false,
    vegan: false,
    glutenFree: false,
    halal: false
  }
}

export const useRestaurantStore = create<RestaurantState>()(
  persist(
    (set, get) => ({
      // Initial State
      restaurants: [],
      featuredRestaurants: [],
      nearbyRestaurants: [],
      currentRestaurant: null,
      currentMenu: [],
      searchResults: [],
      isLoading: false,
      isLoadingMenu: false,
      error: null,
      hasMore: true,
      currentPage: 0,
      filters: defaultFilters,
      searchQuery: '',

      // Actions
      getRestaurants: async (options = {}) => {
        const { refresh = false, loadMore = false } = options
        const { currentPage, restaurants } = get()

        if (!refresh && !loadMore) {
          set({ isLoading: true, error: null })
        }

        try {
          const page = loadMore ? currentPage + 1 : 0
          const limit = 20

          const result: PaginatedResult<Restaurant> = await restaurantService.getRestaurants({
            limit,
            offset: page * limit,
            orderBy: 'rating',
            orderDirection: 'desc'
          })

          const newRestaurants = loadMore ? [...restaurants, ...result.documents] : result.documents

          set({
            restaurants: newRestaurants,
            hasMore: result.hasMore,
            currentPage: page,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load restaurants',
            isLoading: false
          })
        }
      },

      getFeaturedRestaurants: async () => {
        try {
          const featured = await restaurantService.getFeaturedRestaurants(10)
          set({ featuredRestaurants: featured })
        } catch (error: any) {
          console.error('Error loading featured restaurants:', error)
          set({ error: error.message || 'Failed to load featured restaurants' })
        }
      },

      getNearbyRestaurants: async (latitude, longitude, radius = 10) => {
        set({ isLoading: true, error: null })
        
        try {
          const nearby = await restaurantService.getNearbyRestaurants(latitude, longitude, radius, 20)
          
          // Calculate distances client-side
          const restaurantsWithDistance = nearby.map(restaurant => ({
            ...restaurant,
            distance: calculateDistance(
              latitude,
              longitude,
              restaurant.address.coordinates.latitude,
              restaurant.address.coordinates.longitude
            )
          })).sort((a, b) => a.distance - b.distance)

          set({
            nearbyRestaurants: restaurantsWithDistance,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load nearby restaurants',
            isLoading: false
          })
        }
      },

      getRestaurantById: async (id) => {
        try {
          const restaurant = await restaurantService.getRestaurantById(id)
          if (restaurant) {
            set({ currentRestaurant: restaurant })
          }
          return restaurant
        } catch (error: any) {
          set({ error: error.message || 'Failed to load restaurant' })
          return null
        }
      },

      getRestaurantMenu: async (restaurantId) => {
        set({ isLoadingMenu: true, error: null })
        
        try {
          const menu = await restaurantService.getRestaurantMenu(restaurantId)
          set({
            currentMenu: menu,
            isLoadingMenu: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to load menu',
            isLoadingMenu: false
          })
        }
      },

      searchRestaurants: async (query, filters = {}) => {
        set({ isLoading: true, error: null, searchQuery: query })
        
        try {
          const searchFilters = { ...get().filters, ...filters }
          
          const result = await restaurantService.searchRestaurants(query, {
            limit: 50,
            filters: {
              isOpen: searchFilters.isOpen || undefined,
              isFeatured: searchFilters.isFeatured || undefined,
              minRating: searchFilters.rating > 0 ? searchFilters.rating : undefined
            }
          })

          set({
            searchResults: result.documents,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to search restaurants',
            isLoading: false
          })
        }
      },

      setFilters: (newFilters) => {
        set({ filters: { ...get().filters, ...newFilters } })
      },

      clearSearch: () => {
        set({
          searchResults: [],
          searchQuery: '',
          error: null
        })
      },

      clearError: () => {
        set({ error: null })
      },

      setCurrentRestaurant: (restaurant) => {
        set({ currentRestaurant: restaurant })
      }
    }),
    {
      name: 'restaurant-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        featuredRestaurants: state.featuredRestaurants,
        filters: state.filters
      })
    }
  )
)

// Utility function to calculate distance between two coordinates
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // Radius of the Earth in kilometers
  const dLat = deg2rad(lat2 - lat1)
  const dLon = deg2rad(lon2 - lon1)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const d = R * c // Distance in kilometers
  return Math.round(d * 10) / 10 // Round to 1 decimal place
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180)
}
