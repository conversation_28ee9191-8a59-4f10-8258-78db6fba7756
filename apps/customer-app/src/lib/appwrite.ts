import { Client, Account, Databases, Storage, Functions } from 'appwrite'
import Constants from 'expo-constants'

// Get configuration from environment
const config = {
  endpoint: Constants.expoConfig?.extra?.appwriteEndpoint || 'https://cloud.appwrite.io/v1',
  projectId: Constants.expoConfig?.extra?.appwriteProjectId || '',
  databaseId: Constants.expoConfig?.extra?.appwriteDatabaseId || 'hvppyplug-main',
  storageId: Constants.expoConfig?.extra?.appwriteStorageId || 'images',
}

// Validate configuration
if (!config.projectId) {
  throw new Error('Appwrite Project ID is not configured. Please check your environment variables.')
}

console.log('🔧 Initializing Appwrite with config:', {
  endpoint: config.endpoint,
  projectId: config.projectId,
  databaseId: config.databaseId,
  storageId: config.storageId,
})

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)

// Initialize Appwrite services
export const account = new Account(client)
export const databases = new Databases(client)
export const storage = new Storage(client)
export const functions = new Functions(client)

// Export client and config
export { client, config }

// Database and collection IDs
export const DATABASE_ID = config.databaseId
export const COLLECTIONS = {
  USERS: 'users',
  VENDORS: 'vendors',
  MENU_ITEMS: 'menu-items',
  ORDERS: 'orders',
  OTP_CODES: 'otp-codes',
} as const

// Storage bucket IDs
export const BUCKETS = {
  IMAGES: config.storageId,
} as const

// Function IDs
export const FUNCTIONS_ID = {
  HVPPY_API: 'hvppy-api',
} as const

// Helper function to check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    await account.get()
    return true
  } catch {
    return false
  }
}

// Helper function to get current user
export const getCurrentUser = async () => {
  try {
    return await account.get()
  } catch (error) {
    console.log('User not authenticated')
    return null
  }
}
