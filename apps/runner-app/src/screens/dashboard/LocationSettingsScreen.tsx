import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeft,
  MapPin,
  Navigation,
  Shield,
  Battery,
  Wifi,
  Clock,
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { Card, Button } from '../../components/ui'
import { LocationPermissionHandler, LocationTracker } from '../../components/business'
import { useLocationStore } from '../../stores/locationStore'

export function LocationSettingsScreen({ navigation }: any) {
  const { theme } = useTheme()
  const {
    isTracking,
    isLocationShared,
    currentLocation,
    enableLocationSharing,
    disableLocationSharing,
    startTracking,
    stopTracking,
  } = useLocationStore()

  const [settings, setSettings] = useState({
    backgroundTracking: true,
    highAccuracy: true,
    shareWithCustomers: true,
    autoStartTracking: false,
    batteryOptimization: true,
  })

  const handleSettingChange = (key: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    
    // Handle specific setting changes
    switch (key) {
      case 'shareWithCustomers':
        if (value) {
          enableLocationSharing()
        } else {
          disableLocationSharing()
        }
        break
      case 'autoStartTracking':
        if (value && !isTracking) {
          Alert.alert(
            'Auto-Start Tracking',
            'Location tracking will automatically start when you go online.',
            [{ text: 'OK' }]
          )
        }
        break
    }
  }

  const settingsOptions = [
    {
      id: 'backgroundTracking',
      title: 'Background Tracking',
      description: 'Continue tracking location when app is in background',
      icon: Navigation,
      value: settings.backgroundTracking,
      color: theme.colors.primary[600],
    },
    {
      id: 'highAccuracy',
      title: 'High Accuracy Mode',
      description: 'Use GPS for more precise location tracking',
      icon: MapPin,
      value: settings.highAccuracy,
      color: theme.colors.success[600],
    },
    {
      id: 'shareWithCustomers',
      title: 'Share with Customers',
      description: 'Allow customers to see your location during delivery',
      icon: Shield,
      value: settings.shareWithCustomers,
      color: theme.colors.secondary[600],
    },
    {
      id: 'autoStartTracking',
      title: 'Auto-Start Tracking',
      description: 'Automatically start tracking when going online',
      icon: Wifi,
      value: settings.autoStartTracking,
      color: theme.colors.warning[600],
    },
    {
      id: 'batteryOptimization',
      title: 'Battery Optimization',
      description: 'Reduce location updates to save battery',
      icon: Battery,
      value: settings.batteryOptimization,
      color: theme.colors.gray[600],
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Button
          title=""
          variant="ghost"
          size="sm"
          icon={<ArrowLeft />}
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        />
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Location Settings
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {/* Location Permission Handler */}
        <View style={styles.section}>
          <LocationPermissionHandler showAlways={true}>
            {/* Current Location Status */}
            <LocationTracker
              isOnline={isTracking}
              showControls={true}
              compact={false}
            />
          </LocationPermissionHandler>
        </View>

        {/* Location Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Tracking Preferences
          </Text>

          <Card variant="elevated" size="md">
            {settingsOptions.map((option, index) => (
              <View key={option.id}>
                <View style={styles.settingItem}>
                  <View style={styles.settingLeft}>
                    <View style={[styles.settingIcon, { backgroundColor: `${option.color}20` }]}>
                      <option.icon size={20} color={option.color} />
                    </View>
                    <View style={styles.settingText}>
                      <Text style={[styles.settingTitle, { color: theme.colors.gray[900] }]}>
                        {option.title}
                      </Text>
                      <Text style={[styles.settingDescription, { color: theme.colors.gray[600] }]}>
                        {option.description}
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={option.value}
                    onValueChange={(value) => handleSettingChange(option.id, value)}
                    trackColor={{
                      false: theme.colors.gray[300],
                      true: theme.colors.primary[600],
                    }}
                    thumbColor={option.value ? theme.colors.white : theme.colors.gray[50]}
                  />
                </View>
                {index < settingsOptions.length - 1 && (
                  <View style={[styles.separator, { backgroundColor: theme.colors.gray[200] }]} />
                )}
              </View>
            ))}
          </Card>
        </View>

        {/* Location Info */}
        {currentLocation && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Current Location Details
            </Text>

            <Card variant="elevated" size="md">
              <View style={styles.locationDetails}>
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: theme.colors.gray[600] }]}>
                    Latitude:
                  </Text>
                  <Text style={[styles.detailValue, { color: theme.colors.gray[900] }]}>
                    {currentLocation.coords.latitude.toFixed(6)}
                  </Text>
                </View>
                
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: theme.colors.gray[600] }]}>
                    Longitude:
                  </Text>
                  <Text style={[styles.detailValue, { color: theme.colors.gray[900] }]}>
                    {currentLocation.coords.longitude.toFixed(6)}
                  </Text>
                </View>
                
                {currentLocation.coords.accuracy && (
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.gray[600] }]}>
                      Accuracy:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.gray[900] }]}>
                      ±{currentLocation.coords.accuracy.toFixed(0)}m
                    </Text>
                  </View>
                )}
                
                {currentLocation.coords.speed && (
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.gray[600] }]}>
                      Speed:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.gray[900] }]}>
                      {(currentLocation.coords.speed * 3.6).toFixed(1)} km/h
                    </Text>
                  </View>
                )}
                
                <View style={styles.detailRow}>
                  <Clock size={14} color={theme.colors.gray[500]} />
                  <Text style={[styles.timestampText, { color: theme.colors.gray[500] }]}>
                    Updated: {new Date(currentLocation.timestamp).toLocaleString()}
                  </Text>
                </View>
              </View>
            </Card>
          </View>
        )}

        {/* Privacy Notice */}
        <View style={styles.section}>
          <Card variant="outline" size="md">
            <View style={styles.privacyNotice}>
              <Shield size={20} color={theme.colors.gray[600]} />
              <View style={styles.privacyText}>
                <Text style={[styles.privacyTitle, { color: theme.colors.gray[900] }]}>
                  Privacy & Security
                </Text>
                <Text style={[styles.privacyDescription, { color: theme.colors.gray[600] }]}>
                  Your location data is only used for delivery tracking and is shared with customers only during active deliveries. We never store or share your location data for any other purpose.
                </Text>
              </View>
            </View>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  separator: {
    height: 1,
    marginLeft: 56,
  },
  locationDetails: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    minWidth: 80,
  },
  detailValue: {
    fontSize: 14,
    flex: 1,
  },
  timestampText: {
    fontSize: 12,
    marginLeft: 4,
  },
  privacyNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  privacyText: {
    flex: 1,
  },
  privacyTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  privacyDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
})
