import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useNavigation } from '@react-navigation/native'
import {
  MapPin,
  DollarSign,
  Package,
  Bell
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'
import { Card } from '../../components/ui'
import { StatusToggle, EarningsSummary, LocationTracker } from '../../components/business'

export function DashboardHomeScreen() {
  const { theme } = useTheme()
  const navigation = useNavigation()
  const { runner } = useAuthStore()
  const [isOnline, setIsOnline] = React.useState(false)

  // Mock data - will be replaced with real data
  const todayStats = {
    earnings: 245.50,
    deliveries: 8,
    hours: 6.5,
    rating: 4.8,
  }

  const handleQuickAction = (actionId: string) => {
    switch (actionId) {
      case 'available-orders':
        navigation.navigate('Orders' as never)
        break
      case 'earnings':
        navigation.navigate('Earnings' as never)
        break
      case 'location':
        // For now, we'll create a simple alert. In a full implementation,
        // you'd navigate to a location settings screen
        console.log('Navigate to location settings')
        break
      default:
        console.log(`Unknown action: ${actionId}`)
    }
  }

  const quickActions = [
    {
      id: 'available-orders',
      title: 'Available Orders',
      subtitle: '3 orders nearby',
      icon: Package,
      color: theme.colors.primary[600],
      bgColor: theme.colors.primary[50],
    },
    {
      id: 'earnings',
      title: 'Today\'s Earnings',
      subtitle: `R${todayStats.earnings.toFixed(2)}`,
      icon: DollarSign,
      color: theme.colors.success[600],
      bgColor: theme.colors.success[50],
    },
    {
      id: 'location',
      title: 'Update Location',
      subtitle: 'Share your location',
      icon: MapPin,
      color: theme.colors.secondary[600],
      bgColor: theme.colors.secondary[50],
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
          <View style={styles.headerContent}>
            <View>
              <Text style={[styles.greeting, { color: theme.colors.gray[600] }]}>
                Good morning,
              </Text>
              <Text style={[styles.runnerName, { color: theme.colors.gray[900] }]}>
                {runner?.name || 'Runner'}
              </Text>
            </View>
            
            <TouchableOpacity style={styles.notificationButton}>
              <Bell size={24} color={theme.colors.gray[600]} />
            </TouchableOpacity>
          </View>

          {/* Online Status Toggle */}
          <StatusToggle
            isOnline={isOnline}
            onToggle={setIsOnline}
            stats={{
              activeTime: isOnline ? '2h 15m' : undefined,
              ordersReceived: isOnline ? 3 : undefined,
              currentLocation: 'Orlando West, Soweto',
            }}
          />
        </View>

        {/* Earnings Summary */}
        <View style={styles.section}>
          <EarningsSummary
            todayEarnings={todayStats.earnings}
            weekEarnings={1456.80}
            monthEarnings={5234.20}
            onViewDetails={() => console.log('View earnings details')}
          />
        </View>

        {/* Location Tracking */}
        <View style={styles.section}>
          <LocationTracker
            isOnline={isOnline}
            onLocationUpdate={(location) => {
              console.log('Location updated:', location)
              // Update quick actions with current location info
            }}
            showControls={false}
            compact={false}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Quick Actions
          </Text>

          <View style={styles.actionsGrid}>
            {quickActions.map((action) => (
              <Card
                key={action.id}
                variant="elevated"
                size="sm"
                onPress={() => handleQuickAction(action.id)}
                style={styles.actionCard}
              >
                <View style={styles.actionContent}>
                  <View style={[styles.actionIcon, { backgroundColor: action.bgColor }]}>
                    <action.icon size={24} color={action.color} />
                  </View>
                  <View style={styles.actionText}>
                    <Text style={[styles.actionTitle, { color: theme.colors.gray[900] }]}>
                      {action.title}
                    </Text>
                    <Text style={[styles.actionSubtitle, { color: theme.colors.gray[600] }]}>
                      {action.subtitle}
                    </Text>
                  </View>
                </View>
              </Card>
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Recent Activity
            </Text>
            <TouchableOpacity>
              <Text style={[styles.seeAllText, { color: theme.colors.primary[600] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.activityCard, { backgroundColor: theme.colors.white }]}>
            <Text style={[styles.activityText, { color: theme.colors.gray[600] }]}>
              No recent activity. Start accepting orders to see your delivery history here.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  greeting: {
    fontSize: 16,
    marginBottom: 4,
  },
  runnerName: {
    fontSize: 24,
    fontWeight: '700',
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusCard: {
    borderRadius: 12,
    padding: 16,
  },
  statusContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 16,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  actionsGrid: {
    gap: 12,
  },
  actionCard: {
    marginBottom: 12,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 14,
  },
  activityCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  activityText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
