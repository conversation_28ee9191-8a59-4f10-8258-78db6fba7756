import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Eye, EyeOff, Mail, Lock, User, Phone, Truck } from 'lucide-react-native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'

import type { RootStackParamList } from '../../types/navigation'
import type { VehicleType } from '../../types'
import { useAuthStore, type RegisterData } from '../../stores/authStore'
import { useTheme } from '../../providers/ThemeProvider'

type Props = NativeStackScreenProps<RootStackParamList, 'Register'>

const VEHICLE_TYPES: { value: VehicleType; label: string; icon: string }[] = [
  { value: 'bicycle', label: 'Bicycle', icon: '🚲' },
  { value: 'motorcycle', label: 'Motorcycle', icon: '🏍️' },
  { value: 'scooter', label: 'Scooter', icon: '🛵' },
  { value: 'car', label: 'Car', icon: '🚗' },
]

export function RegisterScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { register, isLoading, error, clearError } = useAuthStore()
  
  const [formData, setFormData] = useState<RegisterData>({
    name: '',
    email: '',
    password: '',
    phone: '',
    vehicleType: 'bicycle',
  })
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters'
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Phone validation
    const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid South African phone number'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number'
    }

    // Confirm password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (confirmPassword !== formData.password) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleRegister = async () => {
    clearError()
    
    if (!validateForm()) return

    try {
      await register({
        ...formData,
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.replace(/\s/g, ''),
      })
      
      Alert.alert(
        'Registration Successful!',
        'Your account has been created. Please check your email to verify your account before signing in.',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Login'),
          },
        ]
      )
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message || 'Please try again.')
    }
  }

  const updateFormData = (field: keyof RegisterData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.white }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={[styles.logoContainer, { backgroundColor: theme.colors.primary[600] }]}>
              <Truck size={32} color={theme.colors.white} />
            </View>
            <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
              Join HVPPYPlug+
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
              Start earning by delivering in Soweto
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Name Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Full Name
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: errors.name ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <User size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Enter your full name"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={formData.name}
                  onChangeText={(text) => updateFormData('name', text)}
                  autoCapitalize="words"
                  editable={!isLoading}
                />
              </View>
              {errors.name ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {errors.name}
                </Text>
              ) : null}
            </View>

            {/* Email Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Email Address
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: errors.email ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Mail size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Enter your email"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={formData.email}
                  onChangeText={(text) => updateFormData('email', text)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                />
              </View>
              {errors.email ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {errors.email}
                </Text>
              ) : null}
            </View>

            {/* Phone Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Phone Number
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: errors.phone ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Phone size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="e.g., ************"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={formData.phone}
                  onChangeText={(text) => updateFormData('phone', text)}
                  keyboardType="phone-pad"
                  editable={!isLoading}
                />
              </View>
              {errors.phone ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {errors.phone}
                </Text>
              ) : null}
            </View>

            {/* Vehicle Type Selection */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Vehicle Type
              </Text>
              <View style={styles.vehicleGrid}>
                {VEHICLE_TYPES.map((vehicle) => (
                  <TouchableOpacity
                    key={vehicle.value}
                    style={[
                      styles.vehicleOption,
                      {
                        borderColor: formData.vehicleType === vehicle.value 
                          ? theme.colors.primary[600] 
                          : theme.colors.gray[300],
                        backgroundColor: formData.vehicleType === vehicle.value
                          ? theme.colors.primary[50]
                          : theme.colors.gray[50],
                      }
                    ]}
                    onPress={() => updateFormData('vehicleType', vehicle.value)}
                    disabled={isLoading}
                  >
                    <Text style={styles.vehicleIcon}>{vehicle.icon}</Text>
                    <Text style={[
                      styles.vehicleLabel,
                      {
                        color: formData.vehicleType === vehicle.value
                          ? theme.colors.primary[700]
                          : theme.colors.gray[700]
                      }
                    ]}>
                      {vehicle.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Password Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Password
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: errors.password ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Lock size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Create a strong password"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={formData.password}
                  onChangeText={(text) => updateFormData('password', text)}
                  secureTextEntry={!showPassword}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff size={20} color={theme.colors.gray[500]} />
                  ) : (
                    <Eye size={20} color={theme.colors.gray[500]} />
                  )}
                </TouchableOpacity>
              </View>
              {errors.password ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {errors.password}
                </Text>
              ) : null}
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Confirm Password
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: errors.confirmPassword ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Lock size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Confirm your password"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text)
                    if (errors.confirmPassword) {
                      setErrors(prev => ({ ...prev, confirmPassword: '' }))
                    }
                  }}
                  secureTextEntry={!showConfirmPassword}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={styles.eyeIcon}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={20} color={theme.colors.gray[500]} />
                  ) : (
                    <Eye size={20} color={theme.colors.gray[500]} />
                  )}
                </TouchableOpacity>
              </View>
              {errors.confirmPassword ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {errors.confirmPassword}
                </Text>
              ) : null}
            </View>

            {/* Register Button */}
            <TouchableOpacity
              style={[
                styles.registerButton,
                { 
                  backgroundColor: isLoading ? theme.colors.gray[400] : theme.colors.primary[600],
                  opacity: isLoading ? 0.7 : 1
                }
              ]}
              onPress={handleRegister}
              disabled={isLoading}
            >
              <Text style={[styles.registerButtonText, { color: theme.colors.white }]}>
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Text>
            </TouchableOpacity>

            {/* Error Message */}
            {error && (
              <View style={[styles.errorContainer, { backgroundColor: theme.colors.error[50] }]}>
                <Text style={[styles.errorMessage, { color: theme.colors.error[700] }]}>
                  {error}
                </Text>
              </View>
            )}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.gray[600] }]}>
              Already have an account?{' '}
            </Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Login')}
              disabled={isLoading}
            >
              <Text style={[styles.footerLink, { color: theme.colors.primary[600] }]}>
                Sign In
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    marginTop: 10,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 14,
    height: 48,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  vehicleGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vehicleOption: {
    width: '48%',
    borderWidth: 2,
    borderRadius: 10,
    padding: 16,
    alignItems: 'center',
    marginBottom: 8,
  },
  vehicleIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  vehicleLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  registerButton: {
    height: 48,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  registerButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    paddingBottom: 20,
  },
  footerText: {
    fontSize: 14,
  },
  footerLink: {
    fontSize: 14,
    fontWeight: '600',
  },
})
