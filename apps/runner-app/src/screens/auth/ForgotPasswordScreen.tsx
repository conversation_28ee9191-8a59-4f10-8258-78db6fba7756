import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Mail, ArrowLeft, Lock } from 'lucide-react-native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'

import type { RootStackParamList } from '../../types/navigation'
import { useAuthStore } from '../../stores/authStore'
import { useTheme } from '../../providers/ThemeProvider'

type Props = NativeStackScreenProps<RootStackParamList, 'ForgotPassword'>

export function ForgotPasswordScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { forgotPassword, isLoading, error, clearError } = useAuthStore()
  
  const [email, setEmail] = useState('')
  const [emailError, setEmailError] = useState('')
  const [isEmailSent, setIsEmailSent] = useState(false)

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleSendResetEmail = async () => {
    clearError()
    setEmailError('')

    // Validation
    if (!email.trim()) {
      setEmailError('Email is required')
      return
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      return
    }

    try {
      await forgotPassword(email.trim().toLowerCase())
      setIsEmailSent(true)
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to send reset email. Please try again.'
      )
    }
  }

  const handleBackToLogin = () => {
    navigation.navigate('Login')
  }

  if (isEmailSent) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.white }]}>
        <View style={styles.content}>
          {/* Success Header */}
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: theme.colors.success[100] }]}>
              <Mail size={40} color={theme.colors.success[600]} />
            </View>
            <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
              Check Your Email
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
              We've sent a password reset link to:
            </Text>
            <Text style={[styles.emailText, { color: theme.colors.primary[600] }]}>
              {email}
            </Text>
          </View>

          {/* Instructions */}
          <View style={styles.instructions}>
            <Text style={[styles.instructionText, { color: theme.colors.gray[700] }]}>
              Please check your email and click the reset link to create a new password.
            </Text>
            <Text style={[styles.instructionText, { color: theme.colors.gray[700] }]}>
              If you don't see the email, check your spam folder.
            </Text>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.colors.primary[600] }]}
              onPress={handleBackToLogin}
            >
              <Text style={[styles.primaryButtonText, { color: theme.colors.white }]}>
                Back to Sign In
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={() => setIsEmailSent(false)}
            >
              <Text style={[styles.secondaryButtonText, { color: theme.colors.primary[600] }]}>
                Try Different Email
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.white }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header with Back Button */}
          <View style={styles.headerWithBack}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
              disabled={isLoading}
            >
              <ArrowLeft size={24} color={theme.colors.gray[700]} />
            </TouchableOpacity>
          </View>

          {/* Main Content */}
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary[100] }]}>
                <Lock size={40} color={theme.colors.primary[600]} />
              </View>
              <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
                Forgot Password?
              </Text>
              <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
                No worries! Enter your email address and we'll send you a link to reset your password.
              </Text>
            </View>

            {/* Form */}
            <View style={styles.form}>
              {/* Email Input */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                  Email Address
                </Text>
                <View style={[
                  styles.inputContainer,
                  { 
                    borderColor: emailError ? theme.colors.error[500] : theme.colors.gray[300],
                    backgroundColor: theme.colors.gray[50]
                  }
                ]}>
                  <Mail size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                  <TextInput
                    style={[styles.input, { color: theme.colors.gray[900] }]}
                    placeholder="Enter your email address"
                    placeholderTextColor={theme.colors.gray[500]}
                    value={email}
                    onChangeText={(text) => {
                      setEmail(text)
                      if (emailError) setEmailError('')
                    }}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!isLoading}
                  />
                </View>
                {emailError ? (
                  <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                    {emailError}
                  </Text>
                ) : null}
              </View>

              {/* Send Reset Email Button */}
              <TouchableOpacity
                style={[
                  styles.resetButton,
                  { 
                    backgroundColor: isLoading ? theme.colors.gray[400] : theme.colors.primary[600],
                    opacity: isLoading ? 0.7 : 1
                  }
                ]}
                onPress={handleSendResetEmail}
                disabled={isLoading}
              >
                <Text style={[styles.resetButtonText, { color: theme.colors.white }]}>
                  {isLoading ? 'Sending...' : 'Send Reset Link'}
                </Text>
              </TouchableOpacity>

              {/* Error Message */}
              {error && (
                <View style={[styles.errorContainer, { backgroundColor: theme.colors.error[50] }]}>
                  <Text style={[styles.errorMessage, { color: theme.colors.error[700] }]}>
                    {error}
                  </Text>
                </View>
              )}
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={[styles.footerText, { color: theme.colors.gray[600] }]}>
                Remember your password?{' '}
              </Text>
              <TouchableOpacity
                onPress={handleBackToLogin}
                disabled={isLoading}
              >
                <Text style={[styles.footerLink, { color: theme.colors.primary[600] }]}>
                  Sign In
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  headerWithBack: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  emailText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  errorText: {
    fontSize: 14,
    marginTop: 6,
  },
  resetButton: {
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  resetButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  errorContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
  },
  instructions: {
    marginBottom: 40,
  },
  instructionText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 12,
  },
  actions: {
    gap: 16,
  },
  primaryButton: {
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButton: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 16,
  },
  footerLink: {
    fontSize: 16,
    fontWeight: '600',
  },
})
