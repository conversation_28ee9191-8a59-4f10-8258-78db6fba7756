/**
 * Runner App Authentication Navigator
 * HVPPYPlug+ Runner Authentication Flow
 */

import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {
  WelcomeScreen,
  PhoneInputScreen,
  OTPVerificationScreen,
  UserRegistrationScreen,
  LoginScreen,
  PasswordResetScreen,
  AuthStackParamList,
  UserRole
} from '@hvppyplug/ui-components-v2/auth'

const Stack = createNativeStackNavigator<AuthStackParamList>()

interface AuthNavigatorProps {
  onAuthComplete: (user: any) => void
}

export const AuthNavigator: React.FC<AuthNavigatorProps> = ({ onAuthComplete }) => {
  const runnerRole: UserRole = 'runner'
  
  // Runner app branding colors
  const primaryColor = '#45B7D1'
  const secondaryColor = '#6BC5D8'

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right'
      }}
      initialRouteName="Welcome"
    >
      <Stack.Screen name="Welcome">
        {({ navigation }) => (
          <WelcomeScreen
            onRoleSelect={(role) => {
              // For runner app, always use runner role
              navigation.navigate('PhoneInput', { role: runnerRole })
            }}
            onSkip={() => {
              navigation.navigate('PhoneInput', { role: runnerRole })
            }}
            appName="HVPPYPlug+ Runner"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PhoneInput">
        {({ navigation, route }) => (
          <PhoneInputScreen
            onPhoneSubmit={(phone) => {
              navigation.navigate('OTPVerification', { 
                phone, 
                type: 'verification' 
              })
            }}
            onBack={() => navigation.goBack()}
            role={runnerRole}
            title="Join Our Delivery Team"
            subtitle="Enter your phone number to start earning with HVPPYPlug+"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="OTPVerification">
        {({ navigation, route }) => (
          <OTPVerificationScreen
            phone={route.params.phone}
            type={route.params.type}
            onVerificationSuccess={(phone, code) => {
              // Check if user exists or needs registration
              navigation.navigate('Registration', { 
                phone, 
                role: runnerRole 
              })
            }}
            onBack={() => navigation.goBack()}
            title="Verify Your Phone"
            subtitle="Enter the 6-digit code sent to your phone"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Registration">
        {({ navigation, route }) => (
          <UserRegistrationScreen
            phone={route.params.phone}
            role={route.params.role}
            onRegistrationSuccess={(user) => {
              // Show success message for runner registration
              // Runners need verification before they can start delivering
              onAuthComplete(user)
            }}
            onBack={() => navigation.goBack()}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Login">
        {({ navigation, route }) => (
          <LoginScreen
            onLoginSuccess={(user) => {
              onAuthComplete(user)
            }}
            onForgotPassword={(phone) => {
              navigation.navigate('PasswordReset', { phone })
            }}
            onSignUp={() => {
              navigation.navigate('PhoneInput', { role: runnerRole })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PasswordReset">
        {({ navigation, route }) => (
          <PasswordResetScreen
            onResetSuccess={() => {
              navigation.navigate('Login', { phone: route.params?.phone })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
}

export default AuthNavigator
