import React, { useState, useRef } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  FlatList,
  Image,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Truck, MapPin, DollarSign, Clock } from 'lucide-react-native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'

import type { RootStackParamList } from '../../types/navigation'
import { useAuthStore } from '../../stores/authStore'
import { useTheme } from '../../providers/ThemeProvider'

type Props = NativeStackScreenProps<RootStackParamList, 'Onboarding'>

const { width } = Dimensions.get('window')

interface OnboardingSlide {
  id: string
  title: string
  subtitle: string
  description: string
  icon: React.ReactNode
  color: string
}

export function OnboardingScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { updateOnboardingStatus } = useAuthStore()
  const [currentIndex, setCurrentIndex] = useState(0)
  const flatListRef = useRef<FlatList>(null)

  const slides: OnboardingSlide[] = [
    {
      id: '1',
      title: 'Welcome to HVPPYPlug+',
      subtitle: 'Runner App',
      description: 'Join Soweto\'s premier delivery network and start earning money by delivering food and essentials to your community.',
      icon: <Truck size={64} color={theme.colors.white} />,
      color: theme.colors.primary[600],
    },
    {
      id: '2',
      title: 'Flexible Delivery',
      subtitle: 'Work on Your Schedule',
      description: 'Choose when and where you want to work. Whether it\'s part-time or full-time, you\'re in control of your earning potential.',
      icon: <Clock size={64} color={theme.colors.white} />,
      color: theme.colors.secondary[600],
    },
    {
      id: '3',
      title: 'Local Routes',
      subtitle: 'Know Your Community',
      description: 'Deliver within Soweto using routes you know. Help your neighbors get what they need while earning in your own backyard.',
      icon: <MapPin size={64} color={theme.colors.white} />,
      color: theme.colors.success[600],
    },
    {
      id: '4',
      title: 'Earn More',
      subtitle: 'Competitive Rates',
      description: 'Get paid fairly for every delivery with competitive rates, tips, and bonuses. Track your earnings in real-time.',
      icon: <DollarSign size={64} color={theme.colors.white} />,
      color: theme.colors.warning[600],
    },
  ]

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1
      setCurrentIndex(nextIndex)
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true })
    } else {
      handleGetStarted()
    }
  }

  const handleSkip = () => {
    handleGetStarted()
  }

  const handleGetStarted = () => {
    updateOnboardingStatus(true)
    navigation.replace('Login')
  }

  const renderSlide = ({ item, index }: { item: OnboardingSlide; index: number }) => (
    <View style={[styles.slide, { width }]}>
      <View style={styles.slideContent}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
          {item.icon}
        </View>

        {/* Content */}
        <View style={styles.textContent}>
          <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
            {item.title}
          </Text>
          <Text style={[styles.subtitle, { color: item.color }]}>
            {item.subtitle}
          </Text>
          <Text style={[styles.description, { color: theme.colors.gray[600] }]}>
            {item.description}
          </Text>
        </View>
      </View>
    </View>
  )

  const renderPagination = () => (
    <View style={styles.pagination}>
      {slides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            {
              backgroundColor: index === currentIndex 
                ? theme.colors.primary[600] 
                : theme.colors.gray[300],
              width: index === currentIndex ? 24 : 8,
            }
          ]}
        />
      ))}
    </View>
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.white }]}>
      {/* Skip Button */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={[styles.skipText, { color: theme.colors.gray[600] }]}>
            Skip
          </Text>
        </TouchableOpacity>
      </View>

      {/* Slides */}
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderSlide}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / width)
          setCurrentIndex(index)
        }}
        scrollEventThrottle={16}
      />

      {/* Footer */}
      <View style={styles.footer}>
        {/* Pagination */}
        {renderPagination()}

        {/* Action Buttons */}
        <View style={styles.actions}>
          {currentIndex < slides.length - 1 ? (
            <TouchableOpacity
              style={[styles.nextButton, { backgroundColor: theme.colors.primary[600] }]}
              onPress={handleNext}
            >
              <Text style={[styles.nextButtonText, { color: theme.colors.white }]}>
                Next
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.getStartedButton, { backgroundColor: theme.colors.primary[600] }]}
              onPress={handleGetStarted}
            >
              <Text style={[styles.getStartedButtonText, { color: theme.colors.white }]}>
                Get Started
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  skipButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '600',
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  textContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: 300,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    gap: 8,
  },
  paginationDot: {
    height: 8,
    borderRadius: 4,
    transition: 'all 0.3s ease',
  },
  actions: {
    alignItems: 'center',
  },
  nextButton: {
    width: '100%',
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  getStartedButton: {
    width: '100%',
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  getStartedButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
})
