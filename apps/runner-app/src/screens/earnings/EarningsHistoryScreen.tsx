import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Filter,
  Download,
  Clock,
  MapPin,
  Package
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useEarningsStore } from '../../stores/earningsStore'
import { Card, Button, StatusBadge } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { EarningsStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<EarningsStackParamList, 'EarningsHistory'>

interface EarningRecord {
  id: string
  orderId: string
  customerName: string
  amount: number
  tip: number
  bonus: number
  total: number
  date: string
  duration: number // in minutes
  distance: number // in km
  pickupAddress: string
  deliveryAddress: string
  status: 'completed' | 'cancelled' | 'disputed'
}

type FilterPeriod = 'today' | 'week' | 'month' | 'custom'

const { width } = Dimensions.get('window')

export function EarningsHistoryScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { 
    earningsHistory, 
    loading, 
    fetchEarningsHistory, 
    exportEarnings 
  } = useEarningsStore()

  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('week')
  const [refreshing, setRefreshing] = useState(false)
  const [earnings, setEarnings] = useState<EarningRecord[]>([])

  // Mock data - replace with real data from store
  const mockEarnings: EarningRecord[] = [
    {
      id: '1',
      orderId: 'ORD001',
      customerName: 'John Doe',
      amount: 25.00,
      tip: 5.00,
      bonus: 0,
      total: 30.00,
      date: '2024-01-15T14:30:00Z',
      duration: 25,
      distance: 3.2,
      pickupAddress: 'KFC Maponya Mall',
      deliveryAddress: '123 Vilakazi Street, Orlando West',
      status: 'completed'
    },
    {
      id: '2',
      orderId: 'ORD002',
      customerName: 'Sarah Smith',
      amount: 18.50,
      tip: 2.50,
      bonus: 5.00,
      total: 26.00,
      date: '2024-01-15T12:15:00Z',
      duration: 18,
      distance: 2.1,
      pickupAddress: 'Nandos Soweto',
      deliveryAddress: '456 Khumalo Street, Diepkloof',
      status: 'completed'
    },
    {
      id: '3',
      orderId: 'ORD003',
      customerName: 'Mike Johnson',
      amount: 32.00,
      tip: 8.00,
      bonus: 0,
      total: 40.00,
      date: '2024-01-14T19:45:00Z',
      duration: 35,
      distance: 4.8,
      pickupAddress: 'Steers Protea Glen',
      deliveryAddress: '789 Mandela Avenue, Soweto',
      status: 'completed'
    }
  ]

  useEffect(() => {
    setEarnings(mockEarnings)
    // fetchEarningsHistory(selectedPeriod)
  }, [selectedPeriod])

  const handleRefresh = async () => {
    setRefreshing(true)
    // await fetchEarningsHistory(selectedPeriod)
    setRefreshing(false)
  }

  const handleExport = async () => {
    try {
      await exportEarnings(selectedPeriod)
    } catch (error) {
      console.error('Failed to export earnings:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const getTotalEarnings = () => {
    return earnings.reduce((total, earning) => total + earning.total, 0)
  }

  const getAverageEarning = () => {
    return earnings.length > 0 ? getTotalEarnings() / earnings.length : 0
  }

  const getTotalDeliveries = () => {
    return earnings.filter(e => e.status === 'completed').length
  }

  const periods = [
    { key: 'today' as FilterPeriod, label: 'Today' },
    { key: 'week' as FilterPeriod, label: 'This Week' },
    { key: 'month' as FilterPeriod, label: 'This Month' },
    { key: 'custom' as FilterPeriod, label: 'Custom' },
  ]

  const renderPeriodSelector = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.periodSelector}
      contentContainerStyle={styles.periodSelectorContent}
    >
      {periods.map((period) => (
        <TouchableOpacity
          key={period.key}
          style={[
            styles.periodButton,
            selectedPeriod === period.key && {
              backgroundColor: theme.colors.primary[600]
            }
          ]}
          onPress={() => setSelectedPeriod(period.key)}
        >
          <Text style={[
            styles.periodButtonText,
            selectedPeriod === period.key 
              ? { color: 'white' }
              : { color: theme.colors.gray[600] }
          ]}>
            {period.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  )

  const renderSummaryCards = () => (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryRow}>
        <Card variant="elevated" size="sm" style={styles.summaryCard}>
          <View style={styles.summaryCardContent}>
            <DollarSign size={24} color={theme.colors.success[600]} />
            <Text style={[styles.summaryAmount, { color: theme.colors.gray[900] }]}>
              R{getTotalEarnings().toFixed(2)}
            </Text>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Total Earned
            </Text>
          </View>
        </Card>

        <Card variant="elevated" size="sm" style={styles.summaryCard}>
          <View style={styles.summaryCardContent}>
            <Package size={24} color={theme.colors.primary[600]} />
            <Text style={[styles.summaryAmount, { color: theme.colors.gray[900] }]}>
              {getTotalDeliveries()}
            </Text>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Deliveries
            </Text>
          </View>
        </Card>
      </View>

      <View style={styles.summaryRow}>
        <Card variant="elevated" size="sm" style={styles.summaryCard}>
          <View style={styles.summaryCardContent}>
            <TrendingUp size={24} color={theme.colors.blue[600]} />
            <Text style={[styles.summaryAmount, { color: theme.colors.gray[900] }]}>
              R{getAverageEarning().toFixed(2)}
            </Text>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Avg per Order
            </Text>
          </View>
        </Card>

        <Card variant="elevated" size="sm" style={styles.summaryCard}>
          <View style={styles.summaryCardContent}>
            <Clock size={24} color={theme.colors.orange[600]} />
            <Text style={[styles.summaryAmount, { color: theme.colors.gray[900] }]}>
              {Math.round(earnings.reduce((total, e) => total + e.duration, 0) / 60)}h
            </Text>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Total Hours
            </Text>
          </View>
        </Card>
      </View>
    </View>
  )

  const renderEarningItem = (earning: EarningRecord) => (
    <Card key={earning.id} variant="elevated" size="md" style={styles.earningCard}>
      <View style={styles.earningHeader}>
        <View style={styles.earningInfo}>
          <Text style={[styles.earningDate, { color: theme.colors.gray[600] }]}>
            {formatDate(earning.date)}
          </Text>
          <Text style={[styles.earningOrderId, { color: theme.colors.gray[900] }]}>
            #{earning.orderId}
          </Text>
        </View>
        <View style={styles.earningAmount}>
          <Text style={[styles.earningTotal, { color: theme.colors.success[600] }]}>
            +R{earning.total.toFixed(2)}
          </Text>
          <StatusBadge status={earning.status} size="sm" />
        </View>
      </View>

      <View style={styles.earningDetails}>
        <Text style={[styles.customerName, { color: theme.colors.gray[900] }]}>
          {earning.customerName}
        </Text>
        
        <View style={styles.addressInfo}>
          <View style={styles.addressRow}>
            <MapPin size={14} color={theme.colors.gray[500]} />
            <Text style={[styles.addressText, { color: theme.colors.gray[600] }]} numberOfLines={1}>
              {earning.pickupAddress}
            </Text>
          </View>
          <View style={styles.addressRow}>
            <MapPin size={14} color={theme.colors.success[500]} />
            <Text style={[styles.addressText, { color: theme.colors.gray[600] }]} numberOfLines={1}>
              {earning.deliveryAddress}
            </Text>
          </View>
        </View>

        <View style={styles.earningBreakdown}>
          <View style={styles.breakdownRow}>
            <Text style={[styles.breakdownLabel, { color: theme.colors.gray[600] }]}>
              Base Fee:
            </Text>
            <Text style={[styles.breakdownValue, { color: theme.colors.gray[900] }]}>
              R{earning.amount.toFixed(2)}
            </Text>
          </View>
          
          {earning.tip > 0 && (
            <View style={styles.breakdownRow}>
              <Text style={[styles.breakdownLabel, { color: theme.colors.gray[600] }]}>
                Tip:
              </Text>
              <Text style={[styles.breakdownValue, { color: theme.colors.success[600] }]}>
                +R{earning.tip.toFixed(2)}
              </Text>
            </View>
          )}
          
          {earning.bonus > 0 && (
            <View style={styles.breakdownRow}>
              <Text style={[styles.breakdownLabel, { color: theme.colors.gray[600] }]}>
                Bonus:
              </Text>
              <Text style={[styles.breakdownValue, { color: theme.colors.blue[600] }]}>
                +R{earning.bonus.toFixed(2)}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.tripStats}>
          <Text style={[styles.tripStat, { color: theme.colors.gray[500] }]}>
            {formatDuration(earning.duration)} • {earning.distance}km
          </Text>
        </View>
      </View>
    </Card>
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Earnings History
        </Text>
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: theme.colors.primary[600] }]}
          onPress={handleExport}
        >
          <Download size={16} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary[600]}
          />
        }
      >
        {/* Period Selector */}
        {renderPeriodSelector()}

        {/* Summary Cards */}
        {renderSummaryCards()}

        {/* Earnings List */}
        <View style={styles.earningsList}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Recent Earnings
          </Text>
          {earnings.map(renderEarningItem)}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  exportButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  periodSelector: {
    paddingVertical: 16,
  },
  periodSelectorContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  summaryCard: {
    flex: 1,
  },
  summaryCardContent: {
    alignItems: 'center',
    gap: 8,
  },
  summaryAmount: {
    fontSize: 20,
    fontWeight: '700',
  },
  summaryLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  earningsList: {
    paddingBottom: 32,
  },
  earningCard: {
    marginHorizontal: 20,
    marginBottom: 12,
  },
  earningHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  earningInfo: {
    flex: 1,
  },
  earningDate: {
    fontSize: 12,
  },
  earningOrderId: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 2,
  },
  earningAmount: {
    alignItems: 'flex-end',
    gap: 4,
  },
  earningTotal: {
    fontSize: 16,
    fontWeight: '700',
  },
  earningDetails: {
    gap: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '500',
  },
  addressInfo: {
    gap: 4,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  addressText: {
    fontSize: 12,
    flex: 1,
  },
  earningBreakdown: {
    gap: 4,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  breakdownLabel: {
    fontSize: 12,
  },
  breakdownValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  tripStats: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  tripStat: {
    fontSize: 12,
  },
})
