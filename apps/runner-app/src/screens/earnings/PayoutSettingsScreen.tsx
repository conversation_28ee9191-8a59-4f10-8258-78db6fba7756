import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  TouchableOpacity
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  CreditCard,
  DollarSign,
  Shield,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  Calendar,
  Clock
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useEarningsStore } from '../../stores/earningsStore'
import { Card, Button, LoadingSpinner } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { EarningsStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<EarningsStackParamList, 'PayoutSettings'>

interface PayoutInfo {
  bankName: string
  accountNumber: string
  accountHolder: string
  branchCode: string
  accountType: 'savings' | 'current'
}

const SOUTH_AFRICAN_BANKS = [
  'ABSA Bank',
  'Standard Bank',
  'First National Bank (FNB)',
  'Nedbank',
  'Capitec Bank',
  'African Bank',
  'Bidvest Bank',
  'Discovery Bank',
  'TymeBank',
  'Bank Zero'
]

export function PayoutSettingsScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { 
    payoutInfo, 
    loading, 
    error, 
    updatePayoutInfo, 
    requestPayout,
    clearError 
  } = useEarningsStore()

  const [formData, setFormData] = useState<PayoutInfo>({
    bankName: '',
    accountNumber: '',
    accountHolder: '',
    branchCode: '',
    accountType: 'savings'
  })
  const [showBankPicker, setShowBankPicker] = useState(false)
  const [availableBalance] = useState(1250.75) // Mock available balance
  const [pendingPayouts] = useState([
    {
      id: '1',
      amount: 500.00,
      status: 'processing',
      requestedAt: '2024-01-15T10:30:00Z',
      estimatedArrival: '2024-01-17T00:00:00Z'
    }
  ])

  useEffect(() => {
    if (payoutInfo) {
      setFormData(payoutInfo)
    }
  }, [payoutInfo])

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError }
      ])
    }
  }, [error])

  const handleSave = async () => {
    // Validate form
    if (!formData.bankName || !formData.accountNumber || !formData.accountHolder || !formData.branchCode) {
      Alert.alert('Validation Error', 'Please fill in all required fields.')
      return
    }

    // Validate account number (basic validation)
    if (formData.accountNumber.length < 8 || formData.accountNumber.length > 12) {
      Alert.alert('Validation Error', 'Please enter a valid account number.')
      return
    }

    // Validate branch code (6 digits for SA banks)
    if (!/^\d{6}$/.test(formData.branchCode)) {
      Alert.alert('Validation Error', 'Please enter a valid 6-digit branch code.')
      return
    }

    try {
      await updatePayoutInfo(formData)
      Alert.alert('Success', 'Payout information updated successfully!')
    } catch (error) {
      // Error handled by store
    }
  }

  const handleRequestPayout = () => {
    if (!payoutInfo) {
      Alert.alert('Setup Required', 'Please set up your payout information first.')
      return
    }

    if (availableBalance < 100) {
      Alert.alert('Minimum Amount', 'Minimum payout amount is R100.00')
      return
    }

    Alert.alert(
      'Request Payout',
      `Request payout of R${availableBalance.toFixed(2)} to your bank account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Request', 
          onPress: async () => {
            try {
              await requestPayout(availableBalance)
              Alert.alert('Success', 'Payout request submitted successfully!')
            } catch (error) {
              // Error handled by store
            }
          }
        }
      ]
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing': return theme.colors.blue[600]
      case 'completed': return theme.colors.success[600]
      case 'failed': return theme.colors.error[600]
      default: return theme.colors.gray[600]
    }
  }

  const renderBankPicker = () => (
    <View style={styles.bankPicker}>
      <ScrollView style={styles.bankList} showsVerticalScrollIndicator={false}>
        {SOUTH_AFRICAN_BANKS.map((bank) => (
          <TouchableOpacity
            key={bank}
            style={[styles.bankOption, { backgroundColor: theme.colors.white }]}
            onPress={() => {
              setFormData({ ...formData, bankName: bank })
              setShowBankPicker(false)
            }}
          >
            <Text style={[styles.bankOptionText, { color: theme.colors.gray[900] }]}>
              {bank}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <LoadingSpinner message="Updating payout settings..." />
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Payout Settings
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Available Balance */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.balanceHeader}>
            <DollarSign size={24} color={theme.colors.success[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Available Balance
            </Text>
          </View>
          
          <Text style={[styles.balanceAmount, { color: theme.colors.success[600] }]}>
            R{availableBalance.toFixed(2)}
          </Text>
          
          <Button
            title="Request Payout"
            variant="primary"
            size="lg"
            onPress={handleRequestPayout}
            disabled={availableBalance < 100}
            style={{ marginTop: 16 }}
          />
          
          <Text style={[styles.balanceNote, { color: theme.colors.gray[600] }]}>
            Minimum payout amount: R100.00
          </Text>
        </Card>

        {/* Pending Payouts */}
        {pendingPayouts.length > 0 && (
          <Card variant="elevated" size="md" style={styles.section}>
            <View style={styles.sectionHeader}>
              <Clock size={20} color={theme.colors.blue[600]} />
              <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
                Pending Payouts
              </Text>
            </View>
            
            {pendingPayouts.map((payout) => (
              <View key={payout.id} style={styles.payoutItem}>
                <View style={styles.payoutInfo}>
                  <Text style={[styles.payoutAmount, { color: theme.colors.gray[900] }]}>
                    R{payout.amount.toFixed(2)}
                  </Text>
                  <Text style={[styles.payoutDate, { color: theme.colors.gray[600] }]}>
                    Requested: {formatDate(payout.requestedAt)}
                  </Text>
                  <Text style={[styles.payoutEta, { color: theme.colors.gray[600] }]}>
                    ETA: {formatDate(payout.estimatedArrival)}
                  </Text>
                </View>
                <View style={[styles.payoutStatus, { backgroundColor: getStatusColor(payout.status) + '20' }]}>
                  <Text style={[styles.payoutStatusText, { color: getStatusColor(payout.status) }]}>
                    {payout.status.toUpperCase()}
                  </Text>
                </View>
              </View>
            ))}
          </Card>
        )}

        {/* Bank Account Information */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <CreditCard size={20} color={theme.colors.primary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Bank Account Information
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Bank Name *
            </Text>
            <TouchableOpacity
              style={[styles.input, { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[300] }]}
              onPress={() => setShowBankPicker(true)}
            >
              <Text style={[styles.inputText, { color: formData.bankName ? theme.colors.gray[900] : theme.colors.gray[500] }]}>
                {formData.bankName || 'Select your bank'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Account Holder Name *
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[300], color: theme.colors.gray[900] }]}
              placeholder="Full name as on bank account"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.accountHolder}
              onChangeText={(text) => setFormData({ ...formData, accountHolder: text })}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Account Number *
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[300], color: theme.colors.gray[900] }]}
              placeholder="Enter account number"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.accountNumber}
              onChangeText={(text) => setFormData({ ...formData, accountNumber: text.replace(/\D/g, '') })}
              keyboardType="numeric"
              maxLength={12}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Branch Code *
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[300], color: theme.colors.gray[900] }]}
              placeholder="6-digit branch code"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.branchCode}
              onChangeText={(text) => setFormData({ ...formData, branchCode: text.replace(/\D/g, '') })}
              keyboardType="numeric"
              maxLength={6}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Account Type *
            </Text>
            <View style={styles.accountTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.accountTypeButton,
                  formData.accountType === 'savings' && { backgroundColor: theme.colors.primary[600] }
                ]}
                onPress={() => setFormData({ ...formData, accountType: 'savings' })}
              >
                <Text style={[
                  styles.accountTypeText,
                  formData.accountType === 'savings' 
                    ? { color: 'white' }
                    : { color: theme.colors.gray[600] }
                ]}>
                  Savings
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.accountTypeButton,
                  formData.accountType === 'current' && { backgroundColor: theme.colors.primary[600] }
                ]}
                onPress={() => setFormData({ ...formData, accountType: 'current' })}
              >
                <Text style={[
                  styles.accountTypeText,
                  formData.accountType === 'current' 
                    ? { color: 'white' }
                    : { color: theme.colors.gray[600] }
                ]}>
                  Current
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <Button
            title="Save Bank Details"
            variant="primary"
            size="lg"
            onPress={handleSave}
            style={{ marginTop: 24 }}
            icon={<CheckCircle />}
          />
        </Card>

        {/* Security Notice */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.securityHeader}>
            <Shield size={20} color={theme.colors.blue[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Security & Privacy
            </Text>
          </View>
          
          <Text style={[styles.securityText, { color: theme.colors.gray[600] }]}>
            Your banking information is encrypted and stored securely. We never store your full account details and use bank-grade security measures to protect your data.
          </Text>
          
          <View style={styles.securityFeatures}>
            <View style={styles.securityFeature}>
              <CheckCircle size={16} color={theme.colors.success[600]} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.gray[600] }]}>
                256-bit SSL encryption
              </Text>
            </View>
            <View style={styles.securityFeature}>
              <CheckCircle size={16} color={theme.colors.success[600]} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.gray[600] }]}>
                PCI DSS compliant
              </Text>
            </View>
            <View style={styles.securityFeature}>
              <CheckCircle size={16} color={theme.colors.success[600]} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.gray[600] }]}>
                Regular security audits
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>

      {/* Bank Picker Modal */}
      {showBankPicker && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.white }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.gray[900] }]}>
                Select Your Bank
              </Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowBankPicker(false)}
              >
                <Text style={[styles.modalCloseText, { color: theme.colors.primary[600] }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
            {renderBankPicker()}
          </View>
        </View>
      )}
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 8,
  },
  balanceNote: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  payoutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  payoutInfo: {
    flex: 1,
  },
  payoutAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  payoutDate: {
    fontSize: 12,
    marginTop: 2,
  },
  payoutEta: {
    fontSize: 12,
    marginTop: 2,
  },
  payoutStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  payoutStatusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputText: {
    fontSize: 16,
  },
  accountTypeContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  accountTypeButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  accountTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  securityFeatures: {
    gap: 8,
  },
  securityFeature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  securityFeatureText: {
    fontSize: 14,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    borderRadius: 12,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalCloseText: {
    fontSize: 16,
    fontWeight: '500',
  },
  bankPicker: {
    maxHeight: 300,
  },
  bankList: {
    flex: 1,
  },
  bankOption: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  bankOptionText: {
    fontSize: 16,
  },
})
