import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Image
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import * as ImagePicker from 'expo-image-picker'
import {
  Car,
  Bike,
  Truck,
  Camera,
  ArrowLeft,
  Save,
  CheckCircle,
  AlertCircle,
  Calendar,
  FileText
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'
import { Card, Button, LoadingSpinner } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { ProfileStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<ProfileStackParamList, 'VehicleInfo'>

type VehicleType = 'bicycle' | 'motorcycle' | 'car' | 'scooter'

interface VehicleInfo {
  type: VehicleType
  make: string
  model: string
  year: string
  color: string
  licensePlate: string
  registrationNumber: string
  insuranceProvider: string
  insuranceExpiryDate: string
  licenseExpiryDate: string
  photos: {
    front?: string
    back?: string
    side?: string
    interior?: string
  }
}

const VEHICLE_TYPES = [
  { type: 'bicycle' as VehicleType, name: 'Bicycle', icon: Bike, description: 'Eco-friendly delivery' },
  { type: 'motorcycle' as VehicleType, name: 'Motorcycle', icon: Bike, description: 'Fast urban delivery' },
  { type: 'scooter' as VehicleType, name: 'Scooter', icon: Bike, description: 'Efficient city transport' },
  { type: 'car' as VehicleType, name: 'Car', icon: Car, description: 'All-weather delivery' }
]

const VEHICLE_MAKES = {
  bicycle: ['Trek', 'Giant', 'Specialized', 'Cannondale', 'Scott', 'Other'],
  motorcycle: ['Honda', 'Yamaha', 'Suzuki', 'Kawasaki', 'BMW', 'Harley-Davidson', 'Other'],
  scooter: ['Vespa', 'Honda', 'Yamaha', 'Piaggio', 'Kymco', 'Other'],
  car: ['Toyota', 'Volkswagen', 'Ford', 'Hyundai', 'Nissan', 'BMW', 'Mercedes-Benz', 'Audi', 'Other']
}

const PHOTO_TYPES = [
  { key: 'front' as keyof VehicleInfo['photos'], name: 'Front View', required: true },
  { key: 'back' as keyof VehicleInfo['photos'], name: 'Back View', required: true },
  { key: 'side' as keyof VehicleInfo['photos'], name: 'Side View', required: false },
  { key: 'interior' as keyof VehicleInfo['photos'], name: 'Interior', required: false }
]

export function VehicleInfoScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { runner, updateVehicleInfo, loading } = useAuthStore()

  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo>({
    type: 'bicycle',
    make: '',
    model: '',
    year: '',
    color: '',
    licensePlate: '',
    registrationNumber: '',
    insuranceProvider: '',
    insuranceExpiryDate: '',
    licenseExpiryDate: '',
    photos: {}
  })
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    if (runner?.vehicleDetails) {
      setVehicleInfo({
        type: runner.vehicleType || 'bicycle',
        make: runner.vehicleDetails.make || '',
        model: runner.vehicleDetails.model || '',
        year: runner.vehicleDetails.year || '',
        color: runner.vehicleDetails.color || '',
        licensePlate: runner.vehicleDetails.licensePlate || '',
        registrationNumber: runner.vehicleDetails.registrationNumber || '',
        insuranceProvider: runner.vehicleDetails.insuranceProvider || '',
        insuranceExpiryDate: runner.vehicleDetails.insuranceExpiryDate || '',
        licenseExpiryDate: runner.vehicleDetails.licenseExpiryDate || '',
        photos: runner.vehicleDetails.photos || {}
      })
    }
  }, [runner])

  const handleInputChange = (field: keyof VehicleInfo, value: string) => {
    setVehicleInfo(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const handleVehicleTypeChange = (type: VehicleType) => {
    setVehicleInfo(prev => ({
      ...prev,
      type,
      make: '', // Reset make when type changes
      model: ''  // Reset model when type changes
    }))
    setHasChanges(true)
  }

  const handlePhotoUpload = async (photoType: keyof VehicleInfo['photos']) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Camera access is required to take vehicle photos.')
      return
    }

    Alert.alert(
      'Add Vehicle Photo',
      `Take a photo of your vehicle's ${photoType} view`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => openCamera(photoType) },
        { text: 'Gallery', onPress: () => openGallery(photoType) }
      ]
    )
  }

  const openCamera = async (photoType: keyof VehicleInfo['photos']) => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      setVehicleInfo(prev => ({
        ...prev,
        photos: {
          ...prev.photos,
          [photoType]: result.assets[0].uri
        }
      }))
      setHasChanges(true)
    }
  }

  const openGallery = async (photoType: keyof VehicleInfo['photos']) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Gallery access is required to select photos.')
      return
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      setVehicleInfo(prev => ({
        ...prev,
        photos: {
          ...prev.photos,
          [photoType]: result.assets[0].uri
        }
      }))
      setHasChanges(true)
    }
  }

  const validateForm = (): boolean => {
    if (!vehicleInfo.make.trim()) {
      Alert.alert('Validation Error', 'Vehicle make is required.')
      return false
    }

    if (!vehicleInfo.model.trim()) {
      Alert.alert('Validation Error', 'Vehicle model is required.')
      return false
    }

    if (!vehicleInfo.year.trim()) {
      Alert.alert('Validation Error', 'Vehicle year is required.')
      return false
    }

    if (!vehicleInfo.color.trim()) {
      Alert.alert('Validation Error', 'Vehicle color is required.')
      return false
    }

    // Validate year
    const currentYear = new Date().getFullYear()
    const year = parseInt(vehicleInfo.year)
    if (isNaN(year) || year < 1990 || year > currentYear + 1) {
      Alert.alert('Validation Error', 'Please enter a valid vehicle year.')
      return false
    }

    // Check required photos
    const requiredPhotos = PHOTO_TYPES.filter(pt => pt.required)
    const missingPhotos = requiredPhotos.filter(pt => !vehicleInfo.photos[pt.key])
    
    if (missingPhotos.length > 0) {
      Alert.alert(
        'Photos Required', 
        `Please add photos for: ${missingPhotos.map(p => p.name).join(', ')}`
      )
      return false
    }

    return true
  }

  const handleSave = async () => {
    if (!validateForm()) return

    try {
      await updateVehicleInfo(vehicleInfo)
      setHasChanges(false)
      Alert.alert('Success', 'Vehicle information updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ])
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update vehicle information')
    }
  }

  const isDateExpiringSoon = (dateString: string) => {
    if (!dateString) return false
    const date = new Date(dateString)
    const now = new Date()
    const daysUntilExpiry = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0
  }

  const isDateExpired = (dateString: string) => {
    if (!dateString) return false
    const date = new Date(dateString)
    const now = new Date()
    return date < now
  }

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <LoadingSpinner message="Updating vehicle information..." />
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Vehicle Information
        </Text>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: hasChanges ? theme.colors.primary[600] : theme.colors.gray[300] }
          ]}
          onPress={handleSave}
          disabled={!hasChanges}
        >
          <Save size={16} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Vehicle Type Selection */}
        <Card variant="elevated" size="md" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Vehicle Type
          </Text>
          
          <View style={styles.vehicleTypeGrid}>
            {VEHICLE_TYPES.map((vType) => {
              const IconComponent = vType.icon
              const isSelected = vehicleInfo.type === vType.type
              
              return (
                <TouchableOpacity
                  key={vType.type}
                  style={[
                    styles.vehicleTypeCard,
                    { 
                      backgroundColor: isSelected ? theme.colors.primary[100] : theme.colors.white,
                      borderColor: isSelected ? theme.colors.primary[600] : theme.colors.gray[300]
                    }
                  ]}
                  onPress={() => handleVehicleTypeChange(vType.type)}
                >
                  <IconComponent 
                    size={32} 
                    color={isSelected ? theme.colors.primary[600] : theme.colors.gray[600]} 
                  />
                  <Text style={[
                    styles.vehicleTypeName,
                    { color: isSelected ? theme.colors.primary[700] : theme.colors.gray[900] }
                  ]}>
                    {vType.name}
                  </Text>
                  <Text style={[
                    styles.vehicleTypeDescription,
                    { color: isSelected ? theme.colors.primary[600] : theme.colors.gray[600] }
                  ]}>
                    {vType.description}
                  </Text>
                </TouchableOpacity>
              )
            })}
          </View>
        </Card>

        {/* Vehicle Details */}
        <Card variant="elevated" size="md" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Vehicle Details
          </Text>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Make *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.colors.white, 
                  borderColor: theme.colors.gray[300],
                  color: theme.colors.gray[900]
                }]}
                placeholder="e.g., Toyota"
                placeholderTextColor={theme.colors.gray[500]}
                value={vehicleInfo.make}
                onChangeText={(text) => handleInputChange('make', text)}
              />
            </View>

            <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Model *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.colors.white, 
                  borderColor: theme.colors.gray[300],
                  color: theme.colors.gray[900]
                }]}
                placeholder="e.g., Corolla"
                placeholderTextColor={theme.colors.gray[500]}
                value={vehicleInfo.model}
                onChangeText={(text) => handleInputChange('model', text)}
              />
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Year *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.colors.white, 
                  borderColor: theme.colors.gray[300],
                  color: theme.colors.gray[900]
                }]}
                placeholder="2020"
                placeholderTextColor={theme.colors.gray[500]}
                value={vehicleInfo.year}
                onChangeText={(text) => handleInputChange('year', text)}
                keyboardType="numeric"
                maxLength={4}
              />
            </View>

            <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Color *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.colors.white, 
                  borderColor: theme.colors.gray[300],
                  color: theme.colors.gray[900]
                }]}
                placeholder="White"
                placeholderTextColor={theme.colors.gray[500]}
                value={vehicleInfo.color}
                onChangeText={(text) => handleInputChange('color', text)}
              />
            </View>
          </View>

          {vehicleInfo.type !== 'bicycle' && (
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                License Plate
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.colors.white, 
                  borderColor: theme.colors.gray[300],
                  color: theme.colors.gray[900]
                }]}
                placeholder="ABC 123 GP"
                placeholderTextColor={theme.colors.gray[500]}
                value={vehicleInfo.licensePlate}
                onChangeText={(text) => handleInputChange('licensePlate', text.toUpperCase())}
                autoCapitalize="characters"
              />
            </View>
          )}
        </Card>

        {/* Vehicle Photos */}
        <Card variant="elevated" size="md" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Vehicle Photos
          </Text>
          
          <View style={styles.photosGrid}>
            {PHOTO_TYPES.map((photoType) => (
              <View key={photoType.key} style={styles.photoContainer}>
                <Text style={[styles.photoLabel, { color: theme.colors.gray[700] }]}>
                  {photoType.name} {photoType.required && '*'}
                </Text>
                
                <TouchableOpacity
                  style={[styles.photoUpload, { borderColor: theme.colors.gray[300] }]}
                  onPress={() => handlePhotoUpload(photoType.key)}
                >
                  {vehicleInfo.photos[photoType.key] ? (
                    <Image 
                      source={{ uri: vehicleInfo.photos[photoType.key] }} 
                      style={styles.photoPreview} 
                    />
                  ) : (
                    <View style={styles.photoPlaceholder}>
                      <Camera size={24} color={theme.colors.gray[500]} />
                      <Text style={[styles.photoPlaceholderText, { color: theme.colors.gray[500] }]}>
                        Add Photo
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </Card>

        {/* Save Button */}
        <View style={styles.saveContainer}>
          <Button
            title="Save Vehicle Information"
            variant="primary"
            size="lg"
            onPress={handleSave}
            disabled={!hasChanges}
            fullWidth
            icon={<Save />}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  vehicleTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  vehicleTypeCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    gap: 8,
  },
  vehicleTypeName: {
    fontSize: 14,
    fontWeight: '600',
  },
  vehicleTypeDescription: {
    fontSize: 12,
    textAlign: 'center',
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  photoContainer: {
    flex: 1,
    minWidth: '45%',
  },
  photoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  photoUpload: {
    aspectRatio: 4/3,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  photoPreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  photoPlaceholder: {
    alignItems: 'center',
    gap: 8,
  },
  photoPlaceholderText: {
    fontSize: 12,
  },
  saveContainer: {
    marginTop: 24,
    marginBottom: 32,
  },
})
