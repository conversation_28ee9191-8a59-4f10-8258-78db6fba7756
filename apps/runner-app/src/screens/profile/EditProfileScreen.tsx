import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import * as ImagePicker from 'expo-image-picker'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Camera,
  ArrowLeft,
  Save,
  Edit3
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'
import { Card, Button, LoadingSpinner } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { ProfileStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<ProfileStackParamList, 'EditProfile'>

interface ProfileFormData {
  name: string
  email: string
  phone: string
  address: string
  emergencyContact: string
  emergencyPhone: string
  avatar?: string
}

export function EditProfileScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { runner, updateProfile, loading } = useAuthStore()

  const [formData, setFormData] = useState<ProfileFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    avatar: undefined
  })
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    if (runner) {
      setFormData({
        name: runner.name || '',
        email: runner.email || '',
        phone: runner.phone || '',
        address: runner.currentLocation?.address || '',
        emergencyContact: runner.emergencyContact || '',
        emergencyPhone: runner.emergencyPhone || '',
        avatar: runner.avatar
      })
    }
  }, [runner])

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to update your profile photo.')
      return
    }

    Alert.alert(
      'Update Profile Photo',
      'Choose an option',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: openCamera },
        { text: 'Photo Library', onPress: openImageLibrary }
      ]
    )
  }

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take a photo.')
      return
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      handleInputChange('avatar', result.assets[0].uri)
    }
  }

  const openImageLibrary = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      handleInputChange('avatar', result.assets[0].uri)
    }
  }

  const handleSave = async () => {
    // Basic validation
    if (!formData.name.trim()) {
      Alert.alert('Validation Error', 'Name is required.')
      return
    }

    if (!formData.email.trim()) {
      Alert.alert('Validation Error', 'Email is required.')
      return
    }

    if (!formData.phone.trim()) {
      Alert.alert('Validation Error', 'Phone number is required.')
      return
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address.')
      return
    }

    // Phone validation (South African format)
    const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/
    if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      Alert.alert('Validation Error', 'Please enter a valid South African phone number.')
      return
    }

    try {
      await updateProfile({
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        emergencyContact: formData.emergencyContact.trim(),
        emergencyPhone: formData.emergencyPhone.trim(),
        avatar: formData.avatar
      })

      setHasChanges(false)
      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ])
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update profile')
    }
  }

  const handleDiscard = () => {
    if (hasChanges) {
      Alert.alert(
        'Discard Changes',
        'Are you sure you want to discard your changes?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Discard', style: 'destructive', onPress: () => navigation.goBack() }
        ]
      )
    } else {
      navigation.goBack()
    }
  }

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <LoadingSpinner message="Updating profile..." />
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleDiscard}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Edit Profile
        </Text>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: hasChanges ? theme.colors.primary[600] : theme.colors.gray[300] }
          ]}
          onPress={handleSave}
          disabled={!hasChanges}
        >
          <Save size={16} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Photo */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.photoSection}>
            <View style={styles.photoContainer}>
              {formData.avatar ? (
                <Image source={{ uri: formData.avatar }} style={styles.profilePhoto} />
              ) : (
                <View style={[styles.photoPlaceholder, { backgroundColor: theme.colors.gray[200] }]}>
                  <User size={40} color={theme.colors.gray[500]} />
                </View>
              )}
              <TouchableOpacity
                style={[styles.photoEditButton, { backgroundColor: theme.colors.primary[600] }]}
                onPress={handleImagePicker}
              >
                <Camera size={16} color="white" />
              </TouchableOpacity>
            </View>
            <Text style={[styles.photoLabel, { color: theme.colors.gray[600] }]}>
              Tap to update profile photo
            </Text>
          </View>
        </Card>

        {/* Personal Information */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <User size={20} color={theme.colors.primary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Personal Information
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Full Name *
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Enter your full name"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.name}
              onChangeText={(text) => handleInputChange('name', text)}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Email Address *
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Enter your email address"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.email}
              onChangeText={(text) => handleInputChange('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Phone Number *
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Enter your phone number"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.phone}
              onChangeText={(text) => handleInputChange('phone', text)}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Address
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Enter your address"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.address}
              onChangeText={(text) => handleInputChange('address', text)}
              multiline
              numberOfLines={2}
            />
          </View>
        </Card>

        {/* Emergency Contact */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <Phone size={20} color={theme.colors.error[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Emergency Contact
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Contact Name
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Emergency contact name"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.emergencyContact}
              onChangeText={(text) => handleInputChange('emergencyContact', text)}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
              Contact Phone
            </Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: theme.colors.white, 
                borderColor: theme.colors.gray[300],
                color: theme.colors.gray[900]
              }]}
              placeholder="Emergency contact phone"
              placeholderTextColor={theme.colors.gray[500]}
              value={formData.emergencyPhone}
              onChangeText={(text) => handleInputChange('emergencyPhone', text)}
              keyboardType="phone-pad"
            />
          </View>
        </Card>

        {/* Save Button */}
        <View style={styles.saveContainer}>
          <Button
            title="Save Changes"
            variant="primary"
            size="lg"
            onPress={handleSave}
            disabled={!hasChanges}
            fullWidth
            icon={<Save />}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  photoSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  photoContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  profilePhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  photoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoEditButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  photoLabel: {
    fontSize: 14,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  saveContainer: {
    marginTop: 24,
    marginBottom: 32,
  },
})
