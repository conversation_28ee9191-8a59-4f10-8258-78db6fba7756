import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import * as ImagePicker from 'expo-image-picker'
import * as DocumentPicker from 'expo-document-picker'
import {
  FileText,
  Camera,
  Upload,
  CheckCircle,
  AlertCircle,
  Clock,
  ArrowLeft,
  Eye,
  Trash2,
  Calendar
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'
import { Card, Button, StatusBadge, LoadingSpinner } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { ProfileStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<ProfileStackParamList, 'Documents'>

interface Document {
  id: string
  type: 'drivers_license' | 'vehicle_registration' | 'id_document' | 'proof_of_residence' | 'bank_statement'
  name: string
  uri: string
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  uploadedAt: string
  expiryDate?: string
  rejectionReason?: string
}

const DOCUMENT_TYPES = [
  {
    type: 'drivers_license' as const,
    name: 'Driver\'s License',
    description: 'Valid South African driver\'s license',
    required: true,
    icon: FileText
  },
  {
    type: 'vehicle_registration' as const,
    name: 'Vehicle Registration',
    description: 'Vehicle registration certificate',
    required: true,
    icon: FileText
  },
  {
    type: 'id_document' as const,
    name: 'ID Document',
    description: 'South African ID or passport',
    required: true,
    icon: FileText
  },
  {
    type: 'proof_of_residence' as const,
    name: 'Proof of Residence',
    description: 'Utility bill or bank statement (max 3 months old)',
    required: true,
    icon: FileText
  },
  {
    type: 'bank_statement' as const,
    name: 'Bank Statement',
    description: 'Recent bank statement for verification',
    required: false,
    icon: FileText
  }
]

const { width } = Dimensions.get('window')

export function DocumentsScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { runner, uploadDocument, loading } = useAuthStore()

  const [documents, setDocuments] = useState<Document[]>([])
  const [uploading, setUploading] = useState<string | null>(null)

  // Mock documents - replace with real data
  useEffect(() => {
    const mockDocuments: Document[] = [
      {
        id: '1',
        type: 'drivers_license',
        name: 'Driver\'s License',
        uri: 'mock-uri-1',
        status: 'approved',
        uploadedAt: '2024-01-10T10:00:00Z',
        expiryDate: '2026-03-15'
      },
      {
        id: '2',
        type: 'id_document',
        name: 'ID Document',
        uri: 'mock-uri-2',
        status: 'pending',
        uploadedAt: '2024-01-15T14:30:00Z'
      }
    ]
    setDocuments(mockDocuments)
  }, [])

  const handleUploadDocument = async (documentType: Document['type']) => {
    Alert.alert(
      'Upload Document',
      'Choose upload method',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => openCamera(documentType) },
        { text: 'Gallery', onPress: () => openGallery(documentType) },
        { text: 'Files', onPress: () => openDocumentPicker(documentType) }
      ]
    )
  }

  const openCamera = async (documentType: Document['type']) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Camera access is required to take photos.')
      return
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      await uploadDocumentFile(documentType, result.assets[0].uri, 'image')
    }
  }

  const openGallery = async (documentType: Document['type']) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Gallery access is required to select photos.')
      return
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    })

    if (!result.canceled && result.assets[0]) {
      await uploadDocumentFile(documentType, result.assets[0].uri, 'image')
    }
  }

  const openDocumentPicker = async (documentType: Document['type']) => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true,
      })

      if (!result.canceled && result.assets[0]) {
        await uploadDocumentFile(documentType, result.assets[0].uri, 'document')
      }
    } catch (error) {
      console.error('Document picker error:', error)
      Alert.alert('Error', 'Failed to select document')
    }
  }

  const uploadDocumentFile = async (documentType: Document['type'], uri: string, fileType: 'image' | 'document') => {
    setUploading(documentType)
    
    try {
      // Create new document entry
      const newDocument: Document = {
        id: `doc_${Date.now()}`,
        type: documentType,
        name: DOCUMENT_TYPES.find(dt => dt.type === documentType)?.name || 'Document',
        uri,
        status: 'pending',
        uploadedAt: new Date().toISOString()
      }

      // Remove existing document of same type
      setDocuments(prev => prev.filter(doc => doc.type !== documentType))
      
      // Add new document
      setDocuments(prev => [...prev, newDocument])

      // Upload to server (mock)
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Update status to approved (mock)
      setDocuments(prev => 
        prev.map(doc => 
          doc.id === newDocument.id 
            ? { ...doc, status: 'approved' as const }
            : doc
        )
      )

      Alert.alert('Success', 'Document uploaded successfully!')
    } catch (error: any) {
      console.error('Upload error:', error)
      Alert.alert('Error', error.message || 'Failed to upload document')
      
      // Remove failed document
      setDocuments(prev => prev.filter(doc => doc.type !== documentType))
    } finally {
      setUploading(null)
    }
  }

  const handleDeleteDocument = (documentId: string) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            setDocuments(prev => prev.filter(doc => doc.id !== documentId))
          }
        }
      ]
    )
  }

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'approved': return theme.colors.success[600]
      case 'rejected': return theme.colors.error[600]
      case 'pending': return theme.colors.warning[600]
      case 'expired': return theme.colors.gray[600]
      default: return theme.colors.gray[600]
    }
  }

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'approved': return CheckCircle
      case 'rejected': return AlertCircle
      case 'pending': return Clock
      case 'expired': return AlertCircle
      default: return Clock
    }
  }

  const isDocumentExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false
    const expiry = new Date(expiryDate)
    const now = new Date()
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0
  }

  const isDocumentExpired = (expiryDate?: string) => {
    if (!expiryDate) return false
    const expiry = new Date(expiryDate)
    const now = new Date()
    return expiry < now
  }

  const renderDocumentType = (docType: typeof DOCUMENT_TYPES[0]) => {
    const existingDoc = documents.find(doc => doc.type === docType.type)
    const isUploading = uploading === docType.type
    const IconComponent = docType.icon

    return (
      <Card key={docType.type} variant="elevated" size="md" style={styles.documentCard}>
        <View style={styles.documentHeader}>
          <View style={styles.documentInfo}>
            <View style={styles.documentTitleRow}>
              <IconComponent size={20} color={theme.colors.primary[600]} />
              <Text style={[styles.documentTitle, { color: theme.colors.gray[900] }]}>
                {docType.name}
              </Text>
              {docType.required && (
                <Text style={[styles.requiredLabel, { color: theme.colors.error[600] }]}>
                  Required
                </Text>
              )}
            </View>
            <Text style={[styles.documentDescription, { color: theme.colors.gray[600] }]}>
              {docType.description}
            </Text>
          </View>
        </View>

        {existingDoc ? (
          <View style={styles.documentStatus}>
            <View style={styles.statusRow}>
              <View style={styles.statusInfo}>
                {(() => {
                  const StatusIcon = getStatusIcon(existingDoc.status)
                  return <StatusIcon size={16} color={getStatusColor(existingDoc.status)} />
                })()}
                <Text style={[styles.statusText, { color: getStatusColor(existingDoc.status) }]}>
                  {existingDoc.status.toUpperCase()}
                </Text>
              </View>
              
              <View style={styles.documentActions}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.colors.blue[100] }]}
                  onPress={() => {/* View document */}}
                >
                  <Eye size={16} color={theme.colors.blue[600]} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.colors.error[100] }]}
                  onPress={() => handleDeleteDocument(existingDoc.id)}
                >
                  <Trash2 size={16} color={theme.colors.error[600]} />
                </TouchableOpacity>
              </View>
            </View>

            {existingDoc.expiryDate && (
              <View style={styles.expiryInfo}>
                <Calendar size={14} color={theme.colors.gray[500]} />
                <Text style={[
                  styles.expiryText,
                  { 
                    color: isDocumentExpired(existingDoc.expiryDate)
                      ? theme.colors.error[600]
                      : isDocumentExpiringSoon(existingDoc.expiryDate)
                      ? theme.colors.warning[600]
                      : theme.colors.gray[600]
                  }
                ]}>
                  Expires: {new Date(existingDoc.expiryDate).toLocaleDateString()}
                </Text>
              </View>
            )}

            {existingDoc.rejectionReason && (
              <View style={[styles.rejectionReason, { backgroundColor: theme.colors.error[50] }]}>
                <AlertCircle size={14} color={theme.colors.error[600]} />
                <Text style={[styles.rejectionText, { color: theme.colors.error[700] }]}>
                  {existingDoc.rejectionReason}
                </Text>
              </View>
            )}

            <Button
              title="Replace Document"
              variant="outline"
              size="sm"
              onPress={() => handleUploadDocument(docType.type)}
              style={{ marginTop: 12 }}
            />
          </View>
        ) : (
          <View style={styles.uploadSection}>
            {isUploading ? (
              <View style={styles.uploadingState}>
                <LoadingSpinner size="small" />
                <Text style={[styles.uploadingText, { color: theme.colors.gray[600] }]}>
                  Uploading...
                </Text>
              </View>
            ) : (
              <Button
                title="Upload Document"
                variant="primary"
                size="sm"
                onPress={() => handleUploadDocument(docType.type)}
                icon={<Upload />}
              />
            )}
          </View>
        )}
      </Card>
    )
  }

  const getCompletionPercentage = () => {
    const requiredDocs = DOCUMENT_TYPES.filter(dt => dt.required)
    const approvedRequiredDocs = requiredDocs.filter(dt => 
      documents.find(doc => doc.type === dt.type && doc.status === 'approved')
    )
    return Math.round((approvedRequiredDocs.length / requiredDocs.length) * 100)
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Documents
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Completion Status */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.completionHeader}>
            <Text style={[styles.completionTitle, { color: theme.colors.gray[900] }]}>
              Document Verification
            </Text>
            <Text style={[styles.completionPercentage, { color: theme.colors.primary[600] }]}>
              {getCompletionPercentage()}%
            </Text>
          </View>
          
          <View style={[styles.progressBar, { backgroundColor: theme.colors.gray[200] }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: theme.colors.primary[600],
                  width: `${getCompletionPercentage()}%`
                }
              ]} 
            />
          </View>
          
          <Text style={[styles.completionDescription, { color: theme.colors.gray[600] }]}>
            Complete your document verification to start earning with HVPPYPlug+
          </Text>
        </Card>

        {/* Document Types */}
        <View style={styles.documentsSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Required Documents
          </Text>
          {DOCUMENT_TYPES.filter(dt => dt.required).map(renderDocumentType)}
          
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Optional Documents
          </Text>
          {DOCUMENT_TYPES.filter(dt => !dt.required).map(renderDocumentType)}
        </View>

        {/* Help Section */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.helpHeader}>
            <FileText size={20} color={theme.colors.blue[600]} />
            <Text style={[styles.helpTitle, { color: theme.colors.gray[900] }]}>
              Document Guidelines
            </Text>
          </View>
          
          <View style={styles.helpContent}>
            <Text style={[styles.helpText, { color: theme.colors.gray[600] }]}>
              • Ensure documents are clear and readable
            </Text>
            <Text style={[styles.helpText, { color: theme.colors.gray[600] }]}>
              • All four corners must be visible
            </Text>
            <Text style={[styles.helpText, { color: theme.colors.gray[600] }]}>
              • Documents must be current and not expired
            </Text>
            <Text style={[styles.helpText, { color: theme.colors.gray[600] }]}>
              • File size should not exceed 10MB
            </Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  completionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  completionPercentage: {
    fontSize: 24,
    fontWeight: '700',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  completionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  documentsSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    marginTop: 16,
  },
  documentCard: {
    marginBottom: 16,
  },
  documentHeader: {
    marginBottom: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  requiredLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  documentDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  documentStatus: {
    gap: 8,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  documentActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expiryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  expiryText: {
    fontSize: 12,
  },
  rejectionReason: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 6,
    padding: 8,
    borderRadius: 6,
  },
  rejectionText: {
    fontSize: 12,
    flex: 1,
    lineHeight: 16,
  },
  uploadSection: {
    alignItems: 'center',
  },
  uploadingState: {
    alignItems: 'center',
    gap: 8,
  },
  uploadingText: {
    fontSize: 14,
  },
  helpHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  helpContent: {
    gap: 4,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
  },
})
