import React, { useState, useEffect, useRef } from 'react'
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Linking
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeft,
  Send,
  Phone,
  MapPin,
  Clock,
  User,
  MessageCircle
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useOrderStore } from '../../stores/orderStore'
import { useChatStore } from '../../stores/chatStore'
import { Card, Button, StatusBadge } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { RootStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<RootStackParamList, 'Chat'>

interface Message {
  id: string
  text: string
  senderId: string
  senderType: 'runner' | 'customer'
  timestamp: string
  status: 'sending' | 'sent' | 'delivered' | 'read'
}

export function ChatScreen({ route, navigation }: Props) {
  const { orderId, customerId } = route.params
  const { theme } = useTheme()
  const { currentOrder } = useOrderStore()
  const { 
    messages, 
    sendMessage, 
    markAsRead, 
    subscribeToChat, 
    unsubscribeFromChat 
  } = useChatStore()

  const [messageText, setMessageText] = useState('')
  const [sending, setSending] = useState(false)
  const flatListRef = useRef<FlatList>(null)

  useEffect(() => {
    // Subscribe to chat messages
    subscribeToChat(orderId)
    
    // Mark messages as read
    markAsRead(orderId, 'runner')

    return () => {
      unsubscribeFromChat(orderId)
    }
  }, [orderId])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true })
      }, 100)
    }
  }, [messages])

  const handleSendMessage = async () => {
    if (!messageText.trim()) return

    const message = messageText.trim()
    setMessageText('')
    setSending(true)

    try {
      await sendMessage(orderId, {
        text: message,
        senderId: 'current-runner-id', // Replace with actual runner ID
        senderType: 'runner',
        timestamp: new Date().toISOString()
      })
    } catch (error: any) {
      console.error('Failed to send message:', error)
      Alert.alert('Error', 'Failed to send message. Please try again.')
      setMessageText(message) // Restore message text
    } finally {
      setSending(false)
    }
  }

  const handleCallCustomer = () => {
    if (currentOrder?.customerPhone) {
      Linking.openURL(`tel:${currentOrder.customerPhone}`)
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  const renderMessage = ({ item }: { item: Message }) => {
    const isMyMessage = item.senderType === 'runner'
    
    return (
      <View style={[
        styles.messageContainer,
        isMyMessage ? styles.myMessageContainer : styles.theirMessageContainer
      ]}>
        <View style={[
          styles.messageBubble,
          isMyMessage 
            ? [styles.myMessageBubble, { backgroundColor: theme.colors.primary[600] }]
            : [styles.theirMessageBubble, { backgroundColor: theme.colors.gray[100] }]
        ]}>
          <Text style={[
            styles.messageText,
            { color: isMyMessage ? 'white' : theme.colors.gray[900] }
          ]}>
            {item.text}
          </Text>
          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              { color: isMyMessage ? 'rgba(255,255,255,0.7)' : theme.colors.gray[500] }
            ]}>
              {formatTime(item.timestamp)}
            </Text>
            {isMyMessage && (
              <View style={styles.messageStatus}>
                <View style={[
                  styles.statusDot,
                  { backgroundColor: getStatusColor(item.status) }
                ]} />
              </View>
            )}
          </View>
        </View>
      </View>
    )
  }

  const getStatusColor = (status: Message['status']) => {
    switch (status) {
      case 'sending': return theme.colors.gray[400]
      case 'sent': return theme.colors.gray[300]
      case 'delivered': return theme.colors.blue[400]
      case 'read': return theme.colors.green[400]
      default: return theme.colors.gray[400]
    }
  }

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity
        style={[styles.quickActionButton, { backgroundColor: theme.colors.blue[100] }]}
        onPress={() => setMessageText("I'm on my way to pick up your order!")}
      >
        <Text style={[styles.quickActionText, { color: theme.colors.blue[700] }]}>
          On my way
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.quickActionButton, { backgroundColor: theme.colors.green[100] }]}
        onPress={() => setMessageText("I've arrived at your location.")}
      >
        <Text style={[styles.quickActionText, { color: theme.colors.green[700] }]}>
          Arrived
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.quickActionButton, { backgroundColor: theme.colors.orange[100] }]}
        onPress={() => setMessageText("I'm running a few minutes late. Sorry for the delay!")}
      >
        <Text style={[styles.quickActionText, { color: theme.colors.orange[700] }]}>
          Running late
        </Text>
      </TouchableOpacity>
    </View>
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <View style={styles.customerInfo}>
            <User size={20} color={theme.colors.gray[600]} />
            <Text style={[styles.customerName, { color: theme.colors.gray[900] }]}>
              {currentOrder?.customerName || 'Customer'}
            </Text>
          </View>
          <Text style={[styles.orderInfo, { color: theme.colors.gray[600] }]}>
            Order #{orderId.slice(-6).toUpperCase()}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.callButton, { backgroundColor: theme.colors.green[600] }]}
          onPress={handleCallCustomer}
        >
          <Phone size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Order Status Banner */}
      {currentOrder && (
        <View style={[styles.statusBanner, { backgroundColor: theme.colors.blue[50] }]}>
          <StatusBadge status={currentOrder.status} size="sm" />
          <Text style={[styles.statusText, { color: theme.colors.blue[700] }]}>
            {currentOrder.deliveryAddress.street}
          </Text>
        </View>
      )}

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <MessageCircle size={48} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyTitle, { color: theme.colors.gray[600] }]}>
              No messages yet
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.gray[500] }]}>
              Start a conversation with your customer
            </Text>
          </View>
        }
      />

      {/* Quick Actions */}
      {renderQuickActions()}

      {/* Message Input */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.inputContainer, { backgroundColor: theme.colors.white }]}
      >
        <View style={styles.inputRow}>
          <TextInput
            style={[styles.textInput, { 
              backgroundColor: theme.colors.gray[100],
              color: theme.colors.gray[900]
            }]}
            placeholder="Type a message..."
            placeholderTextColor={theme.colors.gray[500]}
            value={messageText}
            onChangeText={setMessageText}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              { 
                backgroundColor: messageText.trim() 
                  ? theme.colors.primary[600] 
                  : theme.colors.gray[300]
              }
            ]}
            onPress={handleSendMessage}
            disabled={!messageText.trim() || sending}
          >
            <Send size={20} color="white" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerInfo: {
    flex: 1,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  orderInfo: {
    fontSize: 12,
    marginTop: 2,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 12,
  },
  statusText: {
    fontSize: 14,
    flex: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  messageContainer: {
    marginBottom: 16,
  },
  myMessageContainer: {
    alignItems: 'flex-end',
  },
  theirMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  myMessageBubble: {
    borderBottomRightRadius: 4,
  },
  theirMessageBubble: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 12,
  },
  messageStatus: {
    marginLeft: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  quickActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  inputContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  textInput: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
})
