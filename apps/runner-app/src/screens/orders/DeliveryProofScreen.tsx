import React, { useState, useRef } from 'react'
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Camera, CameraType } from 'expo-camera'
import * as ImagePicker from 'expo-image-picker'
import * as MediaLibrary from 'expo-media-library'
import {
  Camera as CameraIcon,
  Image as ImageIcon,
  Check,
  X,
  RotateCcw,
  ArrowLeft,
  FileText,
  Signature
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useOrderStore } from '../../stores/orderStore'
import { Card, Button, LoadingSpinner } from '../../components/ui'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { RootStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<RootStackParamList, 'DeliveryProof'>

const { width, height } = Dimensions.get('window')

export function DeliveryProofScreen({ route, navigation }: Props) {
  const { orderId } = route.params
  const { theme } = useTheme()
  const { currentOrder, uploadDeliveryProof } = useOrderStore()

  const [cameraPermission, requestCameraPermission] = Camera.useCameraPermissions()
  const [mediaPermission, requestMediaPermission] = MediaLibrary.usePermissions()
  
  const [showCamera, setShowCamera] = useState(false)
  const [cameraType, setCameraType] = useState(CameraType.back)
  const [capturedImage, setCapturedImage] = useState<string | null>(null)
  const [signature, setSignature] = useState<string | null>(null)
  const [notes, setNotes] = useState('')
  const [uploading, setUploading] = useState(false)
  
  const cameraRef = useRef<Camera>(null)

  const requestPermissions = async () => {
    if (!cameraPermission?.granted) {
      const cameraResult = await requestCameraPermission()
      if (!cameraResult.granted) {
        Alert.alert('Permission Required', 'Camera access is required to take delivery photos.')
        return false
      }
    }

    if (!mediaPermission?.granted) {
      const mediaResult = await requestMediaPermission()
      if (!mediaResult.granted) {
        Alert.alert('Permission Required', 'Media library access is required to save photos.')
        return false
      }
    }

    return true
  }

  const takePicture = async () => {
    if (!cameraRef.current) return

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      })

      setCapturedImage(photo.uri)
      setShowCamera(false)

      // Save to media library
      await MediaLibrary.saveToLibraryAsync(photo.uri)
    } catch (error) {
      console.error('Failed to take picture:', error)
      Alert.alert('Error', 'Failed to take picture. Please try again.')
    }
  }

  const pickImageFromGallery = async () => {
    const hasPermission = await requestPermissions()
    if (!hasPermission) return

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      })

      if (!result.canceled && result.assets[0]) {
        setCapturedImage(result.assets[0].uri)
      }
    } catch (error) {
      console.error('Failed to pick image:', error)
      Alert.alert('Error', 'Failed to select image. Please try again.')
    }
  }

  const openCamera = async () => {
    const hasPermission = await requestPermissions()
    if (!hasPermission) return

    setShowCamera(true)
  }

  const retakePhoto = () => {
    setCapturedImage(null)
    setShowCamera(true)
  }

  const submitDeliveryProof = async () => {
    if (!capturedImage) {
      Alert.alert('Photo Required', 'Please take a photo as proof of delivery.')
      return
    }

    setUploading(true)
    try {
      await uploadDeliveryProof(orderId, {
        photo: capturedImage,
        signature,
        notes,
        timestamp: new Date().toISOString(),
        location: {
          // Add current location if available
          latitude: 0,
          longitude: 0
        }
      })

      Alert.alert(
        'Success',
        'Delivery proof submitted successfully!',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      )
    } catch (error: any) {
      console.error('Failed to submit delivery proof:', error)
      Alert.alert('Error', error.message || 'Failed to submit delivery proof')
    } finally {
      setUploading(false)
    }
  }

  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          type={cameraType}
          ratio="4:3"
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
                onPress={() => setShowCamera(false)}
              >
                <X size={24} color="white" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
                onPress={() => setCameraType(
                  cameraType === CameraType.back ? CameraType.front : CameraType.back
                )}
              >
                <RotateCcw size={24} color="white" />
              </TouchableOpacity>
            </View>

            <View style={styles.cameraFooter}>
              <TouchableOpacity
                style={styles.captureButton}
                onPress={takePicture}
              >
                <View style={styles.captureButtonInner} />
              </TouchableOpacity>
            </View>
          </View>
        </Camera>
      </View>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[900]} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Delivery Proof
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Info */}
        <Card variant="elevated" size="md" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Order #{orderId.slice(-6).toUpperCase()}
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.gray[600] }]}>
            {currentOrder?.customerName || 'Customer'}
          </Text>
        </Card>

        {/* Photo Section */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <CameraIcon size={20} color={theme.colors.primary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Delivery Photo
            </Text>
          </View>

          {capturedImage ? (
            <View style={styles.imageContainer}>
              <Image source={{ uri: capturedImage }} style={styles.capturedImage} />
              <TouchableOpacity
                style={[styles.retakeButton, { backgroundColor: theme.colors.primary[600] }]}
                onPress={retakePhoto}
              >
                <CameraIcon size={16} color="white" />
                <Text style={styles.retakeButtonText}>Retake</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.photoActions}>
              <Button
                title="Take Photo"
                variant="primary"
                icon={<CameraIcon />}
                onPress={openCamera}
                style={styles.photoButton}
              />
              <Button
                title="Choose from Gallery"
                variant="outline"
                icon={<ImageIcon />}
                onPress={pickImageFromGallery}
                style={styles.photoButton}
              />
            </View>
          )}
        </Card>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <Button
            title={uploading ? "Submitting..." : "Submit Delivery Proof"}
            variant="primary"
            size="lg"
            onPress={submitDeliveryProof}
            disabled={!capturedImage || uploading}
            loading={uploading}
            fullWidth
            icon={<Check />}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  photoActions: {
    gap: 12,
  },
  photoButton: {
    marginBottom: 8,
  },
  imageContainer: {
    position: 'relative',
  },
  capturedImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  retakeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 4,
  },
  retakeButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  submitContainer: {
    marginTop: 24,
    marginBottom: 32,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    justifyContent: 'space-between',
  },
  cameraHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
  },
  cameraButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraFooter: {
    alignItems: 'center',
    paddingBottom: 40,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF6B6B',
  },
})
