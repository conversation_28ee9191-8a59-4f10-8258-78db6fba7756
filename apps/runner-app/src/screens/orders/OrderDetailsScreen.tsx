import { useEffect, useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Linking,
  Alert,
  RefreshControl,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeft,
  Phone,
  MapPin,
  Clock,
  Package,
  DollarSign,
  User,
  Navigation,
  CheckCircle,
  AlertCircle,

  Truck,
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useOrderStore } from '../../stores/orderStore'
import { useAppwriteReady } from '../../hooks/useAppwriteReady'
import { Card, Button, StatusBadge, LoadingSpinner } from '../../components/ui'
import { OrderTracking, DeliveryMap } from '../../components/business'
import type { Order } from '../../types'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { OrdersStackParamList } from '../../types/navigation'

type Props = NativeStackScreenProps<OrdersStackParamList, 'OrderDetails'>

export function OrderDetailsScreen({ route, navigation }: Props) {
  const { orderId } = route.params
  const { theme } = useTheme()
  const { isReady: isAppwriteReady, error: appwriteError } = useAppwriteReady()
  const {
    currentOrder,
    loading,
    error,
    fetchOrderById,
    acceptOrder,
    declineOrder,
    startPickup,

    startDelivery,
    completeDelivery,
    setCurrentOrder,
    clearError
  } = useOrderStore()

  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    if (isAppwriteReady) {
      loadOrder()
    }
    return () => {
      setCurrentOrder(null)
      clearError()
    }
  }, [orderId, isAppwriteReady])

  // Show error alerts
  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError }
      ])
    }
  }, [error])

  const loadOrder = async () => {
    if (!isAppwriteReady) {
      console.log('Appwrite not ready, skipping order fetch')
      return
    }

    const order = await fetchOrderById(orderId)
    if (order) {
      setCurrentOrder(order)
    } else {
      Alert.alert('Error', 'Order not found', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ])
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadOrder()
    setRefreshing(false)
  }

  const handleAction = async (action: string, actionFn: () => Promise<boolean>) => {
    setActionLoading(action)
    try {
      const success = await actionFn()
      if (success) {
        // Refresh order data
        await loadOrder()
      }
    } catch (error) {
      console.error(`Failed to ${action}:`, error)
    } finally {
      setActionLoading(null)
    }
  }

  const handlePhoneCall = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url)
      } else {
        Alert.alert('Error', 'Phone calls are not supported on this device')
      }
    })
  }

  const handleNavigation = (address: string) => {
    const encodedAddress = encodeURIComponent(address)
    const url = `https://maps.google.com/?q=${encodedAddress}`
    
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url)
      } else {
        Alert.alert('Error', 'Maps navigation is not available')
      }
    })
  }

  const getActionButtons = (order: Order) => {
    switch (order.status) {
      case 'pending':
        return (
          <View style={styles.actionButtons}>
            <Button
              title="Decline"
              variant="outline"
              size="lg"
              onPress={() => handleAction('decline', () => declineOrder(order.id))}
              loading={actionLoading === 'decline'}
              style={{ flex: 1 }}
            />
            <Button
              title="Accept Order"
              variant="primary"
              size="lg"
              onPress={() => handleAction('accept', () => acceptOrder(order.id))}
              loading={actionLoading === 'accept'}
              style={{ flex: 1, marginLeft: theme.spacing.md }}
            />
          </View>
        )
      
      case 'accepted':
      case 'preparing':
      case 'ready_for_pickup':
        return (
          <Button
            title="Start Pickup"
            variant="secondary"
            size="lg"
            onPress={() => handleAction('pickup', () => startPickup(order.id))}
            loading={actionLoading === 'pickup'}
            fullWidth
            icon={<Package />}
          />
        )
      
      case 'picked_up':
        return (
          <Button
            title="Start Delivery"
            variant="primary"
            size="lg"
            onPress={() => handleAction('delivery', () => startDelivery(order.id))}
            loading={actionLoading === 'delivery'}
            fullWidth
            icon={<Navigation />}
          />
        )
      
      case 'en_route':
        return (
          <Button
            title="Complete Delivery"
            variant="primary"
            size="lg"
            onPress={() => handleAction('complete', () => completeDelivery(order.id))}
            loading={actionLoading === 'complete'}
            fullWidth
            icon={<CheckCircle />}
          />
        )
      
      case 'delivered':
        return (
          <View style={[styles.completedBanner, { backgroundColor: theme.colors.success[50] }]}>
            <CheckCircle size={24} color={theme.colors.success[600]} />
            <Text style={[styles.completedText, { color: theme.colors.success[700] }]}>
              Order Completed Successfully
            </Text>
          </View>
        )
      
      default:
        return null
    }
  }

  // Show loading if Appwrite is not ready or if loading order
  if (!isAppwriteReady || (loading && !currentOrder)) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <LoadingSpinner message={!isAppwriteReady ? "Initializing..." : "Loading order details..."} />
      </SafeAreaView>
    )
  }

  // Show Appwrite error if there's an initialization error
  if (appwriteError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={theme.colors.error[500]} />
          <Text style={[styles.errorTitle, { color: theme.colors.gray[900] }]}>
            Service Unavailable
          </Text>
          <Text style={[styles.errorMessage, { color: theme.colors.gray[600] }]}>
            {appwriteError}
          </Text>
          <Button
            title="Go Back"
            variant="primary"
            onPress={() => navigation.goBack()}
            style={{ marginTop: theme.spacing.lg }}
          />
        </View>
      </SafeAreaView>
    )
  }

  if (error && !currentOrder) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={theme.colors.error[500]} />
          <Text style={[styles.errorTitle, { color: theme.colors.gray[900] }]}>
            Failed to Load Order
          </Text>
          <Text style={[styles.errorMessage, { color: theme.colors.gray[600] }]}>
            {error}
          </Text>
          <Button
            title="Try Again"
            variant="primary"
            onPress={loadOrder}
            style={{ marginTop: theme.spacing.lg }}
          />
        </View>
      </SafeAreaView>
    )
  }

  if (!currentOrder) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={theme.colors.gray[400]} />
          <Text style={[styles.errorTitle, { color: theme.colors.gray[900] }]}>
            Order Not Found
          </Text>
          <Button
            title="Go Back"
            variant="primary"
            onPress={() => navigation.goBack()}
            style={{ marginTop: theme.spacing.lg }}
          />
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Button
          title=""
          variant="ghost"
          size="sm"
          icon={<ArrowLeft />}
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        />
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
            Order #{currentOrder.id.slice(-6).toUpperCase()}
          </Text>
          <StatusBadge status={currentOrder.status} />
        </View>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary[600]}
          />
        }
      >
        {/* Customer Information */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <User size={20} color={theme.colors.primary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Customer Information
            </Text>
          </View>
          
          <View style={styles.customerInfo}>
            <Text style={[styles.customerName, { color: theme.colors.gray[900] }]}>
              {currentOrder.customerName}
            </Text>
            <Button
              title={currentOrder.customerPhone}
              variant="ghost"
              size="sm"
              icon={<Phone />}
              onPress={() => handlePhoneCall(currentOrder.customerPhone)}
              style={styles.phoneButton}
            />
          </View>
        </Card>

        {/* Delivery Address */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <MapPin size={20} color={theme.colors.secondary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Delivery Address
            </Text>
          </View>
          
          <Text style={[styles.addressText, { color: theme.colors.gray[700] }]}>
            {currentOrder.deliveryAddress.street}
          </Text>
          <Text style={[styles.addressSubtext, { color: theme.colors.gray[600] }]}>
            {currentOrder.deliveryAddress.city}, {currentOrder.deliveryAddress.province}
          </Text>
          
          {currentOrder.deliveryAddress.instructions && (
            <View style={[styles.instructionsBox, { backgroundColor: theme.colors.gray[50] }]}>
              <Text style={[styles.instructionsLabel, { color: theme.colors.gray[700] }]}>
                Delivery Instructions:
              </Text>
              <Text style={[styles.instructionsText, { color: theme.colors.gray[600] }]}>
                {currentOrder.deliveryAddress.instructions}
              </Text>
            </View>
          )}
          
          <Button
            title="Open in Maps"
            variant="outline"
            size="sm"
            icon={<Navigation />}
            onPress={() => handleNavigation(currentOrder.deliveryAddress.street)}
            style={{ marginTop: theme.spacing.md }}
          />
        </Card>

        {/* Delivery Map - Show for active deliveries */}
        {(currentOrder.status === 'picked_up' || currentOrder.status === 'en_route') && (
          <Card variant="elevated" size="md" style={styles.section}>
            <View style={styles.sectionHeader}>
              <MapPin size={20} color={theme.colors.secondary[600]} />
              <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
                Delivery Route
              </Text>
            </View>

            <DeliveryMap
              order={currentOrder}
              showRoute={true}
              showUserLocation={true}
              height={250}
              onMapReady={() => console.log('Map ready')}
              onLocationPress={(coordinate) => {
                console.log('Location pressed:', coordinate)
              }}
            />
          </Card>
        )}

        {/* Order Items */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <Package size={20} color={theme.colors.warning[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Order Items ({currentOrder.items.length})
            </Text>
          </View>
          
          {currentOrder.items.map((item, index) => (
            <View key={index} style={styles.orderItem}>
              <View style={styles.itemInfo}>
                <Text style={[styles.itemName, { color: theme.colors.gray[900] }]}>
                  {item.quantity}x {item.name}
                </Text>
                      {item.options && item.options.length > 0 && (
                  <Text style={[styles.itemOptions, { color: theme.colors.gray[600] }]}>
                    {item.options.map((opt: { name: string; choice: string; price?: number }) => `${opt.name}: ${opt.choice}`).join(', ')}
                  </Text>
                )}
                {item.specialInstructions && (
                  <Text style={[styles.itemInstructions, { color: theme.colors.gray[600] }]}>
                    Note: {item.specialInstructions}
                  </Text>
                )}
              </View>
              <Text style={[styles.itemPrice, { color: theme.colors.gray[900] }]}>
                R{item.total.toFixed(2)}
              </Text>
            </View>
          ))}
        </Card>

        {/* Order Summary */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <DollarSign size={20} color={theme.colors.success[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Order Summary
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Subtotal
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.gray[900] }]}>
              R{currentOrder.subtotal.toFixed(2)}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
              Delivery Fee
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.gray[900] }]}>
              R{currentOrder.deliveryFee.toFixed(2)}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={[styles.totalLabel, { color: theme.colors.gray[900] }]}>
              Total
            </Text>
            <Text style={[styles.totalValue, { color: theme.colors.success[600] }]}>
              R{currentOrder.total.toFixed(2)}
            </Text>
          </View>
          
          <View style={styles.paymentInfo}>
            <Text style={[styles.paymentLabel, { color: theme.colors.gray[600] }]}>
              Payment Method: {currentOrder.paymentMethod.toUpperCase()}
            </Text>
            <Text style={[styles.paymentStatus, { 
              color: currentOrder.paymentStatus === 'paid' 
                ? theme.colors.success[600] 
                : theme.colors.warning[600] 
            }]}>
              {currentOrder.paymentStatus.toUpperCase()}
            </Text>
          </View>
        </Card>

        {/* Timing Information */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color={theme.colors.secondary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Timing
            </Text>
          </View>
          
          <View style={styles.timingRow}>
            <Text style={[styles.timingLabel, { color: theme.colors.gray[600] }]}>
              Estimated Delivery
            </Text>
            <Text style={[styles.timingValue, { color: theme.colors.gray[900] }]}>
              {currentOrder.estimatedDeliveryTime}
            </Text>
          </View>
          
          <View style={styles.timingRow}>
            <Text style={[styles.timingLabel, { color: theme.colors.gray[600] }]}>
              Order Placed
            </Text>
            <Text style={[styles.timingValue, { color: theme.colors.gray[900] }]}>
              {new Date(currentOrder.createdAt).toLocaleString()}
            </Text>
          </View>
          
          {currentOrder.actualDeliveryTime && (
            <View style={styles.timingRow}>
              <Text style={[styles.timingLabel, { color: theme.colors.gray[600] }]}>
                Delivered At
              </Text>
              <Text style={[styles.timingValue, { color: theme.colors.success[600] }]}>
                {new Date(currentOrder.actualDeliveryTime).toLocaleString()}
              </Text>
            </View>
          )}
        </Card>

        {/* Order Progress Tracking */}
        <Card variant="elevated" size="md" style={styles.section}>
          <View style={styles.sectionHeader}>
            <Truck size={20} color={theme.colors.primary[600]} />
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Order Progress
            </Text>
          </View>

          <OrderTracking order={currentOrder} />
        </Card>

        {/* Special Instructions */}
        {currentOrder.specialInstructions && (
          <Card variant="elevated" size="md" style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertCircle size={20} color={theme.colors.warning[600]} />
              <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
                Special Instructions
              </Text>
            </View>
            
            <Text style={[styles.specialInstructions, { color: theme.colors.gray[700] }]}>
              {currentOrder.specialInstructions}
            </Text>
          </Card>
        )}
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionContainer, { backgroundColor: theme.colors.white }]}>
        {getActionButtons(currentOrder)}
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  customerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  phoneButton: {
    paddingHorizontal: 0,
  },
  addressText: {
    fontSize: 16,
    marginBottom: 4,
  },
  addressSubtext: {
    fontSize: 14,
    marginBottom: 12,
  },
  instructionsBox: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  instructionsLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemOptions: {
    fontSize: 14,
    marginBottom: 2,
  },
  itemInstructions: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    marginTop: 8,
    paddingTop: 12,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '700',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  paymentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  paymentLabel: {
    fontSize: 14,
  },
  paymentStatus: {
    fontSize: 14,
    fontWeight: '600',
  },
  timingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
  },
  timingLabel: {
    fontSize: 14,
  },
  timingValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  specialInstructions: {
    fontSize: 16,
    lineHeight: 24,
  },
  actionContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  completedBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  completedText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
