import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AppwriteService } from '../services/appwriteService'
import * as FileSystem from 'expo-file-system'
import * as Sharing from 'expo-sharing'

interface EarningRecord {
  id: string
  orderId: string
  customerName: string
  amount: number
  tip: number
  bonus: number
  total: number
  date: string
  duration: number // in minutes
  distance: number // in km
  pickupAddress: string
  deliveryAddress: string
  status: 'completed' | 'cancelled' | 'disputed'
}

interface EarningsSummary {
  totalEarnings: number
  totalDeliveries: number
  totalHours: number
  averagePerDelivery: number
  totalTips: number
  totalBonuses: number
}

interface PayoutInfo {
  bankName: string
  accountNumber: string
  accountHolder: string
  branchCode: string
  accountType: 'savings' | 'current'
}

interface EarningsState {
  earningsHistory: EarningRecord[]
  summary: EarningsSummary
  payoutInfo: PayoutInfo | null
  loading: boolean
  error: string | null
  
  // Actions
  fetchEarningsHistory: (period: 'today' | 'week' | 'month' | 'custom', customRange?: { start: string; end: string }) => Promise<void>
  fetchEarningsSummary: (period: 'today' | 'week' | 'month') => Promise<void>
  exportEarnings: (period: 'today' | 'week' | 'month' | 'custom', format?: 'csv' | 'pdf') => Promise<void>
  updatePayoutInfo: (payoutInfo: PayoutInfo) => Promise<void>
  requestPayout: (amount: number) => Promise<void>
  clearError: () => void
}

export const useEarningsStore = create<EarningsState>()(
  persist(
    (set, get) => ({
      earningsHistory: [],
      summary: {
        totalEarnings: 0,
        totalDeliveries: 0,
        totalHours: 0,
        averagePerDelivery: 0,
        totalTips: 0,
        totalBonuses: 0
      },
      payoutInfo: null,
      loading: false,
      error: null,

      fetchEarningsHistory: async (period, customRange) => {
        set({ loading: true, error: null })
        
        try {
          const appwrite = AppwriteService.getInstance()
          
          // Calculate date range based on period
          const now = new Date()
          let startDate: Date
          let endDate = now
          
          switch (period) {
            case 'today':
              startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
              break
            case 'week':
              startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
              break
            case 'month':
              startDate = new Date(now.getFullYear(), now.getMonth(), 1)
              break
            case 'custom':
              if (!customRange) throw new Error('Custom range required')
              startDate = new Date(customRange.start)
              endDate = new Date(customRange.end)
              break
            default:
              startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          }

          const response = await appwrite.databases.listDocuments(
            'hvppyplug-main',
            'earnings',
            [
              appwrite.Query.greaterThanEqual('date', startDate.toISOString()),
              appwrite.Query.lessThanEqual('date', endDate.toISOString()),
              appwrite.Query.orderDesc('date'),
              appwrite.Query.limit(100)
            ]
          )

          const earnings: EarningRecord[] = response.documents.map(doc => ({
            id: doc.$id,
            orderId: doc.orderId,
            customerName: doc.customerName,
            amount: doc.amount,
            tip: doc.tip || 0,
            bonus: doc.bonus || 0,
            total: doc.total,
            date: doc.date,
            duration: doc.duration,
            distance: doc.distance,
            pickupAddress: doc.pickupAddress,
            deliveryAddress: doc.deliveryAddress,
            status: doc.status
          }))

          set({ earningsHistory: earnings, loading: false })
        } catch (error: any) {
          console.error('Failed to fetch earnings history:', error)
          set({ 
            loading: false, 
            error: error.message || 'Failed to fetch earnings history' 
          })
        }
      },

      fetchEarningsSummary: async (period) => {
        try {
          const appwrite = AppwriteService.getInstance()
          
          // This would typically call a cloud function to calculate summary
          const response = await appwrite.functions.createExecution(
            'calculate-earnings-summary',
            JSON.stringify({ period })
          )

          const summaryData = JSON.parse(response.responseBody)
          
          set({ 
            summary: {
              totalEarnings: summaryData.totalEarnings || 0,
              totalDeliveries: summaryData.totalDeliveries || 0,
              totalHours: summaryData.totalHours || 0,
              averagePerDelivery: summaryData.averagePerDelivery || 0,
              totalTips: summaryData.totalTips || 0,
              totalBonuses: summaryData.totalBonuses || 0
            }
          })
        } catch (error: any) {
          console.error('Failed to fetch earnings summary:', error)
          // Use local calculation as fallback
          const { earningsHistory } = get()
          const summary = calculateLocalSummary(earningsHistory)
          set({ summary })
        }
      },

      exportEarnings: async (period, format = 'csv') => {
        set({ loading: true, error: null })
        
        try {
          const { earningsHistory } = get()
          
          if (format === 'csv') {
            await exportToCSV(earningsHistory, period)
          } else if (format === 'pdf') {
            await exportToPDF(earningsHistory, period)
          }
          
          set({ loading: false })
        } catch (error: any) {
          console.error('Failed to export earnings:', error)
          set({ 
            loading: false, 
            error: error.message || 'Failed to export earnings' 
          })
        }
      },

      updatePayoutInfo: async (payoutInfo) => {
        set({ loading: true, error: null })
        
        try {
          const appwrite = AppwriteService.getInstance()
          
          // Update payout information in database
          await appwrite.databases.updateDocument(
            'hvppyplug-main',
            'runners',
            'current-runner-id', // Replace with actual runner ID
            {
              payoutInfo: JSON.stringify(payoutInfo)
            }
          )

          set({ payoutInfo, loading: false })
        } catch (error: any) {
          console.error('Failed to update payout info:', error)
          set({ 
            loading: false, 
            error: error.message || 'Failed to update payout information' 
          })
        }
      },

      requestPayout: async (amount) => {
        set({ loading: true, error: null })
        
        try {
          const appwrite = AppwriteService.getInstance()
          const { payoutInfo } = get()
          
          if (!payoutInfo) {
            throw new Error('Please set up your payout information first')
          }

          // Create payout request
          await appwrite.databases.createDocument(
            'hvppyplug-main',
            'payout_requests',
            'unique()',
            {
              runnerId: 'current-runner-id', // Replace with actual runner ID
              amount,
              payoutInfo: JSON.stringify(payoutInfo),
              status: 'pending',
              requestedAt: new Date().toISOString()
            }
          )

          set({ loading: false })
        } catch (error: any) {
          console.error('Failed to request payout:', error)
          set({ 
            loading: false, 
            error: error.message || 'Failed to request payout' 
          })
        }
      },

      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'runner-earnings-store',
      partialize: (state) => ({
        payoutInfo: state.payoutInfo,
        summary: state.summary
      })
    }
  )
)

// Helper function to calculate summary from local data
function calculateLocalSummary(earnings: EarningRecord[]): EarningsSummary {
  const completedEarnings = earnings.filter(e => e.status === 'completed')
  
  const totalEarnings = completedEarnings.reduce((sum, e) => sum + e.total, 0)
  const totalDeliveries = completedEarnings.length
  const totalHours = completedEarnings.reduce((sum, e) => sum + e.duration, 0) / 60
  const totalTips = completedEarnings.reduce((sum, e) => sum + e.tip, 0)
  const totalBonuses = completedEarnings.reduce((sum, e) => sum + e.bonus, 0)
  
  return {
    totalEarnings,
    totalDeliveries,
    totalHours,
    averagePerDelivery: totalDeliveries > 0 ? totalEarnings / totalDeliveries : 0,
    totalTips,
    totalBonuses
  }
}

// Helper function to export earnings to CSV
async function exportToCSV(earnings: EarningRecord[], period: string) {
  const headers = [
    'Date',
    'Order ID',
    'Customer',
    'Base Amount',
    'Tip',
    'Bonus',
    'Total',
    'Duration (min)',
    'Distance (km)',
    'Pickup Address',
    'Delivery Address',
    'Status'
  ]
  
  const csvContent = [
    headers.join(','),
    ...earnings.map(earning => [
      new Date(earning.date).toLocaleDateString(),
      earning.orderId,
      earning.customerName,
      earning.amount.toFixed(2),
      earning.tip.toFixed(2),
      earning.bonus.toFixed(2),
      earning.total.toFixed(2),
      earning.duration.toString(),
      earning.distance.toFixed(1),
      `"${earning.pickupAddress}"`,
      `"${earning.deliveryAddress}"`,
      earning.status
    ].join(','))
  ].join('\n')
  
  const fileName = `earnings_${period}_${new Date().toISOString().split('T')[0]}.csv`
  const fileUri = FileSystem.documentDirectory + fileName
  
  await FileSystem.writeAsStringAsync(fileUri, csvContent, {
    encoding: FileSystem.EncodingType.UTF8
  })
  
  if (await Sharing.isAvailableAsync()) {
    await Sharing.shareAsync(fileUri, {
      mimeType: 'text/csv',
      dialogTitle: 'Export Earnings'
    })
  }
}

// Helper function to export earnings to PDF (simplified)
async function exportToPDF(earnings: EarningRecord[], period: string) {
  // This would require a PDF generation library like react-native-pdf-lib
  // For now, we'll just export as text format
  const content = [
    `Earnings Report - ${period.toUpperCase()}`,
    `Generated: ${new Date().toLocaleDateString()}`,
    '',
    'Summary:',
    `Total Earnings: R${earnings.reduce((sum, e) => sum + e.total, 0).toFixed(2)}`,
    `Total Deliveries: ${earnings.filter(e => e.status === 'completed').length}`,
    '',
    'Detailed Breakdown:',
    ...earnings.map(earning => 
      `${new Date(earning.date).toLocaleDateString()} - ${earning.orderId} - R${earning.total.toFixed(2)}`
    )
  ].join('\n')
  
  const fileName = `earnings_${period}_${new Date().toISOString().split('T')[0]}.txt`
  const fileUri = FileSystem.documentDirectory + fileName
  
  await FileSystem.writeAsStringAsync(fileUri, content, {
    encoding: FileSystem.EncodingType.UTF8
  })
  
  if (await Sharing.isAvailableAsync()) {
    await Sharing.shareAsync(fileUri, {
      mimeType: 'text/plain',
      dialogTitle: 'Export Earnings Report'
    })
  }
}
