import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AppwriteService } from '../services/appwriteService'

interface Message {
  id: string
  text: string
  senderId: string
  senderType: 'runner' | 'customer'
  timestamp: string
  status: 'sending' | 'sent' | 'delivered' | 'read'
  orderId: string
}

interface ChatState {
  messages: Message[]
  activeChats: Set<string>
  loading: boolean
  error: string | null
  
  // Actions
  sendMessage: (orderId: string, messageData: Omit<Message, 'id' | 'status' | 'orderId'>) => Promise<void>
  loadMessages: (orderId: string) => Promise<void>
  markAsRead: (orderId: string, userType: 'runner' | 'customer') => Promise<void>
  subscribeToChat: (orderId: string) => void
  unsubscribeFromChat: (orderId: string) => void
  clearMessages: () => void
  setError: (error: string | null) => void
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      messages: [],
      activeChats: new Set(),
      loading: false,
      error: null,

      sendMessage: async (orderId: string, messageData) => {
        const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        
        const newMessage: Message = {
          id: messageId,
          ...messageData,
          orderId,
          status: 'sending'
        }

        // Optimistically add message
        set(state => ({
          messages: [...state.messages, newMessage]
        }))

        try {
          const appwrite = AppwriteService.getInstance()
          
          // Create message document in Appwrite
          await appwrite.databases.createDocument(
            'hvppyplug-main',
            'chat_messages',
            messageId,
            {
              orderId,
              text: messageData.text,
              senderId: messageData.senderId,
              senderType: messageData.senderType,
              timestamp: messageData.timestamp,
              status: 'sent'
            }
          )

          // Update message status
          set(state => ({
            messages: state.messages.map(msg =>
              msg.id === messageId ? { ...msg, status: 'sent' } : msg
            )
          }))

          // Send push notification to customer
          await sendNotificationToCustomer(orderId, messageData.text)

        } catch (error: any) {
          console.error('Failed to send message:', error)
          
          // Update message status to failed
          set(state => ({
            messages: state.messages.map(msg =>
              msg.id === messageId ? { ...msg, status: 'sending' } : msg
            ),
            error: error.message || 'Failed to send message'
          }))
          
          throw error
        }
      },

      loadMessages: async (orderId: string) => {
        set({ loading: true, error: null })
        
        try {
          const appwrite = AppwriteService.getInstance()
          
          const response = await appwrite.databases.listDocuments(
            'hvppyplug-main',
            'chat_messages',
            [
              appwrite.Query.equal('orderId', orderId),
              appwrite.Query.orderAsc('timestamp'),
              appwrite.Query.limit(100)
            ]
          )

          const messages: Message[] = response.documents.map(doc => ({
            id: doc.$id,
            text: doc.text,
            senderId: doc.senderId,
            senderType: doc.senderType,
            timestamp: doc.timestamp,
            status: doc.status || 'sent',
            orderId: doc.orderId
          }))

          set({ messages, loading: false })
        } catch (error: any) {
          console.error('Failed to load messages:', error)
          set({ 
            loading: false, 
            error: error.message || 'Failed to load messages' 
          })
        }
      },

      markAsRead: async (orderId: string, userType: 'runner' | 'customer') => {
        try {
          const appwrite = AppwriteService.getInstance()
          const { messages } = get()
          
          // Find unread messages from the other party
          const unreadMessages = messages.filter(msg => 
            msg.orderId === orderId && 
            msg.senderType !== userType && 
            msg.status !== 'read'
          )

          // Update messages to read status
          const updatePromises = unreadMessages.map(msg =>
            appwrite.databases.updateDocument(
              'hvppyplug-main',
              'chat_messages',
              msg.id,
              { status: 'read' }
            )
          )

          await Promise.all(updatePromises)

          // Update local state
          set(state => ({
            messages: state.messages.map(msg =>
              unreadMessages.find(unread => unread.id === msg.id)
                ? { ...msg, status: 'read' }
                : msg
            )
          }))
        } catch (error: any) {
          console.error('Failed to mark messages as read:', error)
        }
      },

      subscribeToChat: (orderId: string) => {
        const { activeChats } = get()
        
        if (activeChats.has(orderId)) {
          return // Already subscribed
        }

        try {
          const appwrite = AppwriteService.getInstance()
          
          // Subscribe to real-time updates
          const unsubscribe = appwrite.client.subscribe(
            `databases.hvppyplug-main.collections.chat_messages.documents`,
            (response) => {
              if (response.events.includes('databases.*.collections.*.documents.*.create')) {
                const newMessage = response.payload as any
                
                if (newMessage.orderId === orderId) {
                  const message: Message = {
                    id: newMessage.$id,
                    text: newMessage.text,
                    senderId: newMessage.senderId,
                    senderType: newMessage.senderType,
                    timestamp: newMessage.timestamp,
                    status: newMessage.status || 'sent',
                    orderId: newMessage.orderId
                  }

                  set(state => {
                    // Check if message already exists
                    const exists = state.messages.find(msg => msg.id === message.id)
                    if (exists) return state

                    return {
                      messages: [...state.messages, message]
                    }
                  })
                }
              }
            }
          )

          // Store unsubscribe function
          set(state => ({
            activeChats: new Set([...state.activeChats, orderId])
          }))

          // Load initial messages
          get().loadMessages(orderId)

        } catch (error: any) {
          console.error('Failed to subscribe to chat:', error)
          set({ error: error.message || 'Failed to connect to chat' })
        }
      },

      unsubscribeFromChat: (orderId: string) => {
        set(state => {
          const newActiveChats = new Set(state.activeChats)
          newActiveChats.delete(orderId)
          return { activeChats: newActiveChats }
        })
      },

      clearMessages: () => {
        set({ messages: [], error: null })
      },

      setError: (error: string | null) => {
        set({ error })
      }
    }),
    {
      name: 'runner-chat-store',
      partialize: (state) => ({
        messages: state.messages.slice(-50) // Keep only last 50 messages
      })
    }
  )
)

// Helper function to send push notification to customer
async function sendNotificationToCustomer(orderId: string, messageText: string) {
  try {
    const appwrite = AppwriteService.getInstance()
    
    // This would typically call a cloud function to send push notification
    await appwrite.functions.createExecution(
      'send-chat-notification',
      JSON.stringify({
        orderId,
        message: messageText,
        senderType: 'runner'
      })
    )
  } catch (error) {
    console.error('Failed to send notification:', error)
    // Don't throw error as this is not critical
  }
}
