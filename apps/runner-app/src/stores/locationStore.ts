import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { useLocation, LocationData, LocationCoordinates } from '@hvppyplug/mobile-services'
import { databases, DATABASE_ID, COLLECTIONS } from '../lib/appwrite'
import { useAuthStore } from './authStore'

export interface LocationState {
  // Current location data
  currentLocation: LocationData | null
  isTracking: boolean
  isLoading: boolean
  error: string | null
  
  // Delivery tracking
  isOnDelivery: boolean
  deliveryRoute: LocationCoordinates[]
  estimatedArrival: string | null
  
  // Location sharing
  isLocationShared: boolean
  lastLocationUpdate: string | null
  
  // Actions
  startTracking: () => Promise<boolean>
  stopTracking: () => Promise<void>
  getCurrentLocation: () => Promise<LocationData | null>
  updateLocationInDatabase: (location: LocationData) => Promise<void>
  
  // Delivery specific actions
  startDeliveryTracking: (orderId: string) => Promise<boolean>
  stopDeliveryTracking: () => Promise<void>
  addRoutePoint: (coordinates: LocationCoordinates) => void
  clearRoute: () => void
  
  // Location sharing
  enableLocationSharing: () => Promise<boolean>
  disableLocationSharing: () => Promise<void>
  
  // Utility methods
  calculateDistance: (coord1: LocationCoordinates, coord2: LocationCoordinates) => number
  reverseGeocode: (latitude: number, longitude: number) => Promise<string | null>
  clearError: () => void
}

export const useLocationStore = create<LocationState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    currentLocation: null,
    isTracking: false,
    isLoading: false,
    error: null,
    isOnDelivery: false,
    deliveryRoute: [],
    estimatedArrival: null,
    isLocationShared: false,
    lastLocationUpdate: null,

    // Start location tracking
    startTracking: async () => {
      set({ isLoading: true, error: null })
      
      try {
        const { runner } = useAuthStore.getState()
        if (!runner) {
          throw new Error('No authenticated runner found')
        }

        // This will be handled by the useLocation hook in components
        set({ 
          isTracking: true, 
          isLoading: false,
          lastLocationUpdate: new Date().toISOString()
        })
        
        return true
      } catch (error: any) {
        console.error('Failed to start location tracking:', error)
        set({ 
          error: error.message || 'Failed to start location tracking',
          isLoading: false,
          isTracking: false
        })
        return false
      }
    },

    // Stop location tracking
    stopTracking: async () => {
      try {
        set({ 
          isTracking: false,
          isOnDelivery: false,
          deliveryRoute: [],
          estimatedArrival: null
        })
      } catch (error: any) {
        console.error('Failed to stop location tracking:', error)
        set({ error: error.message || 'Failed to stop location tracking' })
      }
    },

    // Get current location
    getCurrentLocation: async () => {
      set({ isLoading: true, error: null })
      
      try {
        // This will be handled by the useLocation hook
        // For now, return the stored location
        const { currentLocation } = get()
        set({ isLoading: false })
        return currentLocation
      } catch (error: any) {
        console.error('Failed to get current location:', error)
        set({ 
          error: error.message || 'Failed to get current location',
          isLoading: false
        })
        return null
      }
    },

    // Update location in database
    updateLocationInDatabase: async (location: LocationData) => {
      try {
        const { runner } = useAuthStore.getState()
        if (!runner) return

        // Update runner's location in Appwrite
        await databases.updateDocument(
          DATABASE_ID,
          COLLECTIONS.USERS,
          runner.id,
          {
            currentLocation: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              timestamp: new Date(location.timestamp).toISOString(),
              accuracy: location.coords.accuracy,
              heading: location.coords.heading,
              speed: location.coords.speed,
            },
            updatedAt: new Date().toISOString()
          }
        )

        set({ 
          currentLocation: location,
          lastLocationUpdate: new Date().toISOString()
        })
      } catch (error: any) {
        console.error('Failed to update location in database:', error)
        set({ error: error.message || 'Failed to update location' })
      }
    },

    // Start delivery tracking
    startDeliveryTracking: async (orderId: string) => {
      try {
        const success = await get().startTracking()
        if (success) {
          set({ 
            isOnDelivery: true,
            deliveryRoute: [],
            estimatedArrival: null
          })
          
          // Add current location as first route point
          const { currentLocation } = get()
          if (currentLocation) {
            get().addRoutePoint(currentLocation.coords)
          }
        }
        return success
      } catch (error: any) {
        console.error('Failed to start delivery tracking:', error)
        set({ error: error.message || 'Failed to start delivery tracking' })
        return false
      }
    },

    // Stop delivery tracking
    stopDeliveryTracking: async () => {
      try {
        set({ 
          isOnDelivery: false,
          deliveryRoute: [],
          estimatedArrival: null
        })
      } catch (error: any) {
        console.error('Failed to stop delivery tracking:', error)
        set({ error: error.message || 'Failed to stop delivery tracking' })
      }
    },

    // Add route point
    addRoutePoint: (coordinates: LocationCoordinates) => {
      const { deliveryRoute } = get()
      set({ 
        deliveryRoute: [...deliveryRoute, coordinates]
      })
    },

    // Clear route
    clearRoute: () => {
      set({ deliveryRoute: [] })
    },

    // Enable location sharing
    enableLocationSharing: async () => {
      try {
        set({ isLocationShared: true })
        
        // Start tracking if not already tracking
        if (!get().isTracking) {
          await get().startTracking()
        }
        
        return true
      } catch (error: any) {
        console.error('Failed to enable location sharing:', error)
        set({ error: error.message || 'Failed to enable location sharing' })
        return false
      }
    },

    // Disable location sharing
    disableLocationSharing: async () => {
      try {
        set({ isLocationShared: false })
      } catch (error: any) {
        console.error('Failed to disable location sharing:', error)
        set({ error: error.message || 'Failed to disable location sharing' })
      }
    },

    // Calculate distance between coordinates
    calculateDistance: (coord1: LocationCoordinates, coord2: LocationCoordinates) => {
      const R = 6371 // Earth's radius in kilometers
      const dLat = (coord2.latitude - coord1.latitude) * Math.PI / 180
      const dLon = (coord2.longitude - coord1.longitude) * Math.PI / 180
      const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(coord1.latitude * Math.PI / 180) * Math.cos(coord2.latitude * Math.PI / 180) * 
        Math.sin(dLon/2) * Math.sin(dLon/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      return R * c // Distance in kilometers
    },

    // Reverse geocode coordinates to address
    reverseGeocode: async (latitude: number, longitude: number) => {
      try {
        // This will be handled by the useLocation hook
        // For now, return a placeholder
        return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`
      } catch (error: any) {
        console.error('Failed to reverse geocode:', error)
        set({ error: error.message || 'Failed to reverse geocode' })
        return null
      }
    },

    // Clear error
    clearError: () => set({ error: null }),
  }))
)
