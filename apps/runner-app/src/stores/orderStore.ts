import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { AppwriteService } from '@hvppyplug/mobile-services'
import { Query } from 'appwrite'
import type { Order, OrderStatus, ApiResponse } from '../types'
import { useAuthStore } from './authStore'

// Get Appwrite service instance
const getAppwriteService = () => {
  try {
    const service = AppwriteService.getInstance()
    return service
  } catch (error) {
    throw new Error('Appwrite service must be initialized with config')
  }
}

interface OrderState {
  // State
  orders: Order[]
  currentOrder: Order | null
  loading: boolean
  error: string | null
  isOnline: boolean
  
  // Real-time subscription
  unsubscribe: (() => void) | null
  
  // Actions
  setOnlineStatus: (isOnline: boolean) => void
  fetchOrders: () => Promise<void>
  fetchOrderById: (orderId: string) => Promise<Order | null>
  acceptOrder: (orderId: string) => Promise<boolean>
  declineOrder: (orderId: string) => Promise<boolean>
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<boolean>
  startPickup: (orderId: string) => Promise<boolean>
  completePickup: (orderId: string) => Promise<boolean>
  startDelivery: (orderId: string) => Promise<boolean>
  completeDelivery: (orderId: string) => Promise<boolean>
  
  // Real-time methods
  subscribeToOrders: (runnerId: string) => void
  unsubscribeFromOrders: () => void
  
  // Utility methods
  clearError: () => void
  setCurrentOrder: (order: Order | null) => void
}

export const useOrderStore = create<OrderState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    orders: [],
    currentOrder: null,
    loading: false,
    error: null,
    isOnline: false,
    unsubscribe: null,

    // Set online status
    setOnlineStatus: (isOnline: boolean) => {
      set({ isOnline })
      
      // If going online, fetch orders and subscribe to real-time updates
      if (isOnline) {
        get().fetchOrders()
        // Note: subscribeToOrders should be called with runnerId from auth store
      } else {
        // If going offline, unsubscribe from real-time updates
        get().unsubscribeFromOrders()
      }
    },

    // Fetch orders from Appwrite
    fetchOrders: async () => {
      set({ loading: true, error: null })

      try {
        const { runner } = useAuthStore.getState()
        if (!runner) {
          throw new Error('No authenticated runner found')
        }

        const appwriteService = getAppwriteService()

        // Fetch orders that are available for pickup or assigned to this runner
        const response = await appwriteService.databases.listDocuments(
          appwriteService.config.databaseId,
          'orders',
          [
            Query.or([
              Query.equal('status', ['pending', 'accepted', 'preparing', 'ready_for_pickup']),
              Query.equal('runnerId', [runner.id])
            ]),
            Query.orderDesc('createdAt'),
            Query.limit(50)
          ]
        )

        const orders = response.documents.map(doc => ({
          id: doc.$id,
          customerId: doc.customerId,
          customerName: doc.customerName,
          customerPhone: doc.customerPhone,
          vendorId: doc.vendorId,
          vendorName: doc.vendorName,
          vendorAddress: doc.vendorAddress,
          runnerId: doc.runnerId,
          runnerName: doc.runnerName,
          items: doc.items,
          subtotal: doc.subtotal,
          deliveryFee: doc.deliveryFee,
          total: doc.total,
          status: doc.status,
          paymentMethod: doc.paymentMethod,
          paymentStatus: doc.paymentStatus,
          deliveryAddress: doc.deliveryAddress,
          estimatedDeliveryTime: doc.estimatedDeliveryTime,
          actualDeliveryTime: doc.actualDeliveryTime,
          specialInstructions: doc.specialInstructions,
          rating: doc.rating,
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt,
        })) as Order[]

        set({ orders, loading: false })
      } catch (error: any) {
        console.error('Failed to fetch orders:', error)
        let errorMessage = 'Failed to fetch orders'

        if (error?.message?.includes('Appwrite service must be initialized')) {
          errorMessage = 'Service is initializing. Please wait a moment and try again.'
        } else {
          errorMessage = error?.message || 'Failed to fetch orders'
        }

        set({
          error: errorMessage,
          loading: false
        })
      }
    },

    // Fetch single order by ID
    fetchOrderById: async (orderId: string) => {
      try {
        const appwriteService = getAppwriteService()

        const response = await appwriteService.databases.getDocument(
          appwriteService.config.databaseId,
          'orders',
          orderId
        )

        const order = {
          id: response.$id,
          customerId: response.customerId,
          customerName: response.customerName,
          customerPhone: response.customerPhone,
          vendorId: response.vendorId,
          vendorName: response.vendorName,
          vendorAddress: response.vendorAddress,
          runnerId: response.runnerId,
          runnerName: response.runnerName,
          items: response.items,
          subtotal: response.subtotal,
          deliveryFee: response.deliveryFee,
          total: response.total,
          status: response.status,
          paymentMethod: response.paymentMethod,
          paymentStatus: response.paymentStatus,
          deliveryAddress: response.deliveryAddress,
          estimatedDeliveryTime: response.estimatedDeliveryTime,
          actualDeliveryTime: response.actualDeliveryTime,
          specialInstructions: response.specialInstructions,
          rating: response.rating,
          createdAt: response.createdAt,
          updatedAt: response.updatedAt,
        } as Order

        return order
      } catch (error: any) {
        console.error('Failed to fetch order:', error)
        set({ error: error.message || 'Failed to fetch order' })
        return null
      }
    },

    // Accept an order
    acceptOrder: async (orderId: string) => {
      try {
        set({ loading: true, error: null })

        const { runner } = useAuthStore.getState()
        if (!runner) {
          throw new Error('No authenticated runner found')
        }

        const appwriteService = getAppwriteService()

        // Update order status and assign runner
        await appwriteService.databases.updateDocument(
          appwriteService.config.databaseId,
          'orders',
          orderId,
          {
            status: 'accepted',
            runnerId: runner.id,
            runnerName: runner.name,
            updatedAt: new Date().toISOString()
          }
        )

        // Update local state
        const orders = get().orders.map(order => 
          order.id === orderId 
            ? { ...order, status: 'accepted' as OrderStatus }
            : order
        )
        
        set({ orders, loading: false })
        return true
      } catch (error: any) {
        console.error('Failed to accept order:', error)
        set({ 
          error: error.message || 'Failed to accept order', 
          loading: false 
        })
        return false
      }
    },

    // Decline an order
    declineOrder: async (orderId: string) => {
      try {
        // For now, just remove from local state
        // In a real app, you might want to track declined orders
        const orders = get().orders.filter(order => order.id !== orderId)
        set({ orders })
        return true
      } catch (error: any) {
        console.error('Failed to decline order:', error)
        set({ error: error.message || 'Failed to decline order' })
        return false
      }
    },

    // Update order status
    updateOrderStatus: async (orderId: string, status: OrderStatus) => {
      try {
        set({ loading: true, error: null })

        const appwriteService = getAppwriteService()

        const updateData: any = {
          status,
          updatedAt: new Date().toISOString()
        }

        // Add timestamp for specific status changes
        if (status === 'delivered') {
          updateData.actualDeliveryTime = new Date().toISOString()
        }

        await appwriteService.databases.updateDocument(
          appwriteService.config.databaseId,
          'orders',
          orderId,
          updateData
        )

        // Update local state
        const orders = get().orders.map(order => 
          order.id === orderId 
            ? { ...order, status, updatedAt: updateData.updatedAt }
            : order
        )
        
        set({ orders, loading: false })
        return true
      } catch (error: any) {
        console.error('Failed to update order status:', error)
        set({ 
          error: error.message || 'Failed to update order status', 
          loading: false 
        })
        return false
      }
    },

    // Start pickup process
    startPickup: async (orderId: string) => {
      return get().updateOrderStatus(orderId, 'picked_up')
    },

    // Complete pickup (ready for delivery)
    completePickup: async (orderId: string) => {
      return get().updateOrderStatus(orderId, 'en_route')
    },

    // Start delivery process
    startDelivery: async (orderId: string) => {
      return get().updateOrderStatus(orderId, 'en_route')
    },

    // Complete delivery
    completeDelivery: async (orderId: string) => {
      return get().updateOrderStatus(orderId, 'delivered')
    },

    // Subscribe to real-time order updates
    subscribeToOrders: (runnerId: string) => {
      try {
        // Unsubscribe from any existing subscription
        get().unsubscribeFromOrders()

        // Subscribe to orders collection for real-time updates
        const unsubscribe = client.subscribe(
          `databases.${DATABASE_ID}.collections.${COLLECTIONS.ORDERS}.documents`,
          (response) => {
            console.log('Real-time order update:', response)
            
            if (response.events.includes('databases.*.collections.*.documents.*.create')) {
              // New order created
              get().fetchOrders() // Refresh orders list
            } else if (response.events.includes('databases.*.collections.*.documents.*.update')) {
              // Order updated
              const updatedOrder = response.payload as any
              const orders = get().orders.map(order => 
                order.id === updatedOrder.$id 
                  ? {
                      ...order,
                      status: updatedOrder.status,
                      runnerId: updatedOrder.runnerId,
                      runnerName: updatedOrder.runnerName,
                      updatedAt: updatedOrder.updatedAt
                    }
                  : order
              )
              set({ orders })
            } else if (response.events.includes('databases.*.collections.*.documents.*.delete')) {
              // Order deleted (cancelled)
              const deletedOrderId = response.payload.$id
              const orders = get().orders.filter(order => order.id !== deletedOrderId)
              set({ orders })
            }
          }
        )

        set({ unsubscribe })
      } catch (error: any) {
        console.error('Failed to subscribe to orders:', error)
        set({ error: error.message || 'Failed to subscribe to real-time updates' })
      }
    },

    // Unsubscribe from real-time updates
    unsubscribeFromOrders: () => {
      const { unsubscribe } = get()
      if (unsubscribe) {
        unsubscribe()
        set({ unsubscribe: null })
      }
    },

    // Utility methods
    clearError: () => set({ error: null }),
    
    setCurrentOrder: (order: Order | null) => set({ currentOrder: order }),
  }))
)
