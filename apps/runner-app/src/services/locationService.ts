import * as Location from 'expo-location'
import * as TaskManager from 'expo-task-manager'
import { AppwriteService } from './appwriteService'

const LOCATION_TASK_NAME = 'background-location-task'
const LOCATION_UPDATE_INTERVAL = 10000 // 10 seconds
const LOCATION_ACCURACY = Location.Accuracy.High

interface LocationData {
  latitude: number
  longitude: number
  accuracy: number
  speed: number | null
  heading: number | null
  timestamp: number
}

interface RouteOptimization {
  distance: number
  duration: number
  steps: RouteStep[]
}

interface RouteStep {
  instruction: string
  distance: number
  duration: number
  coordinates: {
    latitude: number
    longitude: number
  }
}

class LocationService {
  private static instance: LocationService
  private currentLocation: LocationData | null = null
  private isTracking = false
  private locationSubscription: Location.LocationSubscription | null = null
  private listeners: ((location: LocationData) => void)[] = []

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService()
    }
    return LocationService.instance
  }

  async requestPermissions(): Promise<boolean> {
    try {
      // Request foreground location permission
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync()
      
      if (foregroundStatus !== 'granted') {
        throw new Error('Foreground location permission not granted')
      }

      // Request background location permission for delivery tracking
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync()
      
      if (backgroundStatus !== 'granted') {
        console.warn('Background location permission not granted - some features may be limited')
      }

      return true
    } catch (error) {
      console.error('Failed to request location permissions:', error)
      return false
    }
  }

  async getCurrentLocation(): Promise<LocationData> {
    try {
      const hasPermission = await this.requestPermissions()
      if (!hasPermission) {
        throw new Error('Location permission not granted')
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: LOCATION_ACCURACY,
        maximumAge: 10000, // Use cached location if less than 10 seconds old
      })

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        speed: location.coords.speed,
        heading: location.coords.heading,
        timestamp: location.timestamp,
      }

      this.currentLocation = locationData
      return locationData
    } catch (error) {
      console.error('Failed to get current location:', error)
      throw error
    }
  }

  async startTracking(): Promise<void> {
    try {
      if (this.isTracking) {
        return
      }

      const hasPermission = await this.requestPermissions()
      if (!hasPermission) {
        throw new Error('Location permission not granted')
      }

      // Start foreground location tracking
      this.locationSubscription = await Location.watchPositionAsync(
        {
          accuracy: LOCATION_ACCURACY,
          timeInterval: LOCATION_UPDATE_INTERVAL,
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          const locationData: LocationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || 0,
            speed: location.coords.speed,
            heading: location.coords.heading,
            timestamp: location.timestamp,
          }

          this.currentLocation = locationData
          this.notifyListeners(locationData)
          this.updateLocationOnServer(locationData)
        }
      )

      // Start background location tracking
      await this.startBackgroundLocationTask()

      this.isTracking = true
      console.log('Location tracking started')
    } catch (error) {
      console.error('Failed to start location tracking:', error)
      throw error
    }
  }

  async stopTracking(): Promise<void> {
    try {
      if (!this.isTracking) {
        return
      }

      // Stop foreground tracking
      if (this.locationSubscription) {
        this.locationSubscription.remove()
        this.locationSubscription = null
      }

      // Stop background tracking
      await this.stopBackgroundLocationTask()

      this.isTracking = false
      console.log('Location tracking stopped')
    } catch (error) {
      console.error('Failed to stop location tracking:', error)
    }
  }

  private async startBackgroundLocationTask(): Promise<void> {
    try {
      const isTaskDefined = TaskManager.isTaskDefined(LOCATION_TASK_NAME)
      
      if (!isTaskDefined) {
        TaskManager.defineTask(LOCATION_TASK_NAME, ({ data, error }) => {
          if (error) {
            console.error('Background location task error:', error)
            return
          }

          if (data) {
            const { locations } = data as any
            const location = locations[0]
            
            if (location) {
              const locationData: LocationData = {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                accuracy: location.coords.accuracy || 0,
                speed: location.coords.speed,
                heading: location.coords.heading,
                timestamp: location.timestamp,
              }

              // Update location on server in background
              this.updateLocationOnServer(locationData)
            }
          }
        })
      }

      await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
        accuracy: LOCATION_ACCURACY,
        timeInterval: LOCATION_UPDATE_INTERVAL * 2, // Less frequent in background
        distanceInterval: 20, // Update every 20 meters in background
        foregroundService: {
          notificationTitle: 'HVPPYPlug+ Delivery',
          notificationBody: 'Tracking your location for delivery updates',
          notificationColor: '#FF6B6B',
        },
      })
    } catch (error) {
      console.error('Failed to start background location task:', error)
    }
  }

  private async stopBackgroundLocationTask(): Promise<void> {
    try {
      const hasStarted = await Location.hasStartedLocationUpdatesAsync(LOCATION_TASK_NAME)
      
      if (hasStarted) {
        await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME)
      }
    } catch (error) {
      console.error('Failed to stop background location task:', error)
    }
  }

  private async updateLocationOnServer(location: LocationData): Promise<void> {
    try {
      const appwrite = AppwriteService.getInstance()
      
      // Update runner's current location in database
      await appwrite.databases.updateDocument(
        'hvppyplug-main',
        'runners',
        'current-runner-id', // Replace with actual runner ID
        {
          currentLocation: {
            latitude: location.latitude,
            longitude: location.longitude,
            accuracy: location.accuracy,
            speed: location.speed,
            heading: location.heading,
            timestamp: new Date(location.timestamp).toISOString(),
          },
          lastSeen: new Date().toISOString(),
        }
      )
    } catch (error) {
      console.error('Failed to update location on server:', error)
      // Don't throw error as this shouldn't stop location tracking
    }
  }

  addLocationListener(listener: (location: LocationData) => void): void {
    this.listeners.push(listener)
  }

  removeLocationListener(listener: (location: LocationData) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener)
  }

  private notifyListeners(location: LocationData): void {
    this.listeners.forEach(listener => {
      try {
        listener(location)
      } catch (error) {
        console.error('Error in location listener:', error)
      }
    })
  }

  async getOptimizedRoute(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number },
    waypoints?: { latitude: number; longitude: number }[]
  ): Promise<RouteOptimization> {
    try {
      // This would integrate with Google Maps Directions API or similar
      // For now, return a mock route
      const mockRoute: RouteOptimization = {
        distance: 5200, // meters
        duration: 900, // seconds (15 minutes)
        steps: [
          {
            instruction: 'Head north on Main Street',
            distance: 500,
            duration: 60,
            coordinates: origin,
          },
          {
            instruction: 'Turn right onto Oak Avenue',
            distance: 1200,
            duration: 180,
            coordinates: {
              latitude: origin.latitude + 0.005,
              longitude: origin.longitude + 0.002,
            },
          },
          {
            instruction: 'Continue straight for 2.5km',
            distance: 2500,
            duration: 420,
            coordinates: {
              latitude: origin.latitude + 0.015,
              longitude: origin.longitude + 0.008,
            },
          },
          {
            instruction: 'Turn left onto Delivery Street',
            distance: 800,
            duration: 120,
            coordinates: {
              latitude: destination.latitude - 0.005,
              longitude: destination.longitude - 0.002,
            },
          },
          {
            instruction: 'Arrive at destination',
            distance: 200,
            duration: 120,
            coordinates: destination,
          },
        ],
      }

      return mockRoute
    } catch (error) {
      console.error('Failed to get optimized route:', error)
      throw error
    }
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      })

      if (result.length > 0) {
        const address = result[0]
        return [
          address.streetNumber,
          address.street,
          address.district,
          address.city,
          address.region,
        ]
          .filter(Boolean)
          .join(', ')
      }

      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
    } catch (error) {
      console.error('Failed to reverse geocode:', error)
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
    }
  }

  getLastKnownLocation(): LocationData | null {
    return this.currentLocation
  }

  isCurrentlyTracking(): boolean {
    return this.isTracking
  }
}

export { LocationService, type LocationData, type RouteOptimization, type RouteStep }
