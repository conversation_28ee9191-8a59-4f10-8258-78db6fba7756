import React, { useEffect, useState } from 'react'
import { View, Text, StyleSheet, Alert, Linking } from 'react-native'
import { MapPin, AlertTriangle, Settings } from 'lucide-react-native'
import * as Location from 'expo-location'

import { useTheme } from '../../providers/ThemeProvider'
import { Card, Button } from '../ui'

interface LocationPermissionHandlerProps {
  onPermissionGranted?: () => void
  onPermissionDenied?: () => void
  children?: React.ReactNode
  showAlways?: boolean
}

export function LocationPermissionHandler({
  onPermissionGranted,
  onPermissionDenied,
  children,
  showAlways = false
}: LocationPermissionHandlerProps) {
  const { theme } = useTheme()
  const [permissionStatus, setPermissionStatus] = useState<Location.LocationPermissionResponse | null>(null)
  const [backgroundPermissionStatus, setBackgroundPermissionStatus] = useState<Location.LocationPermissionResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    checkPermissions()
  }, [])

  const checkPermissions = async () => {
    try {
      // Check foreground location permission
      const foregroundPermission = await Location.getForegroundPermissionsAsync()
      setPermissionStatus(foregroundPermission)

      // Check background location permission
      const backgroundPermission = await Location.getBackgroundPermissionsAsync()
      setBackgroundPermissionStatus(backgroundPermission)

      // Notify parent component
      if (foregroundPermission.status === 'granted') {
        onPermissionGranted?.()
      } else {
        onPermissionDenied?.()
      }
    } catch (error) {
      console.error('Failed to check location permissions:', error)
    }
  }

  const requestForegroundPermission = async () => {
    setIsLoading(true)
    try {
      const permission = await Location.requestForegroundPermissionsAsync()
      setPermissionStatus(permission)

      if (permission.status === 'granted') {
        onPermissionGranted?.()
        Alert.alert(
          'Permission Granted',
          'Location access has been enabled. You can now start tracking your location for deliveries.'
        )
      } else {
        onPermissionDenied?.()
        if (permission.canAskAgain === false) {
          showSettingsAlert()
        }
      }
    } catch (error) {
      console.error('Failed to request foreground permission:', error)
      Alert.alert('Error', 'Failed to request location permission')
    } finally {
      setIsLoading(false)
    }
  }

  const requestBackgroundPermission = async () => {
    setIsLoading(true)
    try {
      const permission = await Location.requestBackgroundPermissionsAsync()
      setBackgroundPermissionStatus(permission)

      if (permission.status === 'granted') {
        Alert.alert(
          'Background Location Enabled',
          'Your location will now be tracked in the background during deliveries for better customer experience.'
        )
      } else {
        Alert.alert(
          'Background Location Denied',
          'Background location is recommended for better delivery tracking, but you can still use the app with foreground location only.'
        )
      }
    } catch (error) {
      console.error('Failed to request background permission:', error)
      Alert.alert('Error', 'Failed to request background location permission')
    } finally {
      setIsLoading(false)
    }
  }

  const showSettingsAlert = () => {
    Alert.alert(
      'Location Permission Required',
      'Location access is required for delivery tracking. Please enable location permissions in your device settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Settings', 
          onPress: () => Linking.openSettings()
        }
      ]
    )
  }

  const getPermissionStatusText = (status: string) => {
    switch (status) {
      case 'granted':
        return 'Granted'
      case 'denied':
        return 'Denied'
      case 'undetermined':
        return 'Not Requested'
      default:
        return 'Unknown'
    }
  }

  const getPermissionStatusColor = (status: string) => {
    switch (status) {
      case 'granted':
        return theme.colors.success[600]
      case 'denied':
        return theme.colors.error[600]
      case 'undetermined':
        return theme.colors.warning[600]
      default:
        return theme.colors.gray[500]
    }
  }

  // If permissions are granted and we don't need to show always, render children
  if (permissionStatus?.status === 'granted' && !showAlways) {
    return <>{children}</>
  }

  return (
    <View style={styles.container}>
      <Card variant="elevated" size="md" style={styles.card}>
        {/* Header */}
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary[100] }]}>
            <MapPin size={24} color={theme.colors.primary[600]} />
          </View>
          <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
            Location Permissions
          </Text>
        </View>

        {/* Permission Status */}
        <View style={styles.permissionsList}>
          {/* Foreground Permission */}
          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={[styles.permissionTitle, { color: theme.colors.gray[900] }]}>
                Location Access
              </Text>
              <Text style={[styles.permissionDescription, { color: theme.colors.gray[600] }]}>
                Required for delivery tracking and navigation
              </Text>
            </View>
            <View style={styles.permissionStatus}>
              <Text style={[
                styles.statusText,
                { color: getPermissionStatusColor(permissionStatus?.status || 'undetermined') }
              ]}>
                {getPermissionStatusText(permissionStatus?.status || 'undetermined')}
              </Text>
            </View>
          </View>

          {/* Background Permission */}
          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={[styles.permissionTitle, { color: theme.colors.gray[900] }]}>
                Background Location
              </Text>
              <Text style={[styles.permissionDescription, { color: theme.colors.gray[600] }]}>
                Recommended for continuous delivery tracking
              </Text>
            </View>
            <View style={styles.permissionStatus}>
              <Text style={[
                styles.statusText,
                { color: getPermissionStatusColor(backgroundPermissionStatus?.status || 'undetermined') }
              ]}>
                {getPermissionStatusText(backgroundPermissionStatus?.status || 'undetermined')}
              </Text>
            </View>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actions}>
          {permissionStatus?.status !== 'granted' && (
            <Button
              title="Enable Location Access"
              variant="primary"
              size="md"
              onPress={requestForegroundPermission}
              loading={isLoading}
              icon={<MapPin />}
              fullWidth
              style={{ marginBottom: 12 }}
            />
          )}

          {permissionStatus?.status === 'granted' && backgroundPermissionStatus?.status !== 'granted' && (
            <Button
              title="Enable Background Location"
              variant="secondary"
              size="md"
              onPress={requestBackgroundPermission}
              loading={isLoading}
              icon={<Settings />}
              fullWidth
              style={{ marginBottom: 12 }}
            />
          )}

          {permissionStatus?.canAskAgain === false && (
            <Button
              title="Open Settings"
              variant="outline"
              size="md"
              onPress={() => Linking.openSettings()}
              icon={<Settings />}
              fullWidth
            />
          )}
        </View>

        {/* Warning for denied permissions */}
        {permissionStatus?.status === 'denied' && (
          <View style={[styles.warningContainer, { backgroundColor: theme.colors.warning[50] }]}>
            <AlertTriangle size={16} color={theme.colors.warning[600]} />
            <Text style={[styles.warningText, { color: theme.colors.warning[700] }]}>
              Location access is required for delivery functionality. Some features may not work properly without it.
            </Text>
          </View>
        )}
      </Card>

      {/* Render children if permissions are granted */}
      {permissionStatus?.status === 'granted' && children}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  permissionsList: {
    marginBottom: 20,
  },
  permissionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  permissionInfo: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  permissionStatus: {
    marginLeft: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actions: {
    marginBottom: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  warningText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
})
