import React, { useRef, useEffect, useState } from 'react'
import { View, StyleSheet, Dimensions } from 'react-native'
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps'
import { MapPin, Navigation, Package } from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useLocationStore } from '../../stores/locationStore'
import type { LocationCoordinates } from '@hvppyplug/mobile-services'
import type { Order } from '../../types'

interface DeliveryMapProps {
  order?: Order
  showRoute?: boolean
  showUserLocation?: boolean
  height?: number
  onMapReady?: () => void
  onLocationPress?: (coordinate: LocationCoordinates) => void
}

const { width } = Dimensions.get('window')

export function DeliveryMap({
  order,
  showRoute = true,
  showUserLocation = true,
  height = 300,
  onMapReady,
  onLocationPress
}: DeliveryMapProps) {
  const { theme } = useTheme()
  const mapRef = useRef<MapView>(null)
  const { currentLocation, deliveryRoute, isOnDelivery } = useLocationStore()
  
  const [mapReady, setMapReady] = useState(false)

  // Default region (Soweto, Johannesburg)
  const defaultRegion = {
    latitude: -26.2678,
    longitude: 27.8546,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  }

  // Get initial region based on current location or order
  const getInitialRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }
    }
    
    if (order?.deliveryAddress.coordinates) {
      return {
        latitude: order.deliveryAddress.coordinates.latitude,
        longitude: order.deliveryAddress.coordinates.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }
    }
    
    return defaultRegion
  }

  // Fit map to show all relevant points
  useEffect(() => {
    if (!mapReady || !mapRef.current) return

    const coordinates = []
    
    // Add current location
    if (currentLocation && showUserLocation) {
      coordinates.push({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      })
    }
    
    // Add vendor location
    if (order?.vendorAddress) {
      // For now, use a mock coordinate near the delivery address
      // In a real app, you'd have the vendor's actual coordinates
      coordinates.push({
        latitude: order.deliveryAddress.coordinates.latitude + 0.005,
        longitude: order.deliveryAddress.coordinates.longitude + 0.005,
      })
    }
    
    // Add delivery location
    if (order?.deliveryAddress.coordinates) {
      coordinates.push({
        latitude: order.deliveryAddress.coordinates.latitude,
        longitude: order.deliveryAddress.coordinates.longitude,
      })
    }
    
    // Add route points
    if (showRoute && deliveryRoute.length > 0) {
      coordinates.push(...deliveryRoute)
    }

    // Fit map to coordinates if we have multiple points
    if (coordinates.length > 1) {
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      })
    }
  }, [mapReady, currentLocation, order, deliveryRoute, showRoute, showUserLocation])

  const handleMapReady = () => {
    setMapReady(true)
    onMapReady?.()
  }

  const handleMapPress = (event: any) => {
    const coordinate = event.nativeEvent.coordinate
    onLocationPress?.(coordinate)
  }

  // Custom map style for better visibility
  const mapStyle = [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
  ]

  return (
    <View style={[styles.container, { height }]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={getInitialRegion()}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={false}
        showsCompass={false}
        showsScale={false}
        showsBuildings={true}
        showsTraffic={false}
        customMapStyle={mapStyle}
        onMapReady={handleMapReady}
        onPress={handleMapPress}
      >
        {/* Current Location Marker */}
        {currentLocation && showUserLocation && (
          <Marker
            coordinate={{
              latitude: currentLocation.coords.latitude,
              longitude: currentLocation.coords.longitude,
            }}
            title="Your Location"
            description="Current runner location"
            pinColor={theme.colors.primary[600]}
          >
            <View style={[styles.markerContainer, { backgroundColor: theme.colors.primary[600] }]}>
              <Navigation size={16} color="white" />
            </View>
          </Marker>
        )}

        {/* Vendor Location Marker */}
        {order && (
          <Marker
            coordinate={{
              // Mock vendor coordinates - in real app, get from vendor data
              latitude: order.deliveryAddress.coordinates.latitude + 0.005,
              longitude: order.deliveryAddress.coordinates.longitude + 0.005,
            }}
            title={order.vendorName}
            description="Pickup location"
            pinColor={theme.colors.warning[600]}
          >
            <View style={[styles.markerContainer, { backgroundColor: theme.colors.warning[600] }]}>
              <Package size={16} color="white" />
            </View>
          </Marker>
        )}

        {/* Delivery Location Marker */}
        {order?.deliveryAddress.coordinates && (
          <Marker
            coordinate={{
              latitude: order.deliveryAddress.coordinates.latitude,
              longitude: order.deliveryAddress.coordinates.longitude,
            }}
            title="Delivery Address"
            description={order.deliveryAddress.street}
            pinColor={theme.colors.success[600]}
          >
            <View style={[styles.markerContainer, { backgroundColor: theme.colors.success[600] }]}>
              <MapPin size={16} color="white" />
            </View>
          </Marker>
        )}

        {/* Delivery Route */}
        {showRoute && deliveryRoute.length > 1 && (
          <Polyline
            coordinates={deliveryRoute}
            strokeColor={theme.colors.primary[600]}
            strokeWidth={4}
            strokePattern={isOnDelivery ? undefined : [10, 5]} // Dashed if not on delivery
          />
        )}

        {/* Route from current location to delivery address */}
        {currentLocation && order?.deliveryAddress.coordinates && isOnDelivery && (
          <Polyline
            coordinates={[
              {
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
              },
              {
                latitude: order.deliveryAddress.coordinates.latitude,
                longitude: order.deliveryAddress.coordinates.longitude,
              },
            ]}
            strokeColor={theme.colors.success[600]}
            strokeWidth={3}
            strokePattern={[5, 5]}
          />
        )}
      </MapView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
})
