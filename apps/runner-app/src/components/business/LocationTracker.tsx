import React, { useEffect, useState } from 'react'
import { View, Text, StyleSheet, Alert } from 'react-native'
import { MapPin, Navigation, Wifi, WifiOff, Clock } from 'lucide-react-native'
import { useLocation } from '@hvppyplug/mobile-services'

import { useTheme } from '../../providers/ThemeProvider'
import { Card, Button } from '../ui'
import { useLocationStore } from '../../stores/locationStore'
import { useAuthStore } from '../../stores/authStore'

interface LocationTrackerProps {
  isOnline: boolean
  onLocationUpdate?: (location: any) => void
  showControls?: boolean
  compact?: boolean
}

export function LocationTracker({ 
  isOnline, 
  onLocationUpdate, 
  showControls = true,
  compact = false 
}: LocationTrackerProps) {
  const { theme } = useTheme()
  const { runner } = useAuthStore()
  const {
    currentLocation: storeLocation,
    isTracking: storeIsTracking,
    error: storeError,
    updateLocationInDatabase,
    enableLocationSharing,
    disableLocationSharing,
    clearError
  } = useLocationStore()

  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null)

  // Initialize location tracking with mobile-services hook
  const {
    currentLocation,
    isTracking,
    isLoading,
    error,
    startTracking,
    stopTracking,
    getCurrentLocation,
    reverseGeocode
  } = useLocation({
    enableBackgroundLocation: true,
    enableAppwriteSync: true,
    distanceInterval: 10, // Update every 10 meters
    timeInterval: 5000, // Update every 5 seconds
    onLocationUpdate: handleLocationUpdate,
    onError: handleLocationError
  })

  const [currentAddress, setCurrentAddress] = useState<string>('')

  // Handle location updates
  async function handleLocationUpdate(location: any) {
    try {
      // Update the location store
      await updateLocationInDatabase(location)
      
      // Get address for display
      const address = await reverseGeocode(
        location.coords.latitude, 
        location.coords.longitude
      )
      if (address) {
        setCurrentAddress(address)
      }

      // Update last update time
      setLastUpdateTime(new Date())

      // Notify parent component
      onLocationUpdate?.(location)
    } catch (error) {
      console.error('Failed to handle location update:', error)
    }
  }

  // Handle location errors
  function handleLocationError(error: Error) {
    console.error('Location error:', error)
    Alert.alert(
      'Location Error',
      error.message,
      [{ text: 'OK', onPress: clearError }]
    )
  }

  // Start location tracking when going online
  useEffect(() => {
    if (isOnline && !isTracking) {
      handleStartTracking()
    } else if (!isOnline && isTracking) {
      handleStopTracking()
    }
  }, [isOnline])

  // Handle start tracking
  const handleStartTracking = async () => {
    try {
      const success = await startTracking()
      if (success) {
        await enableLocationSharing()
        
        // Get initial location
        const location = await getCurrentLocation()
        if (location) {
          await handleLocationUpdate(location)
        }
      }
    } catch (error: any) {
      Alert.alert('Error', 'Failed to start location tracking: ' + error.message)
    }
  }

  // Handle stop tracking
  const handleStopTracking = async () => {
    try {
      await stopTracking()
      await disableLocationSharing()
      setCurrentAddress('')
      setLastUpdateTime(null)
    } catch (error: any) {
      Alert.alert('Error', 'Failed to stop location tracking: ' + error.message)
    }
  }

  // Format last update time
  const formatLastUpdate = () => {
    if (!lastUpdateTime) return 'Never'
    
    const now = new Date()
    const diffMs = now.getTime() - lastUpdateTime.getTime()
    const diffSeconds = Math.floor(diffMs / 1000)
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`
    return lastUpdateTime.toLocaleTimeString('en-ZA', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  // Get location accuracy status
  const getAccuracyStatus = () => {
    if (!currentLocation?.coords.accuracy) return 'Unknown'
    
    const accuracy = currentLocation.coords.accuracy
    if (accuracy <= 10) return 'High'
    if (accuracy <= 50) return 'Medium'
    return 'Low'
  }

  // Get location status color
  const getStatusColor = () => {
    if (!isTracking) return theme.colors.gray[500]
    if (error) return theme.colors.error[600]
    return theme.colors.success[600]
  }

  if (compact) {
    return (
      <View style={[styles.compactContainer, { backgroundColor: theme.colors.white }]}>
        <View style={styles.compactContent}>
          <View style={styles.compactStatus}>
            {isTracking ? (
              <Wifi size={16} color={theme.colors.success[600]} />
            ) : (
              <WifiOff size={16} color={theme.colors.gray[500]} />
            )}
            <Text style={[
              styles.compactStatusText,
              { color: getStatusColor() }
            ]}>
              {isTracking ? 'Tracking' : 'Offline'}
            </Text>
          </View>
          
          {currentAddress && (
            <Text style={[styles.compactAddress, { color: theme.colors.gray[600] }]} numberOfLines={1}>
              {currentAddress}
            </Text>
          )}
        </View>
      </View>
    )
  }

  return (
    <Card variant="elevated" size="md" style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <MapPin size={20} color={theme.colors.primary[600]} />
          <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
            Location Tracking
          </Text>
        </View>
        
        <View style={[styles.statusBadge, { 
          backgroundColor: isTracking 
            ? theme.colors.success[100] 
            : theme.colors.gray[100] 
        }]}>
          <Text style={[styles.statusText, { 
            color: isTracking 
              ? theme.colors.success[700] 
              : theme.colors.gray[600] 
          }]}>
            {isTracking ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>

      {/* Location Info */}
      {currentLocation && (
        <View style={styles.locationInfo}>
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>
              Address:
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.gray[900] }]} numberOfLines={2}>
              {currentAddress || 'Getting address...'}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>
              Coordinates:
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.gray[900] }]}>
              {currentLocation.coords.latitude.toFixed(6)}, {currentLocation.coords.longitude.toFixed(6)}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>
              Accuracy:
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.gray[900] }]}>
              {getAccuracyStatus()} ({currentLocation.coords.accuracy?.toFixed(0)}m)
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Clock size={14} color={theme.colors.gray[500]} />
            <Text style={[styles.lastUpdate, { color: theme.colors.gray[500] }]}>
              Last updated: {formatLastUpdate()}
            </Text>
          </View>
        </View>
      )}

      {/* Controls */}
      {showControls && (
        <View style={styles.controls}>
          {!isTracking ? (
            <Button
              title="Start Tracking"
              variant="primary"
              size="sm"
              onPress={handleStartTracking}
              loading={isLoading}
              icon={<Navigation />}
              fullWidth
            />
          ) : (
            <Button
              title="Stop Tracking"
              variant="outline"
              size="sm"
              onPress={handleStopTracking}
              loading={isLoading}
              fullWidth
            />
          )}
        </View>
      )}

      {/* Error Display */}
      {(error || storeError) && (
        <View style={[styles.errorContainer, { backgroundColor: theme.colors.error[50] }]}>
          <Text style={[styles.errorText, { color: theme.colors.error[700] }]}>
            {error?.message || storeError}
          </Text>
        </View>
      )}
    </Card>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  compactContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  compactStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  compactAddress: {
    fontSize: 12,
    flex: 1,
    textAlign: 'right',
    marginLeft: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  locationInfo: {
    marginBottom: 16,
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    flex: 1,
  },
  lastUpdate: {
    fontSize: 12,
    marginLeft: 4,
  },
  controls: {
    marginTop: 8,
  },
  errorContainer: {
    marginTop: 12,
    padding: 12,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
})
