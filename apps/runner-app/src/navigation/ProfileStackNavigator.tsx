import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { ProfileStackParamList } from '../types/navigation'
import { useTheme } from '../providers/ThemeProvider'

// Import Profile Screens
import { ProfileHomeScreen } from '../screens/profile/ProfileHomeScreen'
import { EditProfileScreen } from '../screens/profile/EditProfileScreen'
import { DocumentsScreen } from '../screens/profile/DocumentsScreen'
import { VehicleInfoScreen } from '../screens/profile/VehicleInfoScreen'

const Stack = createNativeStackNavigator<ProfileStackParamList>()

export function ProfileStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="ProfileHome"
        component={ProfileHomeScreen}
        options={{
          title: 'Profile',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: 'Edit Profile',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="Documents"
        component={DocumentsScreen}
        options={{
          title: 'Documents',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="VehicleInfo"
        component={VehicleInfoScreen}
        options={{
          title: 'Vehicle Information',
          headerShown: false, // Custom header in the screen
        }}
      />
    </Stack.Navigator>
  )
}
