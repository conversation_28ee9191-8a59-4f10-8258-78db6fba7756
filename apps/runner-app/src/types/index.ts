export * from './navigation'

// Order Types
export interface Order {
  id: string
  customerId: string
  customerName: string
  customerPhone: string
  vendorId: string
  vendorName: string
  vendorAddress: string
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  total: number
  status: OrderStatus
  paymentMethod: string
  paymentStatus: PaymentStatus
  deliveryAddress: DeliveryAddress
  estimatedDeliveryTime: string
  actualDeliveryTime?: string
  specialInstructions?: string
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  total: number
  specialRequests?: string
  specialInstructions?: string
  options?: Array<{
    name: string
    choice: string
    price?: number
  }>
}

export type OrderStatus =
  | 'pending'
  | 'accepted'
  | 'preparing'
  | 'ready_for_pickup'
  | 'picked_up'
  | 'en_route'
  | 'delivered'
  | 'cancelled'

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'

export interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
  coordinates: {
    latitude: number
    longitude: number
  }
  instructions?: string
}

// Runner Types
export interface Runner {
  id: string
  name: string
  email: string
  phone: string
  avatar?: string
  status: RunnerStatus
  vehicleType: VehicleType
  vehicleDetails: VehicleDetails
  documents: RunnerDocuments
  bankDetails: BankDetails
  rating: number
  totalDeliveries: number
  isOnline: boolean
  currentLocation?: {
    latitude: number
    longitude: number
    timestamp: string
  }
  createdAt: string
  updatedAt: string
}

export type RunnerStatus = 
  | 'pending_approval'
  | 'approved'
  | 'suspended'
  | 'deactivated'

export type VehicleType = 
  | 'bicycle'
  | 'motorcycle'
  | 'car'
  | 'scooter'

export interface VehicleDetails {
  make?: string
  model?: string
  year?: string
  color?: string
  licensePlate?: string
}

export interface RunnerDocuments {
  idDocument?: string
  driversLicense?: string
  vehicleRegistration?: string
  proofOfResidence?: string
  bankStatement?: string
}

export interface BankDetails {
  accountHolderName: string
  bankName: string
  accountNumber: string
  branchCode: string
  accountType: 'savings' | 'current'
}

// Earnings Types
export interface Earnings {
  id: string
  runnerId: string
  orderId: string
  baseAmount: number
  tips: number
  bonuses: number
  total: number
  date: string
  status: 'pending' | 'paid' | 'processing'
}

export interface EarningsSummary {
  today: number
  week: number
  month: number
  totalEarnings: number
  totalDeliveries: number
  averageRating: number
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: NotificationType
  data?: Record<string, any>
  isRead: boolean
  createdAt: string
}

export type NotificationType = 
  | 'new_order'
  | 'order_update'
  | 'payment_received'
  | 'system_message'
  | 'promotion'

// Location Types
export interface LocationUpdate {
  latitude: number
  longitude: number
  accuracy?: number
  heading?: number
  speed?: number
  timestamp: string
}

// Chat Types
export interface ChatMessage {
  id: string
  senderId: string
  senderName: string
  senderType: 'runner' | 'customer'
  message: string
  type: 'text' | 'image' | 'location'
  timestamp: string
  isRead: boolean
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}
