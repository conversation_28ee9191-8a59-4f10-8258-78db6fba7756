const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// Define monorepo packages for better performance
const monorepoPackages = {
  '@hvppyplug/common': path.resolve(monorepoRoot, 'packages/common'),
  '@hvppyplug/compound-components': path.resolve(monorepoRoot, 'packages/compound-components'),
  '@hvppyplug/ui-components-v2': path.resolve(monorepoRoot, 'packages/ui-components-v2'),
  '@hvppyplug/mobile-services': path.resolve(monorepoRoot, 'packages/mobile-services'),
};

// 1. Watch the local app directory and only the shared packages (optimized for performance)
config.watchFolders = [projectRoot, ...Object.values(monorepoPackages)];

// 2. Add the monorepo workspaces as extraNodeModules to Metro
config.resolver.extraNodeModules = monorepoPackages;

// 3. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// 4. Force Metro to resolve (sub)dependencies only from the nodeModulesPaths
config.resolver.disableHierarchicalLookup = true;

// 5. Ensure proper platform extensions resolution
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
