{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/services/*": ["./src/services/*"], "@/hooks/*": ["./src/hooks/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./src/constants/*"]}, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx"}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}