import React, { useEffect } from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { GluestackUIProvider } from '@hvppyplug/compound-components'

import { ThemeProvider } from './src/providers/ThemeProvider'
import { AppwriteProvider } from './src/providers/AppwriteProvider'
import { ErrorBoundary } from './src/components/ErrorBoundary'
import { RootNavigator } from './src/navigation/RootNavigator'
import { notificationService } from './src/services/notificationService'
import { offlineService } from './src/services/offlineService'
import { orderService } from './src/services/orderService'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})

export default function App() {
  useEffect(() => {
    // Initialize services
    const initializeServices = async () => {
      try {
        await Promise.all([
          notificationService.initialize(),
          offlineService.initialize(),
          orderService.initialize()
        ])
        console.log('✅ All vendor services initialized successfully')
      } catch (error) {
        console.error('❌ Failed to initialize vendor services:', error)
      }
    }

    initializeServices()
  }, [])

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <QueryClientProvider client={queryClient}>
            <AppwriteProvider>
              <GluestackUIProvider mode="light">
                <ThemeProvider>
                  <RootNavigator />
                </ThemeProvider>
              </GluestackUIProvider>
            </AppwriteProvider>
          </QueryClientProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  )
}
