{"expo": {"name": "HVPPYPlug+ Vendor", "slug": "hvppyplug-vendor", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hvppyplug.vendor", "buildNumber": "1.0.0", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access to show your store location and manage deliveries.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs location access to show your store location and manage deliveries.", "NSCameraUsageDescription": "This app needs camera access to take photos of menu items and delivery confirmations.", "NSPhotoLibraryUsageDescription": "This app needs photo library access to select images for menu items.", "NSMicrophoneUsageDescription": "This app needs microphone access for voice messages with customers."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.hvppyplug.vendor", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "sounds": ["./assets/notification.wav"]}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow HVPPYPlug+ Vendor to use your location to show your store location and manage deliveries."}], ["expo-camera", {"cameraPermission": "Allow HVPPYPlug+ Vendor to access your camera to take photos of menu items and delivery confirmations."}], ["expo-image-picker", {"photosPermission": "Allow HVPPYPlug+ Vendor to access your photos to select images for menu items."}]], "extra": {"appwriteEndpoint": "https://cloud.appwrite.io/v1", "appwriteProjectId": "hvppyplug", "appwriteDatabaseId": "hvppyplug-main", "appwriteStorageId": "images", "eas": {"projectId": "your-eas-project-id"}}, "owner": "hvppyplug"}}