const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// Define monorepo packages for better performance
const monorepoPackages = {
  '@hvppyplug/common': path.resolve(monorepoRoot, 'packages/common'),
  '@hvppyplug/compound-components': path.resolve(monorepoRoot, 'packages/compound-components'),
  '@hvppyplug/ui-components-v2': path.resolve(monorepoRoot, 'packages/ui-components-v2'),
  '@hvppyplug/mobile-services': path.resolve(monorepoRoot, 'packages/mobile-services'),
};

// 1. Watch all files within the monorepo
config.watchFolders = [
  monorepoRoot,
  ...Object.values(monorepoPackages),
];

// 2. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// 3. Force Metro to resolve (sub)dependencies only from the `nodeModulesPaths`
config.resolver.disableHierarchicalLookup = true;

// 4. Add support for additional asset extensions
config.resolver.assetExts.push(
  // Adds support for `.db` files for SQLite databases
  'db'
);

// 5. Configure transformer for better performance
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

module.exports = config;
