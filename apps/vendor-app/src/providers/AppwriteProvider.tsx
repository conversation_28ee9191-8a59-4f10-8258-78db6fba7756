import React, { createContext, useContext, useEffect, useState } from 'react'
import { account, databases, DATABASE_ID, COLLECTIONS } from '../lib/appwrite'
import { useAuthStore } from '../stores/authStore'
import type { User, Vendor } from '../stores/authStore'

interface AppwriteContextType {
  isInitialized: boolean
  isLoading: boolean
  error: string | null
  login: (phone: string) => Promise<void>
  verifyOTP: (phone: string, otp: string) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<User | null>
  getCurrentVendor: () => Promise<Vendor | null>
}

const AppwriteContext = createContext<AppwriteContextType | undefined>(undefined)

interface AppwriteProviderProps {
  children: React.ReactNode
}

export function AppwriteProvider({ children }: AppwriteProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const {
    setUser,
    setVendor,
    setAuthenticated,
    setToken,
    logout: logoutStore,
    user,
    token,
  } = useAuthStore()

  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if user has a valid session
      if (token) {
        try {
          const currentUser = await getCurrentUser()
          if (currentUser) {
            setAuthenticated(true)
            const vendor = await getCurrentVendor()
            if (vendor) {
              setVendor(vendor)
            }
          } else {
            // Invalid session, clear auth state
            logoutStore()
          }
        } catch (error) {
          console.log('No valid session found')
          logoutStore()
        }
      }

      setIsInitialized(true)
    } catch (error: any) {
      console.error('Failed to initialize auth:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (phone: string) => {
    try {
      setIsLoading(true)
      setError(null)

      // Send OTP via Appwrite function
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send OTP')
      }

      console.log('OTP sent successfully')
    } catch (error: any) {
      console.error('Login error:', error)
      setError(error.message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const verifyOTP = async (phone: string, otp: string) => {
    try {
      setIsLoading(true)
      setError(null)

      // Verify OTP via Appwrite function
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/auth/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, otp }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify OTP')
      }

      // Set user data
      const userData = data.user
      setUser(userData)
      setToken(data.token || 'authenticated')
      setAuthenticated(true)

      // Get vendor data if user is a vendor
      if (userData.role === 'vendor' && userData.vendorId) {
        const vendor = await getCurrentVendor()
        if (vendor) {
          setVendor(vendor)
        }
      }

      console.log('OTP verified successfully')
    } catch (error: any) {
      console.error('OTP verification error:', error)
      setError(error.message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Try to delete session from Appwrite
      try {
        await account.deleteSession('current')
      } catch (error) {
        // Session might already be invalid, continue with logout
        console.log('Session already invalid or expired')
      }

      // Clear local auth state
      logoutStore()
      
      console.log('Logged out successfully')
    } catch (error: any) {
      console.error('Logout error:', error)
      setError(error.message)
      // Still clear local state even if server logout fails
      logoutStore()
    } finally {
      setIsLoading(false)
    }
  }

  const getCurrentUser = async (): Promise<User | null> => {
    try {
      // If we have user data in store, return it
      if (user) {
        return user
      }

      // Otherwise try to get from Appwrite
      const accountData = await account.get()
      
      // Get user document from database
      const userDoc = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        accountData.$id
      )

      const userData: User = {
        id: userDoc.$id,
        name: userDoc.name,
        phone: userDoc.phone,
        email: userDoc.email,
        role: userDoc.role,
        avatarUrl: userDoc.avatarUrl,
        vendorId: userDoc.vendorId,
        isVerified: userDoc.isVerified || false,
        createdAt: userDoc.createdAt || userDoc.$createdAt,
        updatedAt: userDoc.updatedAt || userDoc.$updatedAt,
      }

      setUser(userData)
      return userData
    } catch (error) {
      console.error('Failed to get current user:', error)
      return null
    }
  }

  const getCurrentVendor = async (): Promise<Vendor | null> => {
    try {
      const currentUser = user || await getCurrentUser()
      
      if (!currentUser || currentUser.role !== 'vendor' || !currentUser.vendorId) {
        return null
      }

      // Get vendor document from database
      const vendorDoc = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        currentUser.vendorId
      )

      const vendorData: Vendor = {
        id: vendorDoc.$id,
        name: vendorDoc.name,
        description: vendorDoc.description,
        ownerId: vendorDoc.ownerId,
        location: typeof vendorDoc.location === 'string' 
          ? JSON.parse(vendorDoc.location) 
          : vendorDoc.location,
        status: vendorDoc.status,
        categories: vendorDoc.categories || [],
        isOpen: vendorDoc.isOpen !== false,
        rating: vendorDoc.rating || 0,
        reviewCount: vendorDoc.reviewCount || 0,
        imageUrl: vendorDoc.imageUrl,
        businessHours: typeof vendorDoc.businessHours === 'string'
          ? JSON.parse(vendorDoc.businessHours)
          : vendorDoc.businessHours || {},
        deliverySettings: typeof vendorDoc.deliverySettings === 'string'
          ? JSON.parse(vendorDoc.deliverySettings)
          : vendorDoc.deliverySettings || {
              deliveryRadius: 5,
              deliveryFee: 25,
              minimumOrder: 50,
              estimatedDeliveryTime: '30-45 minutes'
            },
        paymentMethods: vendorDoc.paymentMethods || ['cash', 'card'],
        createdAt: vendorDoc.createdAt || vendorDoc.$createdAt,
        updatedAt: vendorDoc.updatedAt || vendorDoc.$updatedAt,
      }

      setVendor(vendorData)
      return vendorData
    } catch (error) {
      console.error('Failed to get current vendor:', error)
      return null
    }
  }

  const value: AppwriteContextType = {
    isInitialized,
    isLoading,
    error,
    login,
    verifyOTP,
    logout,
    getCurrentUser,
    getCurrentVendor,
  }

  return (
    <AppwriteContext.Provider value={value}>
      {children}
    </AppwriteContext.Provider>
  )
}

export function useAppwrite() {
  const context = useContext(AppwriteContext)
  if (context === undefined) {
    throw new Error('useAppwrite must be used within an AppwriteProvider')
  }
  return context
}
