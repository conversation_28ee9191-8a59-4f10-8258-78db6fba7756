import { NavigatorScreenParams } from '@react-navigation/native'

// Main Tab Navigator Types
export type MainTabParamList = {
  Dashboard: undefined
  Orders: NavigatorScreenParams<OrdersStackParamList>
  Menu: NavigatorScreenParams<MenuStackParamList>
  Analytics: NavigatorScreenParams<AnalyticsStackParamList>
  Profile: NavigatorScreenParams<ProfileStackParamList>
}

// Orders Stack Navigator Types
export type OrdersStackParamList = {
  OrdersList: undefined
  OrderDetails: { orderId: string }
  OrderHistory: undefined
  OrderFilters: undefined
}

// Menu Stack Navigator Types
export type MenuStackParamList = {
  MenuList: undefined
  MenuItemDetails: { itemId: string }
  AddMenuItem: undefined
  EditMenuItem: { itemId: string }
  MenuCategories: undefined
  ManageCategory: { categoryId?: string }
}

// Analytics Stack Navigator Types
export type AnalyticsStackParamList = {
  AnalyticsHome: undefined
  SalesReport: { period: 'daily' | 'weekly' | 'monthly' | 'custom', startDate?: string, endDate?: string }
  ProductPerformance: undefined
  CustomerInsights: undefined
}

// Profile Stack Navigator Types
export type ProfileStackParamList = {
  ProfileHome: undefined
  StoreSettings: undefined
  BusinessHours: undefined
  DeliverySettings: undefined
  PaymentSettings: undefined
  NotificationSettings: undefined
  AccountSettings: undefined
  Support: undefined
}

// Root Stack Navigator Types
export type RootStackParamList = {
  // Auth Flow
  Onboarding: undefined
  Login: undefined
  Register: undefined
  ForgotPassword: undefined
  OTPVerification: { phone: string; type: 'register' | 'forgot-password' }
  
  // Main App
  MainTabs: NavigatorScreenParams<MainTabParamList>
  
  // Modal Screens
  OrderDetail: { orderId: string }
  RunnerAssignment: { orderId: string }
  Chat: { orderId: string; customerId: string }
  DeliveryTracking: { orderId: string; runnerId: string }
  NotificationDetail: { notificationId: string }
  MenuItemForm: { itemId?: string }
  StoreStatusModal: undefined
  Help: undefined
  Support: undefined
  Settings: undefined
}
