import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { OrdersStackParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Order Screens
import { OrdersListScreen } from '../screens/orders/OrdersListScreen'
import { OrderDetailsScreen } from '../screens/orders/OrderDetailsScreen'
import { OrderHistoryScreen } from '../screens/orders/OrderHistoryScreen'
import { OrderFiltersScreen } from '../screens/orders/OrderFiltersScreen'

const Stack = createNativeStackNavigator<OrdersStackParamList>()

export function OrdersStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="OrdersList"
        component={OrdersListScreen}
        options={{
          title: 'Orders',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="OrderDetails"
        component={OrderDetailsScreen}
        options={{
          title: 'Order Details',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="OrderHistory"
        component={OrderHistoryScreen}
        options={{
          title: 'Order History',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="OrderFilters"
        component={OrderFiltersScreen}
        options={{
          title: 'Filter Orders',
          presentation: 'modal',
        }}
      />
    </Stack.Navigator>
  )
}
