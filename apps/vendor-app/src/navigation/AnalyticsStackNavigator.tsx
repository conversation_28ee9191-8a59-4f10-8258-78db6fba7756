import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { AnalyticsStackParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Analytics Screens
import { AnalyticsHomeScreen } from '../screens/analytics/AnalyticsHomeScreen'
import { SalesReportScreen } from '../screens/analytics/SalesReportScreen'
import { ProductPerformanceScreen } from '../screens/analytics/ProductPerformanceScreen'
import { CustomerInsightsScreen } from '../screens/analytics/CustomerInsightsScreen'

const Stack = createNativeStackNavigator<AnalyticsStackParamList>()

export function AnalyticsStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="AnalyticsHome"
        component={AnalyticsHomeScreen}
        options={{
          title: 'Analytics',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="SalesReport"
        component={SalesReportScreen}
        options={{
          title: 'Sales Report',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="ProductPerformance"
        component={ProductPerformanceScreen}
        options={{
          title: 'Product Performance',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="CustomerInsights"
        component={CustomerInsightsScreen}
        options={{
          title: 'Customer Insights',
          headerShown: false, // Custom header in the screen
        }}
      />
    </Stack.Navigator>
  )
}
