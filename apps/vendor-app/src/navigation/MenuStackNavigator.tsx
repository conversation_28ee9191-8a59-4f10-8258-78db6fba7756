import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { MenuStackParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Menu Screens
import { MenuListScreen } from '../screens/menu/MenuListScreen'
import { MenuItemDetailsScreen } from '../screens/menu/MenuItemDetailsScreen'
import { AddMenuItemScreen } from '../screens/menu/AddMenuItemScreen'
import { EditMenuItemScreen } from '../screens/menu/EditMenuItemScreen'
import { MenuCategoriesScreen } from '../screens/menu/MenuCategoriesScreen'
import { ManageCategoryScreen } from '../screens/menu/ManageCategoryScreen'

const Stack = createNativeStackNavigator<MenuStackParamList>()

export function MenuStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="MenuList"
        component={MenuListScreen}
        options={{
          title: 'Menu',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="MenuItemDetails"
        component={MenuItemDetailsScreen}
        options={{
          title: 'Menu Item',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="AddMenuItem"
        component={AddMenuItemScreen}
        options={{
          title: 'Add Menu Item',
          presentation: 'modal',
        }}
      />

      <Stack.Screen
        name="EditMenuItem"
        component={EditMenuItemScreen}
        options={{
          title: 'Edit Menu Item',
          presentation: 'modal',
        }}
      />

      <Stack.Screen
        name="MenuCategories"
        component={MenuCategoriesScreen}
        options={{
          title: 'Categories',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="ManageCategory"
        component={ManageCategoryScreen}
        options={{
          title: 'Manage Category',
          presentation: 'modal',
        }}
      />
    </Stack.Navigator>
  )
}
