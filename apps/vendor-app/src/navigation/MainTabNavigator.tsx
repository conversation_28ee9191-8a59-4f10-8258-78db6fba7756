import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { Home, Package, Grid, BarChart3, User } from 'lucide-react-native'

import type { MainTabParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Stack Navigators
import { DashboardStackNavigator } from './DashboardStackNavigator'
import { OrdersStackNavigator } from './OrdersStackNavigator'
import { MenuStackNavigator } from './MenuStackNavigator'
import { AnalyticsStackNavigator } from './AnalyticsStackNavigator'
import { ProfileStackNavigator } from './ProfileStackNavigator'

const Tab = createBottomTabNavigator<MainTabParamList>()

export function MainTabNavigator() {
  const { theme } = useTheme()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: theme.colors.primary[600],
        tabBarInactiveTintColor: theme.colors.gray[500],
        tabBarStyle: {
          backgroundColor: theme.colors.white,
          borderTopColor: theme.colors.gray[200],
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
          height: 80,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let IconComponent
          
          switch (route.name) {
            case 'Dashboard':
              IconComponent = Home
              break
            case 'Orders':
              IconComponent = Package
              break
            case 'Menu':
              IconComponent = Grid
              break
            case 'Analytics':
              IconComponent = BarChart3
              break
            case 'Profile':
              IconComponent = User
              break
            default:
              IconComponent = Home
          }

          return <IconComponent size={size} color={color} />
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStackNavigator}
        options={{
          tabBarLabel: 'Dashboard',
          tabBarBadge: undefined, // Will be set dynamically for new orders
        }}
      />
      <Tab.Screen
        name="Orders"
        component={OrdersStackNavigator}
        options={{
          tabBarLabel: 'Orders',
        }}
      />
      <Tab.Screen
        name="Menu"
        component={MenuStackNavigator}
        options={{
          tabBarLabel: 'Menu',
        }}
      />
      <Tab.Screen
        name="Analytics"
        component={AnalyticsStackNavigator}
        options={{
          tabBarLabel: 'Analytics',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  )
}
