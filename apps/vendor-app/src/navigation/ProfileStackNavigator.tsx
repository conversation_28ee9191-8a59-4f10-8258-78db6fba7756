import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { ProfileStackParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Profile Screens
import { ProfileHomeScreen } from '../screens/profile/ProfileHomeScreen'
import { StoreSettingsScreen } from '../screens/profile/StoreSettingsScreen'
import { BusinessHoursScreen } from '../screens/profile/BusinessHoursScreen'
import { DeliverySettingsScreen } from '../screens/profile/DeliverySettingsScreen'
import { PaymentSettingsScreen } from '../screens/profile/PaymentSettingsScreen'
import { NotificationSettingsScreen } from '../screens/profile/NotificationSettingsScreen'
import { AccountSettingsScreen } from '../screens/profile/AccountSettingsScreen'
import { SupportScreen } from '../screens/profile/SupportScreen'

const Stack = createNativeStackNavigator<ProfileStackParamList>()

export function ProfileStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="ProfileHome"
        component={ProfileHomeScreen}
        options={{
          title: 'Profile',
          headerShown: false, // Custom header in the screen
        }}
      />

      <Stack.Screen
        name="StoreSettings"
        component={StoreSettingsScreen}
        options={{
          title: 'Store Settings',
        }}
      />

      <Stack.Screen
        name="BusinessHours"
        component={BusinessHoursScreen}
        options={{
          title: 'Business Hours',
        }}
      />

      <Stack.Screen
        name="DeliverySettings"
        component={DeliverySettingsScreen}
        options={{
          title: 'Delivery Settings',
        }}
      />

      <Stack.Screen
        name="PaymentSettings"
        component={PaymentSettingsScreen}
        options={{
          title: 'Payment Settings',
        }}
      />

      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
        options={{
          title: 'Notifications',
        }}
      />

      <Stack.Screen
        name="AccountSettings"
        component={AccountSettingsScreen}
        options={{
          title: 'Account Settings',
        }}
      />

      <Stack.Screen
        name="Support"
        component={SupportScreen}
        options={{
          title: 'Support',
        }}
      />
    </Stack.Navigator>
  )
}
