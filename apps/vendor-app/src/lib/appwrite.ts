import { Client, Account, Databases, Storage, Functions } from 'appwrite'
import Constants from 'expo-constants'

// Initialize Appwrite client
const client = new Client()
client
  .setEndpoint(Constants.expoConfig?.extra?.appwriteEndpoint || 'https://cloud.appwrite.io/v1')
  .setProject(Constants.expoConfig?.extra?.appwriteProjectId || 'hvppyplug')

// Initialize Appwrite services
export const account = new Account(client)
export const databases = new Databases(client)
export const storage = new Storage(client)
export const functions = new Functions(client)

// Export client for custom usage
export { client }

// Database and collection IDs
export const DATABASE_ID = Constants.expoConfig?.extra?.appwriteDatabaseId || 'hvppyplug-main'
export const COLLECTIONS = {
  USERS: 'users',
  VENDORS: 'vendors',
  MENU_ITEMS: 'menu-items',
  ORDERS: 'orders',
  OTP_CODES: 'otp-codes',
  CATEGORIES: 'categories',
  REVIEWS: 'reviews',
  NOTIFICATIONS: 'notifications',
  ANALYTICS: 'analytics',
} as const

// Storage bucket IDs
export const BUCKETS = {
  IMAGES: Constants.expoConfig?.extra?.appwriteStorageId || 'images',
  DOCUMENTS: 'documents',
} as const

// Function IDs
export const FUNCTIONS_ID = {
  HVPPY_API: 'hvppy-api',
  SEND_NOTIFICATION: 'send-notification',
  PROCESS_ORDER: 'process-order',
  GENERATE_ANALYTICS: 'generate-analytics',
} as const
