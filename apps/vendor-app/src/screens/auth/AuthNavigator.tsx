/**
 * Vendor App Authentication Navigator
 * HVPPYPlug+ Vendor Authentication Flow
 */

import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {
  WelcomeScreen,
  PhoneInputScreen,
  OTPVerificationScreen,
  UserRegistrationScreen,
  LoginScreen,
  PasswordResetScreen,
  AuthStackParamList,
  UserRole
} from '@hvppyplug/ui-components-v2/auth'

const Stack = createNativeStackNavigator<AuthStackParamList>()

interface AuthNavigatorProps {
  onAuthComplete: (user: any) => void
}

export const AuthNavigator: React.FC<AuthNavigatorProps> = ({ onAuthComplete }) => {
  const vendorRole: UserRole = 'vendor'
  
  // Vendor app branding colors
  const primaryColor = '#4ECDC4'
  const secondaryColor = '#6ED5CE'

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right'
      }}
      initialRouteName="Welcome"
    >
      <Stack.Screen name="Welcome">
        {({ navigation }) => (
          <WelcomeScreen
            onRoleSelect={(role) => {
              // For vendor app, always use vendor role
              navigation.navigate('PhoneInput', { role: vendorRole })
            }}
            onSkip={() => {
              navigation.navigate('PhoneInput', { role: vendorRole })
            }}
            appName="HVPPYPlug+ Vendor"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PhoneInput">
        {({ navigation, route }) => (
          <PhoneInputScreen
            onPhoneSubmit={(phone) => {
              navigation.navigate('OTPVerification', { 
                phone, 
                type: 'verification' 
              })
            }}
            onBack={() => navigation.goBack()}
            role={vendorRole}
            title="Register Your Business"
            subtitle="Enter your phone number to start selling on HVPPYPlug+"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="OTPVerification">
        {({ navigation, route }) => (
          <OTPVerificationScreen
            phone={route.params.phone}
            type={route.params.type}
            onVerificationSuccess={(phone, code) => {
              // Check if user exists or needs registration
              navigation.navigate('Registration', { 
                phone, 
                role: vendorRole 
              })
            }}
            onBack={() => navigation.goBack()}
            title="Verify Your Business Phone"
            subtitle="Enter the 6-digit code sent to your business phone"
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Registration">
        {({ navigation, route }) => (
          <UserRegistrationScreen
            phone={route.params.phone}
            role={route.params.role}
            onRegistrationSuccess={(user) => {
              // Show success message for vendor registration
              // Vendors need approval before they can start selling
              onAuthComplete(user)
            }}
            onBack={() => navigation.goBack()}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="Login">
        {({ navigation, route }) => (
          <LoginScreen
            onLoginSuccess={(user) => {
              onAuthComplete(user)
            }}
            onForgotPassword={(phone) => {
              navigation.navigate('PasswordReset', { phone })
            }}
            onSignUp={() => {
              navigation.navigate('PhoneInput', { role: vendorRole })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>

      <Stack.Screen name="PasswordReset">
        {({ navigation, route }) => (
          <PasswordResetScreen
            onResetSuccess={() => {
              navigation.navigate('Login', { phone: route.params?.phone })
            }}
            onBack={() => navigation.goBack()}
            initialPhone={route.params?.phone}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
}

export default AuthNavigator
