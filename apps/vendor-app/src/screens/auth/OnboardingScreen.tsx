import React, { useState } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useNavigation } from '@react-navigation/native'
import { Store, Users, TrendingUp, ArrowRight } from 'lucide-react-native'
import { useAuthStore } from '../../stores/authStore'
import { useTheme } from '../../hooks/useTheme'

const { width } = Dimensions.get('window')

const ONBOARDING_SLIDES = [
  {
    id: 1,
    icon: Store,
    title: 'Manage Your Store',
    description: 'Take control of your digital storefront with our comprehensive vendor dashboard. Update your menu, track orders, and manage your business all in one place.',
  },
  {
    id: 2,
    icon: Users,
    title: 'Connect with Customers',
    description: 'Reach more customers in your area and build lasting relationships. Get real-time notifications for new orders and communicate directly with your customers.',
  },
  {
    id: 3,
    icon: TrendingUp,
    title: 'Grow Your Business',
    description: 'Access detailed analytics and insights to understand your performance. Track sales, monitor popular items, and make data-driven decisions to grow your business.',
  },
]

export function OnboardingScreen() {
  const navigation = useNavigation()
  const { setOnboardingCompleted } = useAuthStore()
  const { colors } = useTheme()
  
  const [currentSlide, setCurrentSlide] = useState(0)

  const handleNext = () => {
    if (currentSlide < ONBOARDING_SLIDES.length - 1) {
      setCurrentSlide(currentSlide + 1)
    } else {
      handleGetStarted()
    }
  }

  const handleSkip = () => {
    handleGetStarted()
  }

  const handleGetStarted = () => {
    setOnboardingCompleted(true)
    // Navigation will be handled by the auth state change in RootNavigator
  }

  const slide = ONBOARDING_SLIDES[currentSlide]
  const IconComponent = slide.icon

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Skip Button */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleSkip}>
          <Text style={[styles.skipText, { color: colors.textSecondary }]}>
            Skip
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: colors.primary[100] }]}>
          <IconComponent size={64} color={colors.primary[600]} />
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: colors.text }]}>
          {slide.title}
        </Text>

        {/* Description */}
        <Text style={[styles.description, { color: colors.textSecondary }]}>
          {slide.description}
        </Text>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        {/* Pagination Dots */}
        <View style={styles.pagination}>
          {ONBOARDING_SLIDES.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  backgroundColor: index === currentSlide 
                    ? colors.primary[600] 
                    : colors.gray[300],
                }
              ]}
            />
          ))}
        </View>

        {/* Next Button */}
        <TouchableOpacity
          style={[styles.nextButton, { backgroundColor: colors.primary[600] }]}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentSlide === ONBOARDING_SLIDES.length - 1 ? 'Get Started' : 'Next'}
          </Text>
          <ArrowRight size={20} color="#ffffff" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    maxWidth: width * 0.8,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    borderRadius: 12,
    paddingHorizontal: 24,
  },
  nextButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
})
