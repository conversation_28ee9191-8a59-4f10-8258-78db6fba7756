import React, { useState, useRef } from 'react'
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, Keyboard } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView } from 'moti'
import { Phone, ArrowRight, AlertCircle } from 'lucide-react-native'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { useAppwrite } from '../../providers/AppwriteProvider'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

export function LoginScreen() {
  const navigation = useNavigation()
  const { login, isLoading } = useAppwrite()
  const { colors } = useTheme()

  const [phone, setPhone] = useState('')
  const [errors, setErrors] = useState<{ phone?: string }>({})
  const [isValidating, setIsValidating] = useState(false)
  const phoneInputRef = useRef<TextInput>(null)

  const validatePhone = (phoneNumber: string) => {
    // South African phone number validation
    const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/
    return phoneRegex.test(phoneNumber.replace(/\s/g, ''))
  }

  const formatPhone = (phoneNumber: string) => {
    // Remove all non-digits
    const cleaned = phoneNumber.replace(/\D/g, '')

    // Format as South African number
    if (cleaned.startsWith('27')) {
      return `+${cleaned}`
    } else if (cleaned.startsWith('0')) {
      return `+27${cleaned.slice(1)}`
    } else if (cleaned.length === 9) {
      return `+27${cleaned}`
    }

    return phoneNumber
  }

  const handlePhoneChange = (text: string) => {
    setPhone(text)
    // Clear errors when user starts typing
    if (errors.phone) {
      setErrors({})
    }
  }

  const handleLogin = async () => {
    setErrors({})
    setIsValidating(true)

    // Dismiss keyboard
    Keyboard.dismiss()

    const formattedPhone = formatPhone(phone)

    if (!validatePhone(formattedPhone)) {
      setErrors({ phone: 'Please enter a valid South African phone number' })
      setIsValidating(false)
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
      phoneInputRef.current?.focus()
      return
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
      await login(formattedPhone)

      // Navigate to OTP verification
      navigation.navigate('OTPVerification', {
        phone: formattedPhone,
        type: 'login'
      } as never)
    } catch (error: any) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
      Alert.alert('Login Failed', error.message || 'Failed to send OTP. Please try again.')
    } finally {
      setIsValidating(false)
    }
  }

  const handleRegister = () => {
    navigation.navigate('Register' as never)
  }

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword' as never)
  }

  const isButtonDisabled = isLoading || isValidating || !phone.trim()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        {/* Header */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600 }}
          style={styles.header}
        >
          <Text style={[styles.title, { color: colors.text }]}>
            Welcome Back
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Sign in to manage your store
          </Text>
        </MotiView>

        {/* Form */}
        <MotiView
          from={{ opacity: 0, translateY: 30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 200 }}
          style={styles.form}
        >
          <View style={styles.inputContainer}>
            <Text style={[styles.label, { color: colors.text }]}>
              Phone Number
            </Text>
            <MotiView
              animate={{
                borderColor: errors.phone ? colors.error[500] : colors.border,
                scale: errors.phone ? 1.02 : 1,
              }}
              transition={{ type: 'timing', duration: 200 }}
              style={[
                styles.inputWrapper,
                { backgroundColor: colors.surface }
              ]}
            >
              <Phone size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                ref={phoneInputRef}
                style={[styles.input, { color: colors.text }]}
                placeholder="Enter your phone number"
                placeholderTextColor={colors.textSecondary}
                value={phone}
                onChangeText={handlePhoneChange}
                keyboardType="phone-pad"
                autoComplete="tel"
                textContentType="telephoneNumber"
                returnKeyType="done"
                onSubmitEditing={handleLogin}
                editable={!isLoading && !isValidating}
              />
            </MotiView>
            {errors.phone && (
              <MotiView
                from={{ opacity: 0, translateX: -10 }}
                animate={{ opacity: 1, translateX: 0 }}
                transition={{ type: 'spring', damping: 15 }}
                style={styles.errorContainer}
              >
                <AlertCircle size={16} color={colors.error[500]} />
                <Text style={[styles.errorText, { color: colors.error[500] }]}>
                  {errors.phone}
                </Text>
              </MotiView>
            )}
          </View>

          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', delay: 400 }}
          >
            <AnimatedButton
              title={isLoading ? 'Sending OTP...' : 'Send OTP'}
              onPress={handleLogin}
              loading={isLoading || isValidating}
              disabled={isButtonDisabled}
              icon={<ArrowRight size={20} color="#ffffff" />}
              iconPosition="right"
              fullWidth
              style={styles.loginButton}
            />
          </MotiView>
        </MotiView>

        {/* Footer */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 600 }}
          style={styles.footer}
        >
          <TouchableOpacity
            onPress={handleForgotPassword}
            style={styles.linkButton}
          >
            <Text style={[styles.linkText, { color: colors.primary[600] }]}>
              Forgot your password?
            </Text>
          </TouchableOpacity>

          <View style={styles.registerContainer}>
            <Text style={[styles.registerText, { color: colors.textSecondary }]}>
              Don't have an account?{' '}
            </Text>
            <TouchableOpacity
              onPress={handleRegister}
              style={styles.linkButton}
            >
              <Text style={[styles.linkText, { color: colors.primary[600] }]}>
                Sign up
              </Text>
            </TouchableOpacity>
          </View>
        </MotiView>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 6,
    flex: 1,
  },
  loginButton: {
    marginTop: 8,
  },
  footer: {
    alignItems: 'center',
  },
  linkButton: {
    padding: 8,
  },
  linkText: {
    fontSize: 16,
    fontWeight: '600',
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  registerText: {
    fontSize: 16,
  },
})
