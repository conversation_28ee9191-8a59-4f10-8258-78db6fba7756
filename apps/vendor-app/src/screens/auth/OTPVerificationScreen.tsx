import React, { useState, useEffect, useRef } from 'react'
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { ArrowLeft, Check } from 'lucide-react-native'
import { useAppwrite } from '../../providers/AppwriteProvider'
import { useTheme } from '../../hooks/useTheme'

interface RouteParams {
  phone: string
  type: 'login' | 'register' | 'forgot-password'
}

export function OTPVerificationScreen() {
  const navigation = useNavigation()
  const route = useRoute()
  const { phone, type } = route.params as RouteParams
  const { verifyOTP, login, isLoading } = useAppwrite()
  const { colors } = useTheme()
  
  const [otp, setOtp] = useState(['', '', '', '', '', ''])
  const [timer, setTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const inputRefs = useRef<(TextInput | null)[]>([])

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) {
      // Handle paste
      const pastedOtp = value.slice(0, 6).split('')
      const newOtp = [...otp]
      pastedOtp.forEach((digit, i) => {
        if (i < 6) newOtp[i] = digit
      })
      setOtp(newOtp)
      
      // Focus last filled input or next empty
      const nextIndex = Math.min(pastedOtp.length, 5)
      inputRefs.current[nextIndex]?.focus()
      return
    }

    const newOtp = [...otp]
    newOtp[index] = value
    setOtp(newOtp)

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handleVerifyOTP = async () => {
    const otpCode = otp.join('')
    
    if (otpCode.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter the complete 6-digit code')
      return
    }

    try {
      await verifyOTP(phone, otpCode)
      // Navigation will be handled by the auth state change
    } catch (error: any) {
      Alert.alert('Verification Failed', error.message || 'Invalid OTP. Please try again.')
      // Clear OTP on error
      setOtp(['', '', '', '', '', ''])
      inputRefs.current[0]?.focus()
    }
  }

  const handleResendOTP = async () => {
    if (!canResend) return

    try {
      await login(phone)
      setTimer(60)
      setCanResend(false)
      Alert.alert('OTP Sent', 'A new verification code has been sent to your phone')
    } catch (error: any) {
      Alert.alert('Resend Failed', error.message || 'Failed to resend OTP. Please try again.')
    }
  }

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const isOtpComplete = otp.every(digit => digit !== '')

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Title */}
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            Verify Your Phone
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            We've sent a 6-digit code to{'\n'}
            <Text style={{ fontWeight: '600' }}>{phone}</Text>
          </Text>
        </View>

        {/* OTP Input */}
        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              style={[
                styles.otpInput,
                {
                  borderColor: digit ? colors.primary[600] : colors.border,
                  backgroundColor: colors.surface,
                  color: colors.text,
                }
              ]}
              value={digit}
              onChangeText={(value) => handleOtpChange(value, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={6} // Allow paste
              textAlign="center"
              selectTextOnFocus
            />
          ))}
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          style={[
            styles.verifyButton,
            {
              backgroundColor: isOtpComplete ? colors.primary[600] : colors.gray[300],
              opacity: isLoading ? 0.7 : 1,
            }
          ]}
          onPress={handleVerifyOTP}
          disabled={!isOtpComplete || isLoading}
        >
          <Text style={[
            styles.verifyButtonText,
            { color: isOtpComplete ? '#ffffff' : colors.gray[500] }
          ]}>
            {isLoading ? 'Verifying...' : 'Verify'}
          </Text>
          {isOtpComplete && <Check size={20} color="#ffffff" />}
        </TouchableOpacity>

        {/* Resend */}
        <View style={styles.resendContainer}>
          <Text style={[styles.resendText, { color: colors.textSecondary }]}>
            Didn't receive the code?
          </Text>
          
          {canResend ? (
            <TouchableOpacity onPress={handleResendOTP}>
              <Text style={[styles.resendButton, { color: colors.primary[600] }]}>
                Resend OTP
              </Text>
            </TouchableOpacity>
          ) : (
            <Text style={[styles.timerText, { color: colors.textSecondary }]}>
              Resend in {formatTimer(timer)}
            </Text>
          )}
        </View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  otpInput: {
    width: 48,
    height: 56,
    borderWidth: 2,
    borderRadius: 12,
    fontSize: 24,
    fontWeight: '600',
  },
  verifyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    borderRadius: 12,
    marginBottom: 32,
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  resendContainer: {
    alignItems: 'center',
  },
  resendText: {
    fontSize: 16,
    marginBottom: 8,
  },
  resendButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  timerText: {
    fontSize: 16,
    fontWeight: '500',
  },
})
