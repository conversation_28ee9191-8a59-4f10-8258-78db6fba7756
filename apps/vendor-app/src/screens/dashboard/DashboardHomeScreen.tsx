import React, { useState, useEffect, useCallback } from 'react'
import { View, ScrollView, RefreshControl, Text, StyleSheet, Alert } from 'react-native'
import { useNavigation, useFocusEffect } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView, AnimatePresence } from 'moti'
import { Store, TrendingUp, TrendingDown, Clock, Package, DollarSign } from 'lucide-react-native'
import { AnimatedCard } from '../../components/ui/AnimatedCard'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { SkeletonCard } from '../../components/ui/SkeletonLoader'
import { EnhancedOrderCard } from '../../components/business/EnhancedOrderCard'
import { useAuthStore } from '../../stores/authStore'
import { orderService } from '../../services/orderService'
import { vendorService } from '../../services/vendorService'
import { notificationService } from '../../services/notificationService'
import { useTheme } from '../../hooks/useTheme'
import type { Order } from '../../services/orderService'
import * as Haptics from 'expo-haptics'

export function DashboardHomeScreen() {
  const navigation = useNavigation()
  const { vendor, user, setVendor } = useAuthStore()
  const { colors } = useTheme()

  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [storeToggling, setStoreToggling] = useState(false)
  const [stats, setStats] = useState({
    todayOrders: 0,
    todayRevenue: 0,
    avgRating: 0,
    activeMenuItems: 0,
    totalMenuItems: 0,
    ordersChange: 0,
    revenueChange: 0,
  })
  const [recentOrders, setRecentOrders] = useState<Order[]>([])
  const [error, setError] = useState<string | null>(null)

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (vendor) {
        loadDashboardData()
      }
    }, [vendor])
  )

  const loadDashboardData = async () => {
    if (!vendor) return

    try {
      setLoading(true)
      setError(null)

      // Load dashboard stats and recent orders in parallel
      const [dashboardStats, orders] = await Promise.all([
        vendorService.getDashboardStats(vendor.id),
        orderService.getVendorOrders(vendor.id, undefined, 10, 0)
      ])

      setStats(dashboardStats)
      setRecentOrders(orders)
    } catch (error: any) {
      console.error('Failed to load dashboard data:', error)
      setError(error.message || 'Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  const handleNavigate = (route: string, params?: any) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)

    switch (route) {
      case '/orders':
        navigation.navigate('Orders' as never)
        break
      case '/menu':
        navigation.navigate('Menu' as never)
        break
      case '/analytics':
        navigation.navigate('Analytics' as never)
        break
      case '/profile':
        navigation.navigate('Profile' as never)
        break
      case '/notifications':
        // Handle notifications navigation
        Alert.alert('Coming Soon', 'Notifications center will be available in the next update.')
        break
      case '/settings':
        navigation.navigate('Profile', { screen: 'StoreSettings' } as never)
        break
      case '/order':
        if (params?.orderId) {
          navigation.navigate('Orders', {
            screen: 'OrderDetails',
            params: { orderId: params.orderId }
          } as never)
        }
        break
      default:
        console.log('Unknown route:', route)
    }
  }

  const handleToggleStoreStatus = async (isOpen: boolean) => {
    if (!vendor || storeToggling) return

    try {
      setStoreToggling(true)
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)

      await vendorService.toggleStoreStatus(vendor.id, isOpen)

      // Update local vendor state
      setVendor({ ...vendor, isOpen })

      // Show success notification
      await notificationService.sendLocalNotification(
        'Store Status Updated',
        `Your store is now ${isOpen ? 'open' : 'closed'}`,
        {
          type: 'order_update',
          message: `Store ${isOpen ? 'opened' : 'closed'}`,
        }
      )
    } catch (error: any) {
      console.error('Failed to toggle store status:', error)
      Alert.alert(
        'Error',
        error.message || 'Failed to update store status. Please try again.',
        [{ text: 'OK' }]
      )
    } finally {
      setStoreToggling(false)
    }
  }

  const handleAcceptOrder = async (orderId: string) => {
    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success)

      // In a real implementation, you would show a time picker here
      // For now, we'll use a default time
      const estimatedTime = '30-45 minutes'

      await orderService.acceptOrder(orderId, estimatedTime)

      // Refresh orders list
      const updatedOrders = await orderService.getVendorOrders(vendor?.id || '', undefined, 10, 0)
      setRecentOrders(updatedOrders)

      // Show success notification
      await notificationService.sendLocalNotification(
        'Order Accepted',
        `Order #${orderId.slice(-6)} has been accepted`,
        {
          type: 'order_update',
          orderId,
          message: 'Order accepted successfully',
        }
      )
    } catch (error: any) {
      console.error('Failed to accept order:', error)
      Alert.alert(
        'Error',
        error.message || 'Failed to accept order. Please try again.',
        [{ text: 'OK' }]
      )
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning)

      // In a real implementation, you would show a reason picker here
      const reason = 'Unable to fulfill order at this time'

      await orderService.declineOrder(orderId, reason)

      // Refresh orders list
      const updatedOrders = await orderService.getVendorOrders(vendor?.id || '', undefined, 10, 0)
      setRecentOrders(updatedOrders)

      // Show notification
      await notificationService.sendLocalNotification(
        'Order Declined',
        `Order #${orderId.slice(-6)} has been declined`,
        {
          type: 'order_update',
          orderId,
          message: 'Order declined',
        }
      )
    } catch (error: any) {
      console.error('Failed to decline order:', error)
      Alert.alert(
        'Error',
        error.message || 'Failed to decline order. Please try again.',
        [{ text: 'OK' }]
      )
    }
  }

  // Loading state
  if (loading && !vendor) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading dashboard...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  // Error state
  if (error && !vendor) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: colors.error[600] }]}>
            Unable to load dashboard
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <AnimatedButton
            title="Try Again"
            onPress={loadDashboardData}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    )
  }

  if (!vendor || !user) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary[600]}
            colors={[colors.primary[600]]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <MotiView
          from={{ opacity: 0, translateY: -20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View>
              <Text style={[styles.greeting, { color: colors.textSecondary }]}>
                Good {getGreeting()}, {user.name}
              </Text>
              <Text style={[styles.storeName, { color: colors.text }]}>
                {vendor.name}
              </Text>
            </View>

            <AnimatedButton
              title={vendor.isOpen ? 'Open' : 'Closed'}
              variant={vendor.isOpen ? 'primary' : 'outline'}
              size="small"
              onPress={() => handleToggleStoreStatus(!vendor.isOpen)}
              loading={storeToggling}
              icon={<Store size={16} color={vendor.isOpen ? '#ffffff' : colors.primary[600]} />}
            />
          </View>
        </MotiView>

        {/* Stats Cards */}
        <View style={styles.statsGrid}>
          <StatCard
            title="Today's Orders"
            value={stats.todayOrders.toString()}
            change={stats.ordersChange}
            icon={<Package size={20} color={colors.primary[600]} />}
            delay={100}
          />
          <StatCard
            title="Revenue"
            value={`R${stats.todayRevenue.toFixed(0)}`}
            change={stats.revenueChange}
            icon={<DollarSign size={20} color={colors.success[600]} />}
            delay={200}
          />
          <StatCard
            title="Rating"
            value={stats.avgRating.toFixed(1)}
            icon={<TrendingUp size={20} color={colors.warning[600]} />}
            delay={300}
          />
          <StatCard
            title="Menu Items"
            value={`${stats.activeMenuItems}/${stats.totalMenuItems}`}
            subtitle="Active"
            icon={<Clock size={20} color={colors.gray[600]} />}
            delay={400}
          />
        </View>

        {/* Recent Orders */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 500 }}
          style={styles.ordersSection}
        >
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Recent Orders
            </Text>
            <AnimatedButton
              title="View All"
              variant="ghost"
              size="small"
              onPress={() => handleNavigate('/orders')}
            />
          </View>

          {loading ? (
            <View style={styles.ordersLoading}>
              {Array.from({ length: 3 }).map((_, index) => (
                <SkeletonCard key={index} showAvatar lines={3} />
              ))}
            </View>
          ) : recentOrders.length === 0 ? (
            <AnimatedCard variant="glass" style={styles.emptyOrders}>
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                No recent orders
              </Text>
              <Text style={[styles.emptyMessage, { color: colors.textSecondary }]}>
                Orders will appear here when customers place them
              </Text>
            </AnimatedCard>
          ) : (
            <View style={styles.ordersList}>
              {recentOrders.slice(0, 5).map((order, index) => (
                <EnhancedOrderCard
                  key={order.id}
                  order={order}
                  onPress={() => handleNavigate('/order', { orderId: order.id })}
                  onAccept={handleAcceptOrder}
                  onDecline={handleDeclineOrder}
                  showActions={order.status === 'pending'}
                  delay={index * 100}
                />
              ))}
            </View>
          )}
        </MotiView>
      </ScrollView>
    </SafeAreaView>
  )

// Helper function to get greeting based on time of day
function getGreeting() {
  const hour = new Date().getHours()
  if (hour < 12) return 'morning'
  if (hour < 18) return 'afternoon'
  return 'evening'
}

// Stat Card Component
interface StatCardProps {
  title: string
  value: string
  change?: number
  subtitle?: string
  icon?: React.ReactNode
  delay?: number
}

function StatCard({ title, value, change, subtitle, icon, delay = 0 }: StatCardProps) {
  const { colors } = useTheme()

  return (
    <AnimatedCard
      variant="glass"
      style={styles.statCard}
      delay={delay}
    >
      <View style={styles.statHeader}>
        <Text style={[styles.statTitle, { color: colors.textSecondary }]}>
          {title}
        </Text>
        {icon}
      </View>

      <Text style={[styles.statValue, { color: colors.text }]}>
        {value}
      </Text>

      {subtitle && (
        <Text style={[styles.statSubtitle, { color: colors.textSecondary }]}>
          {subtitle}
        </Text>
      )}

      {change !== undefined && (
        <View style={[
          styles.changeIndicator,
          {
            backgroundColor: change >= 0
              ? `${colors.success[500]}20`
              : `${colors.error[500]}20`
          }
        ]}>
          {change >= 0 ? (
            <TrendingUp size={12} color={colors.success[500]} />
          ) : (
            <TrendingDown size={12} color={colors.error[500]} />
          )}
          <Text style={[
            styles.changeText,
            {
              color: change >= 0
                ? colors.success[500]
                : colors.error[500]
            }
          ]}>
            {Math.abs(change)}%
          </Text>
        </View>
      )}
    </AnimatedCard>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 14,
    marginBottom: 4,
  },
  storeName: {
    fontSize: 24,
    fontWeight: '700',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginTop: 8,
  },
  statCard: {
    width: '46%',
    margin: '2%',
    padding: 12,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  changeText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 2,
  },
  ordersSection: {
    marginTop: 16,
    paddingHorizontal: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  ordersList: {
    marginTop: 8,
  },
  ordersLoading: {
    marginTop: 8,
  },
  emptyOrders: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
})
