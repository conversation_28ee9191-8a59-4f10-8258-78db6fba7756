import React, { useState } from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView } from 'moti'
import {
  User,
  Store,
  Clock,
  Truck,
  CreditCard,
  Bell,
  Settings,
  HelpCircle,
  LogOut,
  ChevronRight,
  Star,
  MapPin,
  Phone,
  Mail,
  Edit
} from 'lucide-react-native'
import { AnimatedCard } from '../../components/ui/AnimatedCard'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { useAuthStore } from '../../stores/authStore'
import { useAppwrite } from '../../providers/AppwriteProvider'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

interface SettingsItemProps {
  icon: React.ReactNode
  title: string
  subtitle?: string
  onPress: () => void
  showChevron?: boolean
  delay?: number
}

function SettingsItem({ icon, title, subtitle, onPress, showChevron = true, delay = 0 }: SettingsItemProps) {
  const { colors } = useTheme()

  return (
    <MotiView
      from={{ opacity: 0, translateX: -20 }}
      animate={{ opacity: 1, translateX: 0 }}
      transition={{ type: 'timing', duration: 300, delay }}
    >
      <TouchableOpacity
        style={[styles.settingsItem, { borderBottomColor: colors.border }]}
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
          onPress()
        }}
      >
        <View style={styles.settingsItemLeft}>
          <View style={[styles.settingsIcon, { backgroundColor: `${colors.primary[600]}15` }]}>
            {icon}
          </View>
          <View style={styles.settingsContent}>
            <Text style={[styles.settingsTitle, { color: colors.text }]}>
              {title}
            </Text>
            {subtitle && (
              <Text style={[styles.settingsSubtitle, { color: colors.textSecondary }]}>
                {subtitle}
              </Text>
            )}
          </View>
        </View>
        {showChevron && (
          <ChevronRight size={20} color={colors.textSecondary} />
        )}
      </TouchableOpacity>
    </MotiView>
  )
}

export function ProfileHomeScreen() {
  const navigation = useNavigation()
  const { user, vendor } = useAuthStore()
  const { logout } = useAppwrite()
  const { colors } = useTheme()
  const [loggingOut, setLoggingOut] = useState(false)

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoggingOut(true)
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning)
              await logout()
            } catch (error) {
              console.error('Logout error:', error)
            } finally {
              setLoggingOut(false)
            }
          },
        },
      ]
    )
  }

  const handleEditProfile = () => {
    // In a real app, this would navigate to an edit profile screen
    Alert.alert('Coming Soon', 'Profile editing will be available in the next update.')
  }

  if (!user || !vendor) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading profile...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Header */}
        <MotiView
          from={{ opacity: 0, translateY: -20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400 }}
        >
          <AnimatedCard variant="glass" style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.profileImageContainer}>
                {vendor.imageUrl ? (
                  <Image
                    source={{ uri: vendor.imageUrl }}
                    style={styles.profileImage}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: colors.primary[100] }]}>
                    <Store size={32} color={colors.primary[600]} />
                  </View>
                )}
                <TouchableOpacity
                  style={[styles.editButton, { backgroundColor: colors.primary[600] }]}
                  onPress={handleEditProfile}
                >
                  <Edit size={16} color="#ffffff" />
                </TouchableOpacity>
              </View>

              <View style={styles.profileInfo}>
                <Text style={[styles.storeName, { color: colors.text }]}>
                  {vendor.name}
                </Text>
                <Text style={[styles.ownerName, { color: colors.textSecondary }]}>
                  {user.name}
                </Text>

                <View style={styles.profileStats}>
                  <View style={styles.statItem}>
                    <Star size={16} color={colors.warning[500]} />
                    <Text style={[styles.statText, { color: colors.text }]}>
                      {vendor.rating.toFixed(1)}
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <MapPin size={16} color={colors.textSecondary} />
                    <Text style={[styles.statText, { color: colors.textSecondary }]}>
                      {vendor.location.address.split(',')[0]}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Phone size={16} color={colors.textSecondary} />
                <Text style={[styles.contactText, { color: colors.textSecondary }]}>
                  {user.phone}
                </Text>
              </View>
              {user.email && (
                <View style={styles.contactItem}>
                  <Mail size={16} color={colors.textSecondary} />
                  <Text style={[styles.contactText, { color: colors.textSecondary }]}>
                    {user.email}
                  </Text>
                </View>
              )}
            </View>
          </AnimatedCard>
        </MotiView>

        {/* Store Settings */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 200 }}
        >
          <AnimatedCard variant="elevated" style={styles.settingsCard}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Store Settings
            </Text>

            <SettingsItem
              icon={<Store size={20} color={colors.primary[600]} />}
              title="Store Information"
              subtitle="Update store details and description"
              onPress={() => navigation.navigate('StoreSettings' as never)}
              delay={300}
            />

            <SettingsItem
              icon={<Clock size={20} color={colors.primary[600]} />}
              title="Business Hours"
              subtitle="Set your operating hours"
              onPress={() => navigation.navigate('BusinessHours' as never)}
              delay={350}
            />

            <SettingsItem
              icon={<Truck size={20} color={colors.primary[600]} />}
              title="Delivery Settings"
              subtitle="Configure delivery options"
              onPress={() => navigation.navigate('DeliverySettings' as never)}
              delay={400}
            />

            <SettingsItem
              icon={<CreditCard size={20} color={colors.primary[600]} />}
              title="Payment Methods"
              subtitle="Manage accepted payments"
              onPress={() => navigation.navigate('PaymentSettings' as never)}
              delay={450}
            />
          </AnimatedCard>
        </MotiView>

        {/* Account Settings */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 400 }}
        >
          <AnimatedCard variant="elevated" style={styles.settingsCard}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Account Settings
            </Text>

            <SettingsItem
              icon={<Bell size={20} color={colors.primary[600]} />}
              title="Notifications"
              subtitle="Manage notification preferences"
              onPress={() => navigation.navigate('NotificationSettings' as never)}
              delay={500}
            />

            <SettingsItem
              icon={<User size={20} color={colors.primary[600]} />}
              title="Account Information"
              subtitle="Update personal details"
              onPress={() => navigation.navigate('AccountSettings' as never)}
              delay={550}
            />

            <SettingsItem
              icon={<Settings size={20} color={colors.primary[600]} />}
              title="App Settings"
              subtitle="Theme, language, and preferences"
              onPress={() => Alert.alert('Coming Soon', 'App settings will be available soon.')}
              delay={600}
            />
          </AnimatedCard>
        </MotiView>

        {/* Support & Help */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 600 }}
        >
          <AnimatedCard variant="elevated" style={styles.settingsCard}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Support & Help
            </Text>

            <SettingsItem
              icon={<HelpCircle size={20} color={colors.primary[600]} />}
              title="Help & Support"
              subtitle="Get help and contact support"
              onPress={() => navigation.navigate('Support' as never)}
              delay={650}
            />
          </AnimatedCard>
        </MotiView>

        {/* Logout Button */}
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', delay: 700 }}
          style={styles.logoutContainer}
        >
          <AnimatedButton
            title="Sign Out"
            variant="outline"
            icon={<LogOut size={20} color={colors.error[600]} />}
            onPress={handleLogout}
            loading={loggingOut}
            fullWidth
            style={[styles.logoutButton, { borderColor: colors.error[600] }]}
          />
        </MotiView>
      </ScrollView>
    </SafeAreaView>
  )
}

export function StoreSettingsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Store Settings
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Update your store information and preferences.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function BusinessHoursScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Business Hours
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Set your operating hours for each day of the week.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function DeliverySettingsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Delivery Settings
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Configure delivery radius, fees, and minimum order amounts.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function PaymentSettingsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Payment Settings
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Manage accepted payment methods and payout preferences.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function NotificationSettingsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Notification Settings
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Control which notifications you receive and how.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function AccountSettingsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Account Settings
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Manage your account information and security settings.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function SupportScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Support
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Get help and contact our support team.
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  profileCard: {
    padding: 20,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  storeName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  ownerName: {
    fontSize: 16,
    marginBottom: 8,
  },
  profileStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  statText: {
    fontSize: 14,
    marginLeft: 4,
  },
  contactInfo: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    marginLeft: 8,
  },
  settingsCard: {
    padding: 0,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    padding: 20,
    paddingBottom: 12,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingsContent: {
    flex: 1,
  },
  settingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingsSubtitle: {
    fontSize: 14,
  },
  logoutContainer: {
    marginTop: 16,
  },
  logoutButton: {
    borderWidth: 1,
  },
})
