import React, { useState, useEffect, useCallback } from 'react'
import { View, ScrollView, RefreshControl, Text, TouchableOpacity, StyleSheet, TextInput, Alert } from 'react-native'
import { useNavigation, useFocusEffect } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView, AnimatePresence } from 'moti'
import { Filter, Search, Bell, X } from 'lucide-react-native'
import { AnimatedCard } from '../../components/ui/AnimatedCard'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { SkeletonCard } from '../../components/ui/SkeletonLoader'
import { EnhancedOrderCard } from '../../components/business/EnhancedOrderCard'
import { useAuthStore } from '../../stores/authStore'
import { orderService, type Order } from '../../services/orderService'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

const ORDER_STATUSES = [
  { key: 'all', label: 'All Orders' },
  { key: 'pending', label: 'Pending' },
  { key: 'accepted', label: 'Accepted' },
  { key: 'preparing', label: 'Preparing' },
  { key: 'ready', label: 'Ready' },
  { key: 'picked_up', label: 'Picked Up' },
  { key: 'delivered', label: 'Delivered' },
  { key: 'cancelled', label: 'Cancelled' },
]

export function OrdersListScreen() {
  const navigation = useNavigation()
  const { vendor } = useAuthStore()
  const { colors } = useTheme()

  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (vendor) {
        loadOrders()
      }
    }, [vendor, selectedStatus])
  )

  const loadOrders = async () => {
    if (!vendor) return

    try {
      setLoading(true)
      setError(null)
      const status = selectedStatus === 'all' ? undefined : selectedStatus
      const ordersList = await orderService.getVendorOrders(vendor.id, status, 50, 0)
      setOrders(ordersList)
      setFilteredOrders(ordersList)
    } catch (error: any) {
      console.error('Failed to load orders:', error)
      setError(error.message || 'Failed to load orders')
    } finally {
      setLoading(false)
    }
  }

  // Filter orders based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredOrders(orders)
      return
    }

    const filtered = orders.filter(order =>
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerInfo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerInfo.phone.includes(searchQuery)
    )
    setFilteredOrders(filtered)
  }, [orders, searchQuery])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadOrders()
    setRefreshing(false)
  }

  const handleOrderPress = (orderId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    navigation.navigate('OrderDetails', { orderId } as never)
  }

  const handleAcceptOrder = async (orderId: string) => {
    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success)
      await orderService.acceptOrder(orderId, '30-45 minutes')
      await loadOrders() // Refresh list
    } catch (error: any) {
      console.error('Failed to accept order:', error)
      Alert.alert('Error', error.message || 'Failed to accept order')
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning)
      await orderService.declineOrder(orderId, 'Unable to fulfill order')
      await loadOrders() // Refresh list
    } catch (error: any) {
      console.error('Failed to decline order:', error)
      Alert.alert('Error', error.message || 'Failed to decline order')
    }
  }

  const handleToggleSearch = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setShowSearch(!showSearch)
    if (showSearch) {
      setSearchQuery('')
    }
  }

  // Loading state
  if (loading && orders.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Orders</Text>
        </View>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading orders...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  // Error state
  if (error && orders.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Orders</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: colors.error[600] }]}>
            Unable to load orders
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <AnimatedButton
            title="Try Again"
            onPress={loadOrders}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 400 }}
        style={[styles.header, { borderBottomColor: colors.border }]}
      >
        <Text style={[styles.headerTitle, { color: colors.text }]}>Orders</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleToggleSearch}
          >
            <Search size={24} color={colors.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Bell size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </MotiView>

      {/* Search Bar */}
      <AnimatePresence>
        {showSearch && (
          <MotiView
            from={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ type: 'timing', duration: 300 }}
            style={[styles.searchContainer, { backgroundColor: colors.surface }]}
          >
            <View style={[styles.searchInputContainer, { borderColor: colors.border }]}>
              <Search size={20} color={colors.textSecondary} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search orders by ID, customer name, or phone"
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <X size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
          </MotiView>
        )}
      </AnimatePresence>

      {/* Status Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}
      >
        {ORDER_STATUSES.map((status, index) => (
          <MotiView
            key={status.key}
            from={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', delay: index * 50 }}
          >
            <TouchableOpacity
              style={[
                styles.filterButton,
                {
                  backgroundColor: selectedStatus === status.key
                    ? colors.primary[600]
                    : colors.surface,
                  borderColor: selectedStatus === status.key
                    ? colors.primary[600]
                    : colors.border,
                }
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                setSelectedStatus(status.key)
              }}
            >
              <Text style={[
                styles.filterButtonText,
                {
                  color: selectedStatus === status.key
                    ? '#ffffff'
                    : colors.textSecondary
                }
              ]}>
                {status.label}
              </Text>
            </TouchableOpacity>
          </MotiView>
        ))}
      </ScrollView>

      {/* Orders List */}
      <ScrollView
        style={styles.ordersList}
        contentContainerStyle={styles.ordersContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary[600]}
            colors={[colors.primary[600]]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {loading && orders.length > 0 ? (
          <View style={styles.loadingOverlay}>
            <LoadingSpinner size="small" />
          </View>
        ) : filteredOrders.length === 0 ? (
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', delay: 200 }}
          >
            <AnimatedCard variant="glass" style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: colors.text }]}>
                {searchQuery ? 'No matching orders found' : 'No orders found'}
              </Text>
              <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Orders will appear here when customers place them'
                }
              </Text>
              {searchQuery && (
                <AnimatedButton
                  title="Clear Search"
                  variant="outline"
                  size="small"
                  onPress={() => setSearchQuery('')}
                  style={styles.clearSearchButton}
                />
              )}
            </AnimatedCard>
          </MotiView>
        ) : (
          <View style={styles.ordersContainer}>
            {filteredOrders.map((order, index) => (
              <EnhancedOrderCard
                key={order.id}
                order={order}
                onPress={handleOrderPress}
                onAccept={handleAcceptOrder}
                onDecline={handleDeclineOrder}
                showActions={order.status === 'pending'}
                delay={index * 50}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterContainer: {
    paddingVertical: 16,
  },
  filterContent: {
    paddingHorizontal: 24,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  ordersList: {
    flex: 1,
  },
  ordersContent: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  ordersContainer: {
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  loadingOverlay: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
  emptyContainer: {
    padding: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  clearSearchButton: {
    marginTop: 8,
  },
})
