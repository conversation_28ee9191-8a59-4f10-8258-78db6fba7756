import React, { useState, useEffect, useCallback } from 'react'
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Dimensions } from 'react-native'
import { useNavigation, useFocusEffect } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView } from 'moti'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit'
import { Calendar, TrendingUp, TrendingDown, DollarSign, Package, Users, Star, Clock } from 'lucide-react-native'
import { AnimatedCard } from '../../components/ui/AnimatedCard'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { useAuthStore } from '../../stores/authStore'
import { vendorService, type VendorAnalytics } from '../../services/vendorService'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

const { width: screenWidth } = Dimensions.get('window')

type TimePeriod = 'today' | 'week' | 'month' | 'custom'

export function AnalyticsHomeScreen() {
  const navigation = useNavigation()
  const { vendor } = useAuthStore()
  const { colors } = useTheme()

  const [analytics, setAnalytics] = useState<VendorAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('week')
  const [error, setError] = useState<string | null>(null)

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (vendor) {
        loadAnalytics()
      }
    }, [vendor, selectedPeriod])
  )

  const loadAnalytics = async () => {
    if (!vendor) return

    try {
      setLoading(true)
      setError(null)

      const analyticsData = await vendorService.getVendorAnalytics(vendor.id, selectedPeriod)
      setAnalytics(analyticsData)
    } catch (error: any) {
      console.error('Failed to load analytics:', error)
      setError(error.message || 'Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadAnalytics()
    setRefreshing(false)
  }

  const handlePeriodChange = (period: TimePeriod) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setSelectedPeriod(period)
  }

  // Chart configuration
  const chartConfig = {
    backgroundColor: colors.surface,
    backgroundGradientFrom: colors.surface,
    backgroundGradientTo: colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
    labelColor: (opacity = 1) => colors.textSecondary,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: colors.primary[600],
    },
  }

  // Sample data for charts (in a real app, this would come from analytics)
  const revenueData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [120, 180, 150, 220, 280, 350, 300],
        color: (opacity = 1) => `rgba(34, 197, 94, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  }

  const ordersData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [8, 12, 10, 15, 18, 22, 20],
      },
    ],
  }

  const topItemsData = analytics?.topSellingItems.slice(0, 5).map((item, index) => ({
    name: item.name.length > 15 ? item.name.substring(0, 15) + '...' : item.name,
    quantity: item.quantity,
    color: [
      colors.primary[600],
      colors.success[600],
      colors.warning[600],
      colors.error[600],
      colors.gray[600],
    ][index] || colors.gray[600],
    legendFontColor: colors.textSecondary,
    legendFontSize: 12,
  })) || []

  // Loading state
  if (loading && !analytics) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Analytics</Text>
        </View>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading analytics...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 400 }}
        style={[styles.header, { borderBottomColor: colors.border }]}
      >
        <Text style={[styles.headerTitle, { color: colors.text }]}>Analytics</Text>
        <TouchableOpacity style={styles.headerButton}>
          <Calendar size={24} color={colors.textSecondary} />
        </TouchableOpacity>
      </MotiView>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary[600]}
            colors={[colors.primary[600]]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Period Selector */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.periodContainer}
          contentContainerStyle={styles.periodContent}
        >
          {(['today', 'week', 'month'] as TimePeriod[]).map((period, index) => (
            <MotiView
              key={period}
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', delay: index * 50 }}
            >
              <TouchableOpacity
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriod === period
                      ? colors.primary[600]
                      : colors.surface,
                    borderColor: selectedPeriod === period
                      ? colors.primary[600]
                      : colors.border,
                  }
                ]}
                onPress={() => handlePeriodChange(period)}
              >
                <Text style={[
                  styles.periodButtonText,
                  {
                    color: selectedPeriod === period
                      ? '#ffffff'
                      : colors.textSecondary
                  }
                ]}>
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            </MotiView>
          ))}
        </ScrollView>

        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Total Revenue"
            value={`R${analytics?.totalRevenue.toFixed(0) || '0'}`}
            change={12.5}
            icon={<DollarSign size={20} color={colors.success[600]} />}
            delay={100}
          />
          <MetricCard
            title="Total Orders"
            value={analytics?.totalOrders.toString() || '0'}
            change={8.3}
            icon={<Package size={20} color={colors.primary[600]} />}
            delay={200}
          />
          <MetricCard
            title="Avg Order Value"
            value={`R${analytics?.averageOrderValue.toFixed(0) || '0'}`}
            change={-2.1}
            icon={<TrendingUp size={20} color={colors.warning[600]} />}
            delay={300}
          />
          <MetricCard
            title="Rating"
            value={analytics?.rating.toFixed(1) || '0.0'}
            subtitle={`${analytics?.reviewCount || 0} reviews`}
            icon={<Star size={20} color={colors.warning[500]} />}
            delay={400}
          />
        </View>

        {/* Revenue Chart */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 500 }}
        >
          <AnimatedCard variant="elevated" style={styles.chartCard}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Revenue Trend
            </Text>
            <LineChart
              data={revenueData}
              width={screenWidth - 80}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          </AnimatedCard>
        </MotiView>

        {/* Orders Chart */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 600 }}
        >
          <AnimatedCard variant="elevated" style={styles.chartCard}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Orders Overview
            </Text>
            <BarChart
              data={ordersData}
              width={screenWidth - 80}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
            />
          </AnimatedCard>
        </MotiView>

        {/* Top Selling Items */}
        {topItemsData.length > 0 && (
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 400, delay: 700 }}
          >
            <AnimatedCard variant="elevated" style={styles.chartCard}>
              <Text style={[styles.chartTitle, { color: colors.text }]}>
                Top Selling Items
              </Text>
              <PieChart
                data={topItemsData}
                width={screenWidth - 80}
                height={220}
                chartConfig={chartConfig}
                accessor="quantity"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
              />
            </AnimatedCard>
          </MotiView>
        )}

        {/* Quick Actions */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 400, delay: 800 }}
          style={styles.actionsContainer}
        >
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Detailed Reports
          </Text>

          <AnimatedButton
            title="Sales Report"
            variant="outline"
            icon={<TrendingUp size={20} color={colors.primary[600]} />}
            onPress={() => navigation.navigate('SalesReport' as never)}
            fullWidth
            style={styles.actionButton}
          />

          <AnimatedButton
            title="Product Performance"
            variant="outline"
            icon={<Package size={20} color={colors.primary[600]} />}
            onPress={() => navigation.navigate('ProductPerformance' as never)}
            fullWidth
            style={styles.actionButton}
          />

          <AnimatedButton
            title="Customer Insights"
            variant="outline"
            icon={<Users size={20} color={colors.primary[600]} />}
            onPress={() => navigation.navigate('CustomerInsights' as never)}
            fullWidth
            style={styles.actionButton}
          />
        </MotiView>
      </ScrollView>
    </SafeAreaView>
  )
// Metric Card Component
interface MetricCardProps {
  title: string
  value: string
  change?: number
  subtitle?: string
  icon?: React.ReactNode
  delay?: number
}

function MetricCard({ title, value, change, subtitle, icon, delay = 0 }: MetricCardProps) {
  const { colors } = useTheme()

  return (
    <AnimatedCard
      variant="glass"
      style={styles.metricCard}
      delay={delay}
    >
      <View style={styles.metricHeader}>
        <Text style={[styles.metricTitle, { color: colors.textSecondary }]}>
          {title}
        </Text>
        {icon}
      </View>

      <Text style={[styles.metricValue, { color: colors.text }]}>
        {value}
      </Text>

      {subtitle && (
        <Text style={[styles.metricSubtitle, { color: colors.textSecondary }]}>
          {subtitle}
        </Text>
      )}

      {change !== undefined && (
        <View style={[
          styles.changeIndicator,
          {
            backgroundColor: change >= 0
              ? `${colors.success[500]}20`
              : `${colors.error[500]}20`
          }
        ]}>
          {change >= 0 ? (
            <TrendingUp size={12} color={colors.success[500]} />
          ) : (
            <TrendingDown size={12} color={colors.error[500]} />
          )}
          <Text style={[
            styles.changeText,
            {
              color: change >= 0
                ? colors.success[500]
                : colors.error[500]
            }
          ]}>
            {Math.abs(change)}%
          </Text>
        </View>
      )}
    </AnimatedCard>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  headerButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  periodContainer: {
    paddingVertical: 16,
  },
  periodContent: {
    paddingHorizontal: 24,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginTop: 8,
  },
  metricCard: {
    width: '46%',
    margin: '2%',
    padding: 12,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  changeText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 2,
  },
  chartCard: {
    marginHorizontal: 24,
    marginVertical: 8,
    padding: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  actionsContainer: {
    paddingHorizontal: 24,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  actionButton: {
    marginBottom: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
})

export function SalesReportScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Sales Report
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Detailed sales reports and revenue analysis.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function ProductPerformanceScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Product Performance
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          See which menu items are performing best.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function CustomerInsightsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Customer Insights
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Understand your customer behavior and preferences.
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
