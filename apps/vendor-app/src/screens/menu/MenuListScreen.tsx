import React, { useState, useEffect, useCallback } from 'react'
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Alert, Image } from 'react-native'
import { useNavigation, useFocusEffect } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MotiView, AnimatePresence } from 'moti'
import { Plus, Search, Grid, List, MoreVertical, Eye, EyeOff, Edit, Trash2 } from 'lucide-react-native'
import { AnimatedCard } from '../../components/ui/AnimatedCard'
import { AnimatedButton } from '../../components/ui/AnimatedButton'
import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { SkeletonCard } from '../../components/ui/SkeletonLoader'
import { useAuthStore } from '../../stores/authStore'
import { menuService, type MenuItem, type MenuCategory } from '../../services/menuService'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

export function MenuListScreen() {
  const navigation = useNavigation()
  const { vendor } = useAuthStore()
  const { colors } = useTheme()

  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [categories, setCategories] = useState<MenuCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [error, setError] = useState<string | null>(null)

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (vendor) {
        loadMenuData()
      }
    }, [vendor])
  )

  const loadMenuData = async () => {
    if (!vendor) return

    try {
      setLoading(true)
      setError(null)

      const [items, cats] = await Promise.all([
        menuService.getMenuItems(vendor.id),
        menuService.getMenuCategories(vendor.id)
      ])

      setMenuItems(items)
      setCategories(cats)
    } catch (error: any) {
      console.error('Failed to load menu data:', error)
      setError(error.message || 'Failed to load menu data')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadMenuData()
    setRefreshing(false)
  }

  const handleToggleAvailability = async (itemId: string, available: boolean) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
      await menuService.toggleAvailability(itemId, available)

      // Update local state
      setMenuItems(items =>
        items.map(item =>
          item.id === itemId ? { ...item, available } : item
        )
      )
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update item availability')
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    Alert.alert(
      'Delete Menu Item',
      'Are you sure you want to delete this menu item? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning)
              await menuService.deleteMenuItem(itemId)
              setMenuItems(items => items.filter(item => item.id !== itemId))
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete menu item')
            }
          },
        },
      ]
    )
  }

  const filteredItems = selectedCategory === 'all'
    ? menuItems
    : menuItems.filter(item => item.category === selectedCategory)

  // Loading state
  if (loading && menuItems.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Menu</Text>
        </View>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading menu...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  // Error state
  if (error && menuItems.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Menu</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: colors.error[600] }]}>
            Unable to load menu
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <AnimatedButton
            title="Try Again"
            onPress={loadMenuData}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 400 }}
        style={[styles.header, { borderBottomColor: colors.border }]}
      >
        <Text style={[styles.headerTitle, { color: colors.text }]}>Menu</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? (
              <List size={24} color={colors.textSecondary} />
            ) : (
              <Grid size={24} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Search size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </MotiView>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary[600]}
            colors={[colors.primary[600]]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Add Item Button */}
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', delay: 100 }}
          style={styles.addButtonContainer}
        >
          <AnimatedButton
            title="Add Menu Item"
            variant="primary"
            icon={<Plus size={20} color="#ffffff" />}
            onPress={() => navigation.navigate('AddMenuItem' as never)}
            fullWidth
          />
        </MotiView>

        {/* Categories Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
          contentContainerStyle={styles.categoriesContent}
        >
          <MotiView
            from={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', delay: 200 }}
          >
            <TouchableOpacity
              style={[
                styles.categoryButton,
                {
                  backgroundColor: selectedCategory === 'all'
                    ? colors.primary[600]
                    : colors.surface,
                  borderColor: selectedCategory === 'all'
                    ? colors.primary[600]
                    : colors.border,
                }
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                setSelectedCategory('all')
              }}
            >
              <Text style={[
                styles.categoryButtonText,
                {
                  color: selectedCategory === 'all'
                    ? '#ffffff'
                    : colors.textSecondary
                }
              ]}>
                All Items ({menuItems.length})
              </Text>
            </TouchableOpacity>
          </MotiView>

          {categories.map((category, index) => {
            const itemCount = menuItems.filter(item => item.category === category.name).length
            return (
              <MotiView
                key={category.id}
                from={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', delay: 250 + (index * 50) }}
              >
                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    {
                      backgroundColor: selectedCategory === category.name
                        ? colors.primary[600]
                        : colors.surface,
                      borderColor: selectedCategory === category.name
                        ? colors.primary[600]
                        : colors.border,
                    }
                  ]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                    setSelectedCategory(category.name)
                  }}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    {
                      color: selectedCategory === category.name
                        ? '#ffffff'
                        : colors.textSecondary
                    }
                  ]}>
                    {category.name} ({itemCount})
                  </Text>
                </TouchableOpacity>
              </MotiView>
            )
          })}
        </ScrollView>

        {/* Menu Items */}
        {loading && menuItems.length > 0 ? (
          <View style={styles.loadingOverlay}>
            <LoadingSpinner size="small" />
          </View>
        ) : filteredItems.length === 0 ? (
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', delay: 300 }}
          >
            <AnimatedCard variant="glass" style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No menu items found
              </Text>
              <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                {selectedCategory === 'all'
                  ? 'Start by adding your first menu item'
                  : `No items in the ${selectedCategory} category`
                }
              </Text>
              <AnimatedButton
                title="Add Menu Item"
                variant="outline"
                size="small"
                onPress={() => navigation.navigate('AddMenuItem' as never)}
                style={styles.addItemButton}
              />
            </AnimatedCard>
          </MotiView>
        ) : (
          <View style={viewMode === 'grid' ? styles.gridContainer : styles.listContainer}>
            {filteredItems.map((item, index) => (
              <MenuItemCard
                key={item.id}
                item={item}
                viewMode={viewMode}
                onPress={() => navigation.navigate('MenuItemDetails', { itemId: item.id } as never)}
                onToggleAvailability={handleToggleAvailability}
                onEdit={() => navigation.navigate('EditMenuItem', { itemId: item.id } as never)}
                onDelete={handleDeleteItem}
                delay={index * 50}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
// Menu Item Card Component
interface MenuItemCardProps {
  item: MenuItem
  viewMode: 'grid' | 'list'
  onPress: () => void
  onToggleAvailability: (itemId: string, available: boolean) => void
  onEdit: () => void
  onDelete: (itemId: string) => void
  delay?: number
}

function MenuItemCard({
  item,
  viewMode,
  onPress,
  onToggleAvailability,
  onEdit,
  onDelete,
  delay = 0
}: MenuItemCardProps) {
  const { colors } = useTheme()
  const [showActions, setShowActions] = useState(false)

  const handleToggleActions = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setShowActions(!showActions)
  }

  const cardStyle = viewMode === 'grid' ? styles.gridCard : styles.listCard

  return (
    <AnimatedCard
      variant="elevated"
      onPress={onPress}
      delay={delay}
      style={cardStyle}
    >
      <View style={styles.cardContent}>
        {/* Image */}
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={viewMode === 'grid' ? styles.gridImage : styles.listImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[
            viewMode === 'grid' ? styles.gridImage : styles.listImage,
            styles.placeholderImage,
            { backgroundColor: colors.gray[200] }
          ]}>
            <Grid size={viewMode === 'grid' ? 32 : 24} color={colors.gray[400]} />
          </View>
        )}

        {/* Content */}
        <View style={styles.itemContent}>
          <View style={styles.itemHeader}>
            <Text
              style={[styles.itemName, { color: colors.text }]}
              numberOfLines={viewMode === 'grid' ? 2 : 1}
            >
              {item.name}
            </Text>
            <TouchableOpacity
              style={styles.moreButton}
              onPress={handleToggleActions}
            >
              <MoreVertical size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <Text
            style={[styles.itemDescription, { color: colors.textSecondary }]}
            numberOfLines={viewMode === 'grid' ? 3 : 2}
          >
            {item.description}
          </Text>

          <View style={styles.itemFooter}>
            <Text style={[styles.itemPrice, { color: colors.success[600] }]}>
              R{item.price.toFixed(2)}
            </Text>

            <View style={styles.availabilityContainer}>
              <TouchableOpacity
                style={[
                  styles.availabilityButton,
                  {
                    backgroundColor: item.available
                      ? `${colors.success[500]}20`
                      : `${colors.gray[500]}20`
                  }
                ]}
                onPress={() => onToggleAvailability(item.id, !item.available)}
              >
                {item.available ? (
                  <Eye size={16} color={colors.success[500]} />
                ) : (
                  <EyeOff size={16} color={colors.gray[500]} />
                )}
                <Text style={[
                  styles.availabilityText,
                  {
                    color: item.available
                      ? colors.success[500]
                      : colors.gray[500]
                  }
                ]}>
                  {item.available ? 'Available' : 'Hidden'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Action Menu */}
      <AnimatePresence>
        {showActions && (
          <MotiView
            from={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ type: 'timing', duration: 200 }}
            style={[styles.actionMenu, { backgroundColor: colors.surface, borderTopColor: colors.border }]}
          >
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                setShowActions(false)
                onEdit()
              }}
            >
              <Edit size={16} color={colors.primary[600]} />
              <Text style={[styles.actionButtonText, { color: colors.primary[600] }]}>
                Edit
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                setShowActions(false)
                onDelete(item.id)
              }}
            >
              <Trash2 size={16} color={colors.error[600]} />
              <Text style={[styles.actionButtonText, { color: colors.error[600] }]}>
                Delete
              </Text>
            </TouchableOpacity>
          </MotiView>
        )}
      </AnimatePresence>
    </AnimatedCard>
  )
}

export function MenuItemDetailsScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Menu Item Details
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          View and edit menu item details.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function AddMenuItemScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Add Menu Item
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Create a new menu item with photos, pricing, and details.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function EditMenuItemScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Edit Menu Item
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Update menu item information and availability.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function MenuCategoriesScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Menu Categories
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Organize your menu items into categories.
        </Text>
      </View>
    </SafeAreaView>
  )
}

export function ManageCategoryScreen() {
  const { colors } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          Manage Category
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Create or edit menu category.
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  addButtonContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesContent: {
    paddingHorizontal: 24,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginTop: 8,
  },
  listContainer: {
    paddingHorizontal: 24,
    marginTop: 8,
  },
  gridCard: {
    width: '46%',
    margin: '2%',
  },
  listCard: {
    marginVertical: 6,
  },
  cardContent: {
    flexDirection: 'row',
  },
  gridImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 12,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  placeholderImage: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  moreButton: {
    padding: 4,
  },
  itemDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    fontSize: 18,
    fontWeight: '700',
  },
  availabilityContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  availabilityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  actionMenu: {
    flexDirection: 'row',
    borderTopWidth: 1,
    marginTop: 12,
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 12,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  loadingOverlay: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
  emptyContainer: {
    padding: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  addItemButton: {
    marginTop: 8,
  },
})
