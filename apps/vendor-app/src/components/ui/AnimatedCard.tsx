import React from 'react'
import { View, Pressable, StyleSheet } from 'react-native'
import { MotiView } from 'moti'
import { BlurView } from '@react-native-community/blur'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

interface AnimatedCardProps {
  children: React.ReactNode
  onPress?: () => void
  variant?: 'default' | 'glass' | 'elevated'
  delay?: number
  className?: string
  style?: any
  disabled?: boolean
}

export function AnimatedCard({
  children,
  onPress,
  variant = 'default',
  delay = 0,
  className,
  style,
  disabled = false,
}: AnimatedCardProps) {
  const { colors, isDark } = useTheme()

  const handlePress = () => {
    if (disabled) return
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    onPress?.()
  }

  const getCardStyle = () => {
    switch (variant) {
      case 'glass':
        return {
          backgroundColor: isDark 
            ? 'rgba(255, 255, 255, 0.1)' 
            : 'rgba(255, 255, 255, 0.8)',
          borderWidth: 1,
          borderColor: isDark 
            ? 'rgba(255, 255, 255, 0.2)' 
            : 'rgba(0, 0, 0, 0.1)',
        }
      case 'elevated':
        return {
          backgroundColor: colors.surface,
          shadowColor: colors.black,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 8,
          elevation: 8,
        }
      default:
        return {
          backgroundColor: colors.surface,
          borderWidth: 1,
          borderColor: colors.border,
        }
    }
  }

  const CardContent = (
    <MotiView
      from={{ opacity: 0, scale: 0.95, translateY: 20 }}
      animate={{ opacity: 1, scale: 1, translateY: 0 }}
      transition={{
        type: 'timing',
        duration: 400,
        delay,
      }}
      style={[
        styles.card,
        getCardStyle(),
        style,
      ]}
    >
      {variant === 'glass' && (
        <BlurView
          style={StyleSheet.absoluteFill}
          blurType={isDark ? 'dark' : 'light'}
          blurAmount={10}
          reducedTransparencyFallbackColor={colors.surface}
        />
      )}
      <View style={styles.content}>
        {children}
      </View>
    </MotiView>
  )

  if (onPress && !disabled) {
    return (
      <Pressable
        onPress={handlePress}
        style={({ pressed }) => [
          {
            opacity: pressed ? 0.95 : 1,
            transform: [{ scale: pressed ? 0.98 : 1 }],
          },
        ]}
      >
        {CardContent}
      </Pressable>
    )
  }

  return CardContent
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    marginVertical: 4,
  },
  content: {
    padding: 16,
  },
})
