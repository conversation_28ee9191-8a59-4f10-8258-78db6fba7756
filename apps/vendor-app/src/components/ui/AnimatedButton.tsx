import React from 'react'
import { Text, Pressable, StyleSheet, ActivityIndicator } from 'react-native'
import { MotiView } from 'moti'
import { useTheme } from '../../hooks/useTheme'
import * as Haptics from 'expo-haptics'

interface AnimatedButtonProps {
  title: string
  onPress: () => void
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  style?: any
}

export function AnimatedButton({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
}: AnimatedButtonProps) {
  const { colors } = useTheme()

  const handlePress = () => {
    if (disabled || loading) return
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
    onPress()
  }

  const getButtonStyle = () => {
    const baseStyle = {
      borderRadius: 12,
      borderWidth: 1,
    }

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: colors.primary[600],
          borderColor: colors.primary[600],
        }
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: colors.gray[100],
          borderColor: colors.gray[200],
        }
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderColor: colors.primary[600],
        }
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderColor: 'transparent',
        }
      case 'danger':
        return {
          ...baseStyle,
          backgroundColor: colors.error[600],
          borderColor: colors.error[600],
        }
      default:
        return baseStyle
    }
  }

  const getTextStyle = () => {
    const baseStyle = {
      fontWeight: '600' as const,
    }

    switch (variant) {
      case 'primary':
      case 'danger':
        return {
          ...baseStyle,
          color: '#ffffff',
        }
      case 'secondary':
        return {
          ...baseStyle,
          color: colors.text,
        }
      case 'outline':
      case 'ghost':
        return {
          ...baseStyle,
          color: colors.primary[600],
        }
      default:
        return baseStyle
    }
  }

  const getSizeStyle = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: 12,
          paddingVertical: 8,
          fontSize: 14,
        }
      case 'large':
        return {
          paddingHorizontal: 24,
          paddingVertical: 16,
          fontSize: 18,
        }
      default:
        return {
          paddingHorizontal: 16,
          paddingVertical: 12,
          fontSize: 16,
        }
    }
  }

  const sizeStyle = getSizeStyle()
  const isDisabled = disabled || loading

  return (
    <Pressable
      onPress={handlePress}
      disabled={isDisabled}
      style={({ pressed }) => [
        {
          opacity: isDisabled ? 0.6 : pressed ? 0.9 : 1,
          width: fullWidth ? '100%' : undefined,
        },
        style,
      ]}
    >
      <MotiView
        animate={{
          scale: isDisabled ? 1 : 1,
        }}
        transition={{
          type: 'spring',
          damping: 15,
          stiffness: 150,
        }}
        style={[
          styles.button,
          getButtonStyle(),
          {
            paddingHorizontal: sizeStyle.paddingHorizontal,
            paddingVertical: sizeStyle.paddingVertical,
          },
        ]}
      >
        {loading ? (
          <ActivityIndicator
            size="small"
            color={variant === 'primary' || variant === 'danger' ? '#ffffff' : colors.primary[600]}
          />
        ) : (
          <MotiView
            style={styles.content}
            from={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'timing', duration: 200 }}
          >
            {icon && iconPosition === 'left' && (
              <MotiView style={styles.iconLeft}>
                {icon}
              </MotiView>
            )}
            
            <Text
              style={[
                getTextStyle(),
                { fontSize: sizeStyle.fontSize },
              ]}
            >
              {title}
            </Text>
            
            {icon && iconPosition === 'right' && (
              <MotiView style={styles.iconRight}>
                {icon}
              </MotiView>
            )}
          </MotiView>
        )}
      </MotiView>
    </Pressable>
  )
}

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
})
