import React from 'react'
import { View, StyleSheet } from 'react-native'
import { Mo<PERSON>View } from 'moti'
import { useTheme } from '../../hooks/useTheme'

interface SkeletonLoaderProps {
  width?: number | string
  height?: number
  borderRadius?: number
  style?: any
}

export function SkeletonLoader({
  width = '100%',
  height = 20,
  borderRadius = 8,
  style,
}: SkeletonLoaderProps) {
  const { colors, isDark } = useTheme()

  const skeletonColor = isDark ? colors.gray[700] : colors.gray[200]
  const highlightColor = isDark ? colors.gray[600] : colors.gray[100]

  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor: skeletonColor,
          overflow: 'hidden',
        },
        style,
      ]}
    >
      <MotiView
        from={{ translateX: -200 }}
        animate={{ translateX: 200 }}
        transition={{
          type: 'timing',
          duration: 1500,
          loop: true,
        }}
        style={[
          StyleSheet.absoluteFill,
          {
            backgroundColor: highlightColor,
            opacity: 0.5,
          },
        ]}
      />
    </View>
  )
}

interface SkeletonCardProps {
  showAvatar?: boolean
  lines?: number
}

export function SkeletonCard({ showAvatar = false, lines = 3 }: SkeletonCardProps) {
  return (
    <View style={styles.card}>
      {showAvatar && (
        <View style={styles.avatarRow}>
          <SkeletonLoader width={40} height={40} borderRadius={20} />
          <View style={styles.avatarText}>
            <SkeletonLoader width="60%" height={16} />
            <SkeletonLoader width="40%" height={12} style={{ marginTop: 4 }} />
          </View>
        </View>
      )}
      
      <View style={styles.content}>
        {Array.from({ length: lines }).map((_, index) => (
          <SkeletonLoader
            key={index}
            width={index === lines - 1 ? '70%' : '100%'}
            height={14}
            style={{ marginBottom: 8 }}
          />
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginVertical: 8,
  },
  avatarRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarText: {
    marginLeft: 12,
    flex: 1,
  },
  content: {
    flex: 1,
  },
})
