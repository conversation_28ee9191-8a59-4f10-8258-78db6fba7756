import React from 'react'
import { View, StyleSheet } from 'react-native'
import { MotiView } from 'moti'
import { useTheme } from '../../hooks/useTheme'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  color?: string
}

export function LoadingSpinner({ size = 'medium', color }: LoadingSpinnerProps) {
  const { colors } = useTheme()
  
  const spinnerColor = color || colors.primary[600]
  const spinnerSize = {
    small: 20,
    medium: 32,
    large: 48,
  }[size]

  return (
    <View style={styles.container}>
      <MotiView
        from={{ rotate: '0deg' }}
        animate={{ rotate: '360deg' }}
        transition={{
          type: 'timing',
          duration: 1000,
          loop: true,
        }}
        style={[
          styles.spinner,
          {
            width: spinnerSize,
            height: spinnerSize,
            borderColor: `${spinnerColor}20`,
            borderTopColor: spinnerColor,
          },
        ]}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinner: {
    borderWidth: 2,
    borderRadius: 50,
  },
})
