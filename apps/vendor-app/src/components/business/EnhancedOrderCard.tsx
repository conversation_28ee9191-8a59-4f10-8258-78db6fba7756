import React, { useState } from 'react'
import { View, Text, StyleSheet, Alert } from 'react-native'
import { MotiView, AnimatePresence } from 'moti'
import { Clock, MapPin, Phone, User, Package, DollarSign } from 'lucide-react-native'
import { AnimatedCard } from '../ui/AnimatedCard'
import { AnimatedButton } from '../ui/AnimatedButton'
import { useTheme } from '../../hooks/useTheme'
import type { Order } from '../../services/orderService'
import * as Haptics from 'expo-haptics'

interface EnhancedOrderCardProps {
  order: Order
  onPress?: (orderId: string) => void
  onAccept?: (orderId: string) => Promise<void>
  onDecline?: (orderId: string) => Promise<void>
  showActions?: boolean
  delay?: number
}

export function EnhancedOrderCard({
  order,
  onPress,
  onAccept,
  onDecline,
  showActions = false,
  delay = 0,
}: EnhancedOrderCardProps) {
  const { colors } = useTheme()
  const [isAccepting, setIsAccepting] = useState(false)
  const [isDeclining, setIsDeclining] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return colors.warning[500]
      case 'accepted': return colors.primary[500]
      case 'preparing': return colors.primary[600]
      case 'ready': return colors.success[500]
      case 'picked_up': return colors.success[600]
      case 'delivered': return colors.success[700]
      case 'cancelled': return colors.error[500]
      default: return colors.gray[500]
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    return date.toLocaleDateString('en-ZA', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleAccept = async () => {
    if (!onAccept) return
    
    try {
      setIsAccepting(true)
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
      await onAccept(order.id)
    } catch (error) {
      Alert.alert('Error', 'Failed to accept order. Please try again.')
    } finally {
      setIsAccepting(false)
    }
  }

  const handleDecline = async () => {
    if (!onDecline) return
    
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeclining(true)
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy)
              await onDecline(order.id)
            } catch (error) {
              Alert.alert('Error', 'Failed to decline order. Please try again.')
            } finally {
              setIsDeclining(false)
            }
          },
        },
      ]
    )
  }

  const handleCardPress = () => {
    if (onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
      onPress(order.id)
    }
  }

  return (
    <AnimatedCard
      variant="elevated"
      onPress={handleCardPress}
      delay={delay}
      style={styles.card}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.orderInfo}>
          <Text style={[styles.orderId, { color: colors.text }]}>
            Order #{order.id.slice(-6).toUpperCase()}
          </Text>
          <Text style={[styles.orderTime, { color: colors.textSecondary }]}>
            {formatTime(order.createdAt)}
          </Text>
        </View>
        
        <MotiView
          from={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', delay: delay + 200 }}
          style={[
            styles.statusBadge,
            { backgroundColor: `${getStatusColor(order.status)}20` }
          ]}
        >
          <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
            {order.status.replace('_', ' ').toUpperCase()}
          </Text>
        </MotiView>
      </View>

      {/* Customer Info */}
      <MotiView
        from={{ opacity: 0, translateX: -20 }}
        animate={{ opacity: 1, translateX: 0 }}
        transition={{ type: 'timing', duration: 300, delay: delay + 100 }}
        style={styles.customerSection}
      >
        <View style={styles.customerRow}>
          <User size={16} color={colors.textSecondary} />
          <Text style={[styles.customerName, { color: colors.text }]}>
            {order.customerInfo.name}
          </Text>
        </View>
        
        <View style={styles.customerRow}>
          <Phone size={16} color={colors.textSecondary} />
          <Text style={[styles.customerPhone, { color: colors.textSecondary }]}>
            {order.customerInfo.phone}
          </Text>
        </View>
      </MotiView>

      {/* Order Details */}
      <MotiView
        from={{ opacity: 0, translateY: 10 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 300, delay: delay + 150 }}
        style={styles.orderDetails}
      >
        <View style={styles.detailRow}>
          <Package size={16} color={colors.textSecondary} />
          <Text style={[styles.itemCount, { color: colors.textSecondary }]}>
            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
          </Text>
          <View style={styles.spacer} />
          <DollarSign size={16} color={colors.success[600]} />
          <Text style={[styles.orderTotal, { color: colors.success[600] }]}>
            R{order.total.toFixed(2)}
          </Text>
        </View>

        {order.deliveryAddress && (
          <View style={styles.detailRow}>
            <MapPin size={16} color={colors.textSecondary} />
            <Text 
              style={[styles.address, { color: colors.textSecondary }]}
              numberOfLines={1}
            >
              {order.deliveryAddress.street}
            </Text>
          </View>
        )}

        {order.estimatedDeliveryTime && (
          <View style={styles.detailRow}>
            <Clock size={16} color={colors.primary[600]} />
            <Text style={[styles.estimatedTime, { color: colors.primary[600] }]}>
              Est. {order.estimatedDeliveryTime}
            </Text>
          </View>
        )}
      </MotiView>

      {/* Action Buttons */}
      <AnimatePresence>
        {showActions && order.status === 'pending' && (
          <MotiView
            from={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ type: 'timing', duration: 300 }}
            style={styles.actionButtons}
          >
            <AnimatedButton
              title="Decline"
              variant="outline"
              size="small"
              onPress={handleDecline}
              loading={isDeclining}
              style={styles.declineButton}
            />
            <AnimatedButton
              title="Accept"
              variant="primary"
              size="small"
              onPress={handleAccept}
              loading={isAccepting}
              style={styles.acceptButton}
            />
          </MotiView>
        )}
      </AnimatePresence>
    </AnimatedCard>
  )
}

const styles = StyleSheet.create({
  card: {
    marginVertical: 6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  orderTime: {
    fontSize: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  customerSection: {
    marginBottom: 12,
  },
  customerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  customerPhone: {
    fontSize: 12,
    marginLeft: 8,
  },
  orderDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  itemCount: {
    fontSize: 12,
    marginLeft: 6,
  },
  spacer: {
    flex: 1,
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 4,
  },
  address: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  estimatedTime: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  declineButton: {
    flex: 1,
  },
  acceptButton: {
    flex: 1,
  },
})
