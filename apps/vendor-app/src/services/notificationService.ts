import * as Notifications from 'expo-notifications'
import * as Device from 'expo-device'
import { Platform } from 'react-native'
import { functions, FUNCTIONS_ID } from '../lib/appwrite'

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
})

export interface NotificationData {
  type: 'new_order' | 'order_update' | 'payment_received' | 'runner_assigned' | 'delivery_completed'
  orderId?: string
  customerId?: string
  runnerId?: string
  message: string
  data?: any
}

class NotificationService {
  private initialized = false
  private expoPushToken: string | null = null

  async initialize() {
    if (this.initialized) return
    
    try {
      console.log('🔄 Initializing Notification Service...')
      
      // Request permissions
      await this.requestPermissions()
      
      // Get push token
      await this.registerForPushNotifications()
      
      // Set up notification listeners
      this.setupNotificationListeners()
      
      this.initialized = true
      console.log('✅ Notification Service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize Notification Service:', error)
      throw error
    }
  }

  /**
   * Request notification permissions
   */
  private async requestPermissions() {
    if (!Device.isDevice) {
      console.warn('Push notifications only work on physical devices')
      return
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync()
      finalStatus = status
    }

    if (finalStatus !== 'granted') {
      console.warn('Failed to get push token for push notification!')
      return
    }
  }

  /**
   * Register for push notifications and get token
   */
  private async registerForPushNotifications() {
    if (!Device.isDevice) return

    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      })
      
      this.expoPushToken = token.data
      console.log('📱 Push token:', token.data)
      
      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('orders', {
          name: 'Order Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        })

        await Notifications.setNotificationChannelAsync('general', {
          name: 'General Notifications',
          importance: Notifications.AndroidImportance.DEFAULT,
        })
      }
    } catch (error) {
      console.error('Failed to get push token:', error)
    }
  }

  /**
   * Set up notification event listeners
   */
  private setupNotificationListeners() {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('📨 Notification received:', notification)
      this.handleNotificationReceived(notification)
    })

    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('👆 Notification tapped:', response)
      this.handleNotificationTapped(response)
    })
  }

  /**
   * Handle notification received while app is active
   */
  private handleNotificationReceived(notification: Notifications.Notification) {
    const data = notification.request.content.data as NotificationData
    
    // Handle different notification types
    switch (data.type) {
      case 'new_order':
        // Play custom sound or vibration for new orders
        this.playOrderNotificationSound()
        break
      case 'payment_received':
        // Handle payment notification
        break
      default:
        break
    }
  }

  /**
   * Handle notification tap
   */
  private handleNotificationTapped(response: Notifications.NotificationResponse) {
    const data = response.notification.request.content.data as NotificationData
    
    // Navigate to appropriate screen based on notification type
    switch (data.type) {
      case 'new_order':
      case 'order_update':
        if (data.orderId) {
          // Navigate to order details
          // This would be handled by the navigation service
          console.log('Navigate to order:', data.orderId)
        }
        break
      default:
        break
    }
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(
    title: string,
    body: string,
    data?: NotificationData,
    channelId: string = 'general'
  ) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
        },
        trigger: null, // Send immediately
      })
    } catch (error) {
      console.error('Failed to send local notification:', error)
    }
  }

  /**
   * Send order status update notification to customer
   */
  async sendOrderStatusUpdate(
    customerId: string,
    orderId: string,
    status: string
  ) {
    try {
      await functions.createExecution(
        FUNCTIONS_ID.SEND_NOTIFICATION,
        JSON.stringify({
          type: 'order_status_update',
          userId: customerId,
          orderId,
          status,
          title: 'Order Update',
          body: this.getStatusMessage(status),
        })
      )
    } catch (error) {
      console.error('Failed to send order status notification:', error)
    }
  }

  /**
   * Send notification to runner about new delivery assignment
   */
  async sendRunnerAssignment(
    runnerId: string,
    orderId: string,
    pickupAddress: string,
    deliveryAddress: string
  ) {
    try {
      await functions.createExecution(
        FUNCTIONS_ID.SEND_NOTIFICATION,
        JSON.stringify({
          type: 'delivery_assignment',
          userId: runnerId,
          orderId,
          title: 'New Delivery Assignment',
          body: `Pickup from ${pickupAddress} to ${deliveryAddress}`,
          data: {
            orderId,
            pickupAddress,
            deliveryAddress,
          },
        })
      )
    } catch (error) {
      console.error('Failed to send runner assignment notification:', error)
    }
  }

  /**
   * Play custom sound for order notifications
   */
  private async playOrderNotificationSound() {
    try {
      // You can implement custom sound logic here
      // For now, we'll use the default system sound
    } catch (error) {
      console.error('Failed to play notification sound:', error)
    }
  }

  /**
   * Get user-friendly status message
   */
  private getStatusMessage(status: string): string {
    const messages = {
      accepted: 'Your order has been accepted and is being prepared',
      preparing: 'Your order is being prepared',
      ready: 'Your order is ready for pickup',
      picked_up: 'Your order has been picked up and is on the way',
      delivered: 'Your order has been delivered',
      cancelled: 'Your order has been cancelled',
    }
    
    return messages[status as keyof typeof messages] || 'Your order status has been updated'
  }

  /**
   * Get push token
   */
  getPushToken(): string | null {
    return this.expoPushToken
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications() {
    try {
      await Notifications.dismissAllNotificationsAsync()
    } catch (error) {
      console.error('Failed to clear notifications:', error)
    }
  }

  /**
   * Set badge count
   */
  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count)
    } catch (error) {
      console.error('Failed to set badge count:', error)
    }
  }
}

export const notificationService = new NotificationService()
