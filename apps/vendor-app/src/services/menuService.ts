import { databases, storage, DATABASE_ID, COLLECTIONS, BUCKETS } from '../lib/appwrite'
import { Query, ID } from 'appwrite'
import * as ImagePicker from 'expo-image-picker'

export interface MenuItem {
  id: string
  vendorId: string
  name: string
  description: string
  price: number
  imageUrl?: string
  category: string
  available: boolean
  preparationTime: number // in minutes
  ingredients?: string[]
  allergens?: string[]
  nutritionalInfo?: {
    calories: number
    protein: number
    carbs: number
    fat: number
  }
  customizations?: {
    name: string
    options: {
      name: string
      price: number
    }[]
  }[]
  tags?: string[]
  isPopular: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

export interface MenuCategory {
  id: string
  vendorId: string
  name: string
  description?: string
  imageUrl?: string
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

class MenuService {
  /**
   * Get all menu items for a vendor
   */
  async getMenuItems(vendorId: string): Promise<MenuItem[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        [
          Query.equal('vendorId', vendorId),
          Query.orderAsc('sortOrder'),
          Query.limit(1000)
        ]
      )

      return response.documents.map(this.mapMenuItemDocument)
    } catch (error) {
      console.error('Failed to get menu items:', error)
      throw error
    }
  }

  /**
   * Get menu items by category
   */
  async getMenuItemsByCategory(vendorId: string, category: string): Promise<MenuItem[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        [
          Query.equal('vendorId', vendorId),
          Query.equal('category', category),
          Query.orderAsc('sortOrder'),
          Query.limit(1000)
        ]
      )

      return response.documents.map(this.mapMenuItemDocument)
    } catch (error) {
      console.error('Failed to get menu items by category:', error)
      throw error
    }
  }

  /**
   * Get a specific menu item
   */
  async getMenuItem(itemId: string): Promise<MenuItem> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        itemId
      )

      return this.mapMenuItemDocument(response)
    } catch (error) {
      console.error('Failed to get menu item:', error)
      throw error
    }
  }

  /**
   * Create a new menu item
   */
  async createMenuItem(menuItem: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<MenuItem> {
    try {
      const now = new Date().toISOString()
      
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        ID.unique(),
        {
          ...menuItem,
          ingredients: menuItem.ingredients ? JSON.stringify(menuItem.ingredients) : null,
          allergens: menuItem.allergens ? JSON.stringify(menuItem.allergens) : null,
          nutritionalInfo: menuItem.nutritionalInfo ? JSON.stringify(menuItem.nutritionalInfo) : null,
          customizations: menuItem.customizations ? JSON.stringify(menuItem.customizations) : null,
          tags: menuItem.tags ? JSON.stringify(menuItem.tags) : null,
          createdAt: now,
          updatedAt: now,
        }
      )

      return this.mapMenuItemDocument(response)
    } catch (error) {
      console.error('Failed to create menu item:', error)
      throw error
    }
  }

  /**
   * Update a menu item
   */
  async updateMenuItem(itemId: string, updates: Partial<MenuItem>): Promise<MenuItem> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: new Date().toISOString(),
      }

      // Handle JSON fields
      if (updates.ingredients) {
        updateData.ingredients = JSON.stringify(updates.ingredients)
      }
      if (updates.allergens) {
        updateData.allergens = JSON.stringify(updates.allergens)
      }
      if (updates.nutritionalInfo) {
        updateData.nutritionalInfo = JSON.stringify(updates.nutritionalInfo)
      }
      if (updates.customizations) {
        updateData.customizations = JSON.stringify(updates.customizations)
      }
      if (updates.tags) {
        updateData.tags = JSON.stringify(updates.tags)
      }

      // Remove fields that shouldn't be updated
      delete updateData.id
      delete updateData.createdAt

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        itemId,
        updateData
      )

      return this.mapMenuItemDocument(response)
    } catch (error) {
      console.error('Failed to update menu item:', error)
      throw error
    }
  }

  /**
   * Delete a menu item
   */
  async deleteMenuItem(itemId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        itemId
      )
    } catch (error) {
      console.error('Failed to delete menu item:', error)
      throw error
    }
  }

  /**
   * Toggle menu item availability
   */
  async toggleAvailability(itemId: string, available: boolean): Promise<MenuItem> {
    return this.updateMenuItem(itemId, { available })
  }

  /**
   * Upload menu item image
   */
  async uploadMenuItemImage(imageUri: string): Promise<string> {
    try {
      // Convert image URI to file
      const response = await fetch(imageUri)
      const blob = await response.blob()
      
      // Create file from blob
      const file = new File([blob], `menu-item-${Date.now()}.jpg`, {
        type: 'image/jpeg',
      })

      // Upload to Appwrite storage
      const uploadResponse = await storage.createFile(
        BUCKETS.IMAGES,
        ID.unique(),
        file
      )

      // Get file URL
      const fileUrl = storage.getFileView(BUCKETS.IMAGES, uploadResponse.$id)
      
      return fileUrl.toString()
    } catch (error) {
      console.error('Failed to upload menu item image:', error)
      throw error
    }
  }

  /**
   * Pick and upload image
   */
  async pickAndUploadImage(): Promise<string> {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()
      
      if (permissionResult.granted === false) {
        throw new Error('Permission to access camera roll is required!')
      }

      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      })

      if (result.canceled) {
        throw new Error('Image selection was cancelled')
      }

      // Upload image
      return await this.uploadMenuItemImage(result.assets[0].uri)
    } catch (error) {
      console.error('Failed to pick and upload image:', error)
      throw error
    }
  }

  /**
   * Get menu categories for a vendor
   */
  async getMenuCategories(vendorId: string): Promise<MenuCategory[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        [
          Query.equal('vendorId', vendorId),
          Query.orderAsc('sortOrder'),
          Query.limit(100)
        ]
      )

      return response.documents.map(this.mapCategoryDocument)
    } catch (error) {
      console.error('Failed to get menu categories:', error)
      throw error
    }
  }

  /**
   * Create a new category
   */
  async createCategory(category: Omit<MenuCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<MenuCategory> {
    try {
      const now = new Date().toISOString()
      
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        ID.unique(),
        {
          ...category,
          createdAt: now,
          updatedAt: now,
        }
      )

      return this.mapCategoryDocument(response)
    } catch (error) {
      console.error('Failed to create category:', error)
      throw error
    }
  }

  /**
   * Update a category
   */
  async updateCategory(categoryId: string, updates: Partial<MenuCategory>): Promise<MenuCategory> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString(),
      }

      // Remove fields that shouldn't be updated
      delete updateData.id
      delete updateData.createdAt

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        categoryId,
        updateData
      )

      return this.mapCategoryDocument(response)
    } catch (error) {
      console.error('Failed to update category:', error)
      throw error
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(categoryId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        categoryId
      )
    } catch (error) {
      console.error('Failed to delete category:', error)
      throw error
    }
  }

  /**
   * Map Appwrite document to MenuItem interface
   */
  private mapMenuItemDocument(doc: any): MenuItem {
    return {
      id: doc.$id,
      vendorId: doc.vendorId,
      name: doc.name,
      description: doc.description,
      price: doc.price,
      imageUrl: doc.imageUrl,
      category: doc.category,
      available: doc.available,
      preparationTime: doc.preparationTime || 15,
      ingredients: doc.ingredients ? JSON.parse(doc.ingredients) : undefined,
      allergens: doc.allergens ? JSON.parse(doc.allergens) : undefined,
      nutritionalInfo: doc.nutritionalInfo ? JSON.parse(doc.nutritionalInfo) : undefined,
      customizations: doc.customizations ? JSON.parse(doc.customizations) : undefined,
      tags: doc.tags ? JSON.parse(doc.tags) : undefined,
      isPopular: doc.isPopular || false,
      sortOrder: doc.sortOrder || 0,
      createdAt: doc.createdAt || doc.$createdAt,
      updatedAt: doc.updatedAt || doc.$updatedAt,
    }
  }

  /**
   * Map Appwrite document to MenuCategory interface
   */
  private mapCategoryDocument(doc: any): MenuCategory {
    return {
      id: doc.$id,
      vendorId: doc.vendorId,
      name: doc.name,
      description: doc.description,
      imageUrl: doc.imageUrl,
      sortOrder: doc.sortOrder || 0,
      isActive: doc.isActive !== false,
      createdAt: doc.createdAt || doc.$createdAt,
      updatedAt: doc.updatedAt || doc.$updatedAt,
    }
  }
}

export const menuService = new MenuService()
