import AsyncStorage from '@react-native-async-storage/async-storage'
// Note: @react-native-community/netinfo needs to be installed
// For now, we'll use a simple online/offline detection

interface OfflineAction {
  id: string
  type: 'order_status_update' | 'menu_item_update' | 'vendor_settings_update'
  data: any
  timestamp: string
  retryCount: number
}

class OfflineService {
  private initialized = false
  private isOnline = true
  private pendingActions: OfflineAction[] = []
  private readonly STORAGE_KEY = 'vendor_offline_actions'
  private readonly MAX_RETRY_COUNT = 3

  async initialize() {
    if (this.initialized) return
    
    try {
      console.log('🔄 Initializing Offline Service...')
      
      // Load pending actions from storage
      await this.loadPendingActions()
      
      // Set up network listener
      this.setupNetworkListener()
      
      // Process any pending actions if online
      if (this.isOnline) {
        await this.processPendingActions()
      }
      
      this.initialized = true
      console.log('✅ Offline Service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize Offline Service:', error)
      throw error
    }
  }

  /**
   * Set up network connectivity listener
   */
  private setupNetworkListener() {
    // Simple network detection - in production, use @react-native-community/netinfo
    // NetInfo.addEventListener((state) => {
    //   const wasOffline = !this.isOnline
    //   this.isOnline = state.isConnected ?? false
    //
    //   console.log('📶 Network status:', this.isOnline ? 'Online' : 'Offline')
    //
    //   // If we just came back online, process pending actions
    //   if (wasOffline && this.isOnline) {
    //     this.processPendingActions()
    //   }
    // })

    // For now, assume we're always online
    this.isOnline = true
    console.log('📶 Network status: Online (simplified detection)')
  }

  /**
   * Load pending actions from storage
   */
  private async loadPendingActions() {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.pendingActions = JSON.parse(stored)
        console.log(`📱 Loaded ${this.pendingActions.length} pending actions`)
      }
    } catch (error) {
      console.error('Failed to load pending actions:', error)
    }
  }

  /**
   * Save pending actions to storage
   */
  private async savePendingActions() {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Failed to save pending actions:', error)
    }
  }

  /**
   * Add action to offline queue
   */
  async queueAction(
    type: OfflineAction['type'],
    data: any
  ): Promise<void> {
    const action: OfflineAction = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date().toISOString(),
      retryCount: 0,
    }

    this.pendingActions.push(action)
    await this.savePendingActions()

    console.log(`📝 Queued offline action: ${type}`)

    // If online, try to process immediately
    if (this.isOnline) {
      await this.processPendingActions()
    }
  }

  /**
   * Process all pending actions
   */
  private async processPendingActions() {
    if (!this.isOnline || this.pendingActions.length === 0) {
      return
    }

    console.log(`🔄 Processing ${this.pendingActions.length} pending actions...`)

    const actionsToProcess = [...this.pendingActions]
    const failedActions: OfflineAction[] = []

    for (const action of actionsToProcess) {
      try {
        await this.processAction(action)
        console.log(`✅ Processed action: ${action.type}`)
        
        // Remove successful action from pending list
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
      } catch (error) {
        console.error(`❌ Failed to process action ${action.type}:`, error)
        
        // Increment retry count
        action.retryCount++
        
        // If max retries reached, remove from queue
        if (action.retryCount >= this.MAX_RETRY_COUNT) {
          console.warn(`🚫 Max retries reached for action: ${action.type}`)
          this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
        } else {
          failedActions.push(action)
        }
      }
    }

    // Save updated pending actions
    await this.savePendingActions()

    if (failedActions.length > 0) {
      console.log(`⏳ ${failedActions.length} actions will be retried later`)
    }
  }

  /**
   * Process individual action
   */
  private async processAction(action: OfflineAction): Promise<void> {
    switch (action.type) {
      case 'order_status_update':
        await this.processOrderStatusUpdate(action.data)
        break
      case 'menu_item_update':
        await this.processMenuItemUpdate(action.data)
        break
      case 'vendor_settings_update':
        await this.processVendorSettingsUpdate(action.data)
        break
      default:
        throw new Error(`Unknown action type: ${action.type}`)
    }
  }

  /**
   * Process order status update
   */
  private async processOrderStatusUpdate(data: any): Promise<void> {
    // Import orderService dynamically to avoid circular dependency
    const { orderService } = await import('./orderService')
    await orderService.updateOrderStatus(data.orderId, data.status, data.estimatedTime)
  }

  /**
   * Process menu item update
   */
  private async processMenuItemUpdate(data: any): Promise<void> {
    // Import menuService dynamically to avoid circular dependency
    const { menuService } = await import('./menuService')
    
    if (data.action === 'create') {
      await menuService.createMenuItem(data.menuItem)
    } else if (data.action === 'update') {
      await menuService.updateMenuItem(data.itemId, data.updates)
    } else if (data.action === 'delete') {
      await menuService.deleteMenuItem(data.itemId)
    }
  }

  /**
   * Process vendor settings update
   */
  private async processVendorSettingsUpdate(data: any): Promise<void> {
    // Import vendorService dynamically to avoid circular dependency
    const { vendorService } = await import('./vendorService')
    await vendorService.updateVendorSettings(data.vendorId, data.settings)
  }

  /**
   * Get network status
   */
  isConnected(): boolean {
    return this.isOnline
  }

  /**
   * Get pending actions count
   */
  getPendingActionsCount(): number {
    return this.pendingActions.length
  }

  /**
   * Clear all pending actions (use with caution)
   */
  async clearPendingActions(): Promise<void> {
    this.pendingActions = []
    await this.savePendingActions()
    console.log('🗑️ Cleared all pending actions')
  }

  /**
   * Force sync - manually trigger processing of pending actions
   */
  async forceSync(): Promise<void> {
    if (this.isOnline) {
      await this.processPendingActions()
    } else {
      throw new Error('Cannot sync while offline')
    }
  }
}

export const offlineService = new OfflineService()
