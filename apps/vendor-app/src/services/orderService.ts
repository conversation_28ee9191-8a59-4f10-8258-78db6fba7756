import { databases, DATABASE_ID, COLLECTIONS } from '../lib/appwrite'
import { Query } from 'appwrite'
import { notificationService } from './notificationService'

export interface OrderItem {
  id: string
  menuItemId: string
  name: string
  price: number
  quantity: number
  customizations?: any
  subtotal: number
}

export interface Order {
  id: string
  customerId: string
  vendorId: string
  items: OrderItem[]
  subtotal: number
  discount: number
  deliveryFee: number
  total: number
  status: 'pending' | 'accepted' | 'preparing' | 'ready' | 'picked_up' | 'delivered' | 'cancelled'
  paymentMethod: string
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'
  deliveryAddress: {
    street: string
    city: string
    postalCode: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  customerInfo: {
    name: string
    phone: string
    email?: string
  }
  runnerId?: string
  notes?: string
  estimatedDeliveryTime?: string
  actualDeliveryTime?: string
  createdAt: string
  updatedAt: string
}

class OrderService {
  private initialized = false

  async initialize() {
    if (this.initialized) return
    
    try {
      console.log('🔄 Initializing Order Service...')
      this.initialized = true
      console.log('✅ Order Service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize Order Service:', error)
      throw error
    }
  }

  /**
   * Get orders for a specific vendor
   */
  async getVendorOrders(
    vendorId: string,
    status?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Order[]> {
    try {
      const queries = [
        Query.equal('vendorId', vendorId),
        Query.orderDesc('createdAt'),
        Query.limit(limit),
        Query.offset(offset)
      ]

      if (status) {
        queries.push(Query.equal('status', status))
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        queries
      )

      return response.documents.map(this.mapOrderDocument)
    } catch (error) {
      console.error('Failed to get vendor orders:', error)
      throw error
    }
  }

  /**
   * Get a specific order by ID
   */
  async getOrder(orderId: string): Promise<Order> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        orderId
      )

      return this.mapOrderDocument(response)
    } catch (error) {
      console.error('Failed to get order:', error)
      throw error
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(
    orderId: string,
    newStatus: Order['status'],
    estimatedTime?: string
  ): Promise<Order> {
    try {
      const updateData: any = {
        status: newStatus,
        updatedAt: new Date().toISOString()
      }

      if (estimatedTime) {
        updateData.estimatedDeliveryTime = estimatedTime
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        orderId,
        updateData
      )

      // Send notification to customer about status update
      const order = this.mapOrderDocument(response)
      await this.notifyCustomerStatusUpdate(order)

      return order
    } catch (error) {
      console.error('Failed to update order status:', error)
      throw error
    }
  }

  /**
   * Accept an order
   */
  async acceptOrder(orderId: string, estimatedTime: string): Promise<Order> {
    return this.updateOrderStatus(orderId, 'accepted', estimatedTime)
  }

  /**
   * Decline an order
   */
  async declineOrder(orderId: string, reason?: string): Promise<Order> {
    try {
      const updateData: any = {
        status: 'cancelled',
        updatedAt: new Date().toISOString()
      }

      if (reason) {
        updateData.cancellationReason = reason
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        orderId,
        updateData
      )

      const order = this.mapOrderDocument(response)
      await this.notifyCustomerStatusUpdate(order)

      return order
    } catch (error) {
      console.error('Failed to decline order:', error)
      throw error
    }
  }

  /**
   * Get order statistics for vendor
   */
  async getOrderStats(vendorId: string, period: 'today' | 'week' | 'month' = 'today') {
    try {
      const now = new Date()
      let startDate: Date

      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('vendorId', vendorId),
          Query.greaterThanEqual('createdAt', startDate.toISOString()),
          Query.limit(1000)
        ]
      )

      const orders = response.documents.map(this.mapOrderDocument)
      
      return {
        totalOrders: orders.length,
        completedOrders: orders.filter(o => o.status === 'delivered').length,
        pendingOrders: orders.filter(o => o.status === 'pending').length,
        cancelledOrders: orders.filter(o => o.status === 'cancelled').length,
        totalRevenue: orders
          .filter(o => o.status === 'delivered')
          .reduce((sum, o) => sum + o.total, 0),
        averageOrderValue: orders.length > 0 
          ? orders.reduce((sum, o) => sum + o.total, 0) / orders.length 
          : 0
      }
    } catch (error) {
      console.error('Failed to get order stats:', error)
      throw error
    }
  }

  /**
   * Map Appwrite document to Order interface
   */
  private mapOrderDocument(doc: any): Order {
    return {
      id: doc.$id,
      customerId: doc.customerId,
      vendorId: doc.vendorId,
      items: typeof doc.items === 'string' ? JSON.parse(doc.items) : doc.items,
      subtotal: doc.subtotal,
      discount: doc.discount || 0,
      deliveryFee: doc.deliveryFee || 0,
      total: doc.total,
      status: doc.status,
      paymentMethod: doc.paymentMethod,
      paymentStatus: doc.paymentStatus,
      deliveryAddress: typeof doc.deliveryAddress === 'string' 
        ? JSON.parse(doc.deliveryAddress) 
        : doc.deliveryAddress,
      customerInfo: typeof doc.customerInfo === 'string'
        ? JSON.parse(doc.customerInfo)
        : doc.customerInfo,
      runnerId: doc.runnerId,
      notes: doc.notes,
      estimatedDeliveryTime: doc.estimatedDeliveryTime,
      actualDeliveryTime: doc.actualDeliveryTime,
      createdAt: doc.createdAt || doc.$createdAt,
      updatedAt: doc.updatedAt || doc.$updatedAt,
    }
  }

  /**
   * Send notification to customer about status update
   */
  private async notifyCustomerStatusUpdate(order: Order) {
    try {
      await notificationService.sendOrderStatusUpdate(
        order.customerId,
        order.id,
        order.status
      )
    } catch (error) {
      console.error('Failed to notify customer:', error)
      // Don't throw error as this is not critical
    }
  }
}

export const orderService = new OrderService()
