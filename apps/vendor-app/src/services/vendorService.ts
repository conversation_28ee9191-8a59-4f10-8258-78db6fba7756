import { databases, DATABASE_ID, COLLECTIONS } from '../lib/appwrite'
import { Query } from 'appwrite'
import type { Vendor } from '../stores/authStore'

export interface VendorSettings {
  businessHours: {
    [key: string]: {
      open: string
      close: string
      isOpen: boolean
    }
  }
  deliverySettings: {
    deliveryRadius: number
    deliveryFee: number
    minimumOrder: number
    estimatedDeliveryTime: string
    freeDeliveryThreshold?: number
  }
  paymentMethods: string[]
  notifications: {
    newOrders: boolean
    orderUpdates: boolean
    promotions: boolean
    systemUpdates: boolean
  }
  autoAcceptOrders: boolean
  maxOrdersPerHour: number
}

export interface VendorAnalytics {
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  topSellingItems: {
    itemId: string
    name: string
    quantity: number
    revenue: number
  }[]
  customerRetention: number
  rating: number
  reviewCount: number
  period: {
    start: string
    end: string
  }
}

class VendorService {
  /**
   * Get vendor information
   */
  async getVendor(vendorId: string): Promise<Vendor> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        vendorId
      )

      return this.mapVendorDocument(response)
    } catch (error) {
      console.error('Failed to get vendor:', error)
      throw error
    }
  }

  /**
   * Update vendor information
   */
  async updateVendor(vendorId: string, updates: Partial<Vendor>): Promise<Vendor> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: new Date().toISOString(),
      }

      // Handle JSON fields
      if (updates.location) {
        updateData.location = JSON.stringify(updates.location)
      }
      if (updates.businessHours) {
        updateData.businessHours = JSON.stringify(updates.businessHours)
      }
      if (updates.deliverySettings) {
        updateData.deliverySettings = JSON.stringify(updates.deliverySettings)
      }
      if (updates.categories) {
        updateData.categories = updates.categories
      }
      if (updates.paymentMethods) {
        updateData.paymentMethods = updates.paymentMethods
      }

      // Remove fields that shouldn't be updated
      delete updateData.id
      delete updateData.createdAt

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        vendorId,
        updateData
      )

      return this.mapVendorDocument(response)
    } catch (error) {
      console.error('Failed to update vendor:', error)
      throw error
    }
  }

  /**
   * Update vendor settings
   */
  async updateVendorSettings(vendorId: string, settings: Partial<VendorSettings>): Promise<Vendor> {
    try {
      const updateData: any = {
        updatedAt: new Date().toISOString(),
      }

      if (settings.businessHours) {
        updateData.businessHours = JSON.stringify(settings.businessHours)
      }
      if (settings.deliverySettings) {
        updateData.deliverySettings = JSON.stringify(settings.deliverySettings)
      }
      if (settings.paymentMethods) {
        updateData.paymentMethods = settings.paymentMethods
      }
      if (settings.notifications) {
        updateData.notificationSettings = JSON.stringify(settings.notifications)
      }
      if (settings.autoAcceptOrders !== undefined) {
        updateData.autoAcceptOrders = settings.autoAcceptOrders
      }
      if (settings.maxOrdersPerHour !== undefined) {
        updateData.maxOrdersPerHour = settings.maxOrdersPerHour
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        vendorId,
        updateData
      )

      return this.mapVendorDocument(response)
    } catch (error) {
      console.error('Failed to update vendor settings:', error)
      throw error
    }
  }

  /**
   * Toggle vendor store status (open/closed)
   */
  async toggleStoreStatus(vendorId: string, isOpen: boolean): Promise<Vendor> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        vendorId,
        {
          isOpen,
          updatedAt: new Date().toISOString(),
        }
      )

      return this.mapVendorDocument(response)
    } catch (error) {
      console.error('Failed to toggle store status:', error)
      throw error
    }
  }

  /**
   * Get vendor analytics
   */
  async getVendorAnalytics(
    vendorId: string,
    period: 'today' | 'week' | 'month' | 'custom' = 'today',
    startDate?: string,
    endDate?: string
  ): Promise<VendorAnalytics> {
    try {
      // Calculate date range
      const now = new Date()
      let start: Date
      let end: Date = now

      switch (period) {
        case 'today':
          start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          start = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'custom':
          if (!startDate || !endDate) {
            throw new Error('Start and end dates are required for custom period')
          }
          start = new Date(startDate)
          end = new Date(endDate)
          break
        default:
          start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      }

      // Get orders for the period
      const ordersResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('vendorId', vendorId),
          Query.greaterThanEqual('createdAt', start.toISOString()),
          Query.lessThanEqual('createdAt', end.toISOString()),
          Query.limit(1000)
        ]
      )

      const orders = ordersResponse.documents
      const completedOrders = orders.filter(order => order.status === 'delivered')

      // Calculate analytics
      const totalOrders = orders.length
      const totalRevenue = completedOrders.reduce((sum, order) => sum + order.total, 0)
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

      // Calculate top selling items
      const itemSales: { [key: string]: { name: string; quantity: number; revenue: number } } = {}
      
      completedOrders.forEach(order => {
        const items = typeof order.items === 'string' ? JSON.parse(order.items) : order.items
        items.forEach((item: any) => {
          if (!itemSales[item.menuItemId]) {
            itemSales[item.menuItemId] = {
              name: item.name,
              quantity: 0,
              revenue: 0
            }
          }
          itemSales[item.menuItemId].quantity += item.quantity
          itemSales[item.menuItemId].revenue += item.subtotal
        })
      })

      const topSellingItems = Object.entries(itemSales)
        .map(([itemId, data]) => ({
          itemId,
          name: data.name,
          quantity: data.quantity,
          revenue: data.revenue
        }))
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 10)

      // Get vendor rating (this would typically come from a reviews collection)
      const vendor = await this.getVendor(vendorId)

      return {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        topSellingItems,
        customerRetention: 0, // This would require more complex calculation
        rating: vendor.rating,
        reviewCount: vendor.reviewCount,
        period: {
          start: start.toISOString(),
          end: end.toISOString()
        }
      }
    } catch (error) {
      console.error('Failed to get vendor analytics:', error)
      throw error
    }
  }

  /**
   * Get vendor dashboard stats
   */
  async getDashboardStats(vendorId: string) {
    try {
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const yesterday = new Date(startOfDay.getTime() - 24 * 60 * 60 * 1000)

      // Get today's orders
      const todayOrdersResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('vendorId', vendorId),
          Query.greaterThanEqual('createdAt', startOfDay.toISOString()),
          Query.limit(1000)
        ]
      )

      // Get yesterday's orders for comparison
      const yesterdayOrdersResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('vendorId', vendorId),
          Query.greaterThanEqual('createdAt', yesterday.toISOString()),
          Query.lessThan('createdAt', startOfDay.toISOString()),
          Query.limit(1000)
        ]
      )

      const todayOrders = todayOrdersResponse.documents
      const yesterdayOrders = yesterdayOrdersResponse.documents

      const todayRevenue = todayOrders
        .filter(order => order.status === 'delivered')
        .reduce((sum, order) => sum + order.total, 0)

      const yesterdayRevenue = yesterdayOrders
        .filter(order => order.status === 'delivered')
        .reduce((sum, order) => sum + order.total, 0)

      // Get menu items count
      const menuItemsResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.MENU_ITEMS,
        [
          Query.equal('vendorId', vendorId),
          Query.limit(1000)
        ]
      )

      const totalMenuItems = menuItemsResponse.documents.length
      const activeMenuItems = menuItemsResponse.documents.filter(item => item.available).length

      // Get vendor info for rating
      const vendor = await this.getVendor(vendorId)

      return {
        todayOrders: todayOrders.length,
        todayRevenue,
        avgRating: vendor.rating,
        activeMenuItems,
        totalMenuItems,
        ordersChange: todayOrders.length - yesterdayOrders.length,
        revenueChange: todayRevenue - yesterdayRevenue,
      }
    } catch (error) {
      console.error('Failed to get dashboard stats:', error)
      throw error
    }
  }

  /**
   * Map Appwrite document to Vendor interface
   */
  private mapVendorDocument(doc: any): Vendor {
    return {
      id: doc.$id,
      name: doc.name,
      description: doc.description,
      ownerId: doc.ownerId,
      location: typeof doc.location === 'string' ? JSON.parse(doc.location) : doc.location,
      status: doc.status,
      categories: doc.categories || [],
      isOpen: doc.isOpen !== false,
      rating: doc.rating || 0,
      reviewCount: doc.reviewCount || 0,
      imageUrl: doc.imageUrl,
      businessHours: typeof doc.businessHours === 'string' 
        ? JSON.parse(doc.businessHours) 
        : doc.businessHours || {},
      deliverySettings: typeof doc.deliverySettings === 'string'
        ? JSON.parse(doc.deliverySettings)
        : doc.deliverySettings || {
            deliveryRadius: 5,
            deliveryFee: 25,
            minimumOrder: 50,
            estimatedDeliveryTime: '30-45 minutes'
          },
      paymentMethods: doc.paymentMethods || ['cash', 'card'],
      createdAt: doc.createdAt || doc.$createdAt,
      updatedAt: doc.updatedAt || doc.$updatedAt,
    }
  }
}

export const vendorService = new VendorService()
