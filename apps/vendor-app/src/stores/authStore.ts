import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'

export interface User {
  id: string
  name: string
  phone: string
  email?: string
  role: 'vendor'
  avatarUrl?: string
  vendorId?: string
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface Vendor {
  id: string
  name: string
  description: string
  ownerId: string
  location: {
    address: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  status: 'active' | 'pending' | 'suspended' | 'inactive'
  categories: string[]
  isOpen: boolean
  rating: number
  reviewCount: number
  imageUrl?: string
  businessHours: {
    [key: string]: {
      open: string
      close: string
      isOpen: boolean
    }
  }
  deliverySettings: {
    deliveryRadius: number
    deliveryFee: number
    minimumOrder: number
    estimatedDeliveryTime: string
  }
  paymentMethods: string[]
  createdAt: string
  updatedAt: string
}

interface AuthState {
  // Auth state
  isAuthenticated: boolean
  hasCompletedOnboarding: boolean
  user: User | null
  vendor: Vendor | null
  token: string | null
  
  // Loading states
  isLoading: boolean
  isLoginLoading: boolean
  isRegisterLoading: boolean
  
  // Actions
  setUser: (user: User) => void
  setVendor: (vendor: Vendor) => void
  setToken: (token: string) => void
  setAuthenticated: (isAuthenticated: boolean) => void
  setOnboardingCompleted: (completed: boolean) => void
  setLoading: (loading: boolean) => void
  setLoginLoading: (loading: boolean) => void
  setRegisterLoading: (loading: boolean) => void
  logout: () => void
  reset: () => void
}

const initialState = {
  isAuthenticated: false,
  hasCompletedOnboarding: false,
  user: null,
  vendor: null,
  token: null,
  isLoading: false,
  isLoginLoading: false,
  isRegisterLoading: false,
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setUser: (user: User) => set({ user }),
      
      setVendor: (vendor: Vendor) => set({ vendor }),
      
      setToken: (token: string) => set({ token }),
      
      setAuthenticated: (isAuthenticated: boolean) => set({ isAuthenticated }),
      
      setOnboardingCompleted: (completed: boolean) => 
        set({ hasCompletedOnboarding: completed }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),
      
      setLoginLoading: (loading: boolean) => set({ isLoginLoading: loading }),
      
      setRegisterLoading: (loading: boolean) => set({ isRegisterLoading: loading }),
      
      logout: () => {
        set({
          ...initialState,
          hasCompletedOnboarding: get().hasCompletedOnboarding, // Keep onboarding state
        })
      },
      
      reset: () => set(initialState),
    }),
    {
      name: 'vendor-auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        hasCompletedOnboarding: state.hasCompletedOnboarding,
        user: state.user,
        vendor: state.vendor,
        token: state.token,
      }),
    }
  )
)
