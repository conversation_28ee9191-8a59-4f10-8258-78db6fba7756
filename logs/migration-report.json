{"timestamp": "2025-07-30T15:59:33.345Z", "status": "PARTIAL_SUCCESS", "totalSteps": 8, "completedSteps": 5, "errors": [{"step": "Validate Environment", "error": "Failed to connect to Appwrite: Failed to parse URL from /health", "stack": "Error: Failed to connect to Appwrite: Failed to parse URL from /health\n    at Object.validateEnvironment [as execute] (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:227:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigration (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:127:9)"}, {"step": "Create New Collections", "error": "Invalid `permissions` param: Permissions using the \"role:\" prefix have been replaced. Use \"users\", \"guests\", or \"any\" instead.", "stack": "AppwriteException: Invalid `permissions` param: Permissions using the \"role:\" prefix have been replaced. Use \"users\", \"guests\", or \"any\" instead.\n    at _Client.call (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/node_modules/node-appwrite/dist/client.js:275:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Databases.createCollection (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/node_modules/node-appwrite/dist/services/databases.js:248:12)\n    at async Object.createNewCollections [as execute] (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:276:7)\n    at async runMigration (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:127:9)"}, {"step": "Validate Mi<PERSON>", "error": "Missing collection: users", "stack": "Error: Missing collection: users\n    at Object.validateMigration [as execute] (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:594:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigration (/home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/scripts/database-migration.js:127:9)"}], "warnings": [], "completed": ["Backup Current Data", "Migrate Existing Data", "Update Indexes", "Update Permissions", "Cleanup"]}