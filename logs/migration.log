[2025-07-30T15:59:24.807Z] INFO: 🚀 Starting HVPPYPlug+ Database Migration
[2025-07-30T15:59:24.809Z] INFO: 📊 Total migration steps: 8
[2025-07-30T15:59:24.809Z] INFO: 
📋 Step 1/8: Backup Current Data
[2025-07-30T15:59:24.810Z] INFO: 📝 Create backup of existing collections
[2025-07-30T15:59:24.810Z] INFO: 📦 Creating backup of current database...
[2025-07-30T15:59:25.589Z] INFO: 📊 Found database: main
[2025-07-30T15:59:25.590Z] INFO: 📋 Backing up collection: users
[2025-07-30T15:59:26.758Z] WARN: ⚠️ Collection users not found, skipping backup
[2025-07-30T15:59:26.759Z] INFO: 📋 Backing up collection: vendors
[2025-07-30T15:59:27.355Z] WARN: ⚠️ Collection vendors not found, skipping backup
[2025-07-30T15:59:27.356Z] INFO: 📋 Backing up collection: menu-items
[2025-07-30T15:59:28.056Z] WARN: ⚠️ Collection menu-items not found, skipping backup
[2025-07-30T15:59:28.056Z] INFO: 📋 Backing up collection: orders
[2025-07-30T15:59:29.118Z] WARN: ⚠️ Collection orders not found, skipping backup
[2025-07-30T15:59:29.119Z] INFO: 📋 Backing up collection: otp-codes
[2025-07-30T15:59:29.428Z] WARN: ⚠️ Collection otp-codes not found, skipping backup
[2025-07-30T15:59:29.428Z] SUCCESS: 📦 Backup completed in: /home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/backups/2025-07-30
[2025-07-30T15:59:29.429Z] SUCCESS: ✅ Step 1 completed: Backup Current Data
[2025-07-30T15:59:29.429Z] INFO: 
📋 Step 2/8: Validate Environment
[2025-07-30T15:59:29.429Z] INFO: 📝 Check Appwrite connection and permissions
[2025-07-30T15:59:29.429Z] INFO: 🔍 Validating environment...
[2025-07-30T15:59:29.430Z] ERROR: ❌ Step 2 failed: Validate Environment
[2025-07-30T15:59:29.430Z] INFO: 
📋 Step 3/8: Create New Collections
[2025-07-30T15:59:29.430Z] INFO: 📝 Create missing collections with proper schema
[2025-07-30T15:59:29.430Z] INFO: 🏗️ Creating new collections...
[2025-07-30T15:59:29.771Z] INFO: 🔨 Creating collection: Users (users)
[2025-07-30T15:59:30.118Z] ERROR: ❌ Failed to create collection users
[2025-07-30T15:59:30.120Z] ERROR: ❌ Step 3 failed: Create New Collections
[2025-07-30T15:59:30.121Z] INFO: 
📋 Step 4/8: Migrate Existing Data
[2025-07-30T15:59:30.121Z] INFO: 📝 Migrate data from old schema to new schema
[2025-07-30T15:59:30.122Z] INFO: 🔄 Migrating existing data...
[2025-07-30T15:59:30.123Z] INFO: 👥 Migrating users...
[2025-07-30T15:59:30.974Z] ERROR: Failed to migrate users
[2025-07-30T15:59:30.975Z] INFO: 🏪 Migrating vendors...
[2025-07-30T15:59:31.529Z] ERROR: Failed to migrate vendors
[2025-07-30T15:59:31.529Z] INFO: 🍔 Migrating menu items...
[2025-07-30T15:59:32.209Z] ERROR: Failed to migrate menu items
[2025-07-30T15:59:32.210Z] INFO: 📦 Migrating orders...
[2025-07-30T15:59:32.641Z] ERROR: Failed to migrate orders
[2025-07-30T15:59:32.644Z] SUCCESS: 🔄 Data migration completed
[2025-07-30T15:59:32.647Z] SUCCESS: ✅ Step 4 completed: Migrate Existing Data
[2025-07-30T15:59:32.652Z] INFO: 
📋 Step 5/8: Update Indexes
[2025-07-30T15:59:32.679Z] INFO: 📝 Create new indexes for performance optimization
[2025-07-30T15:59:32.685Z] INFO: 📊 Updating indexes...
[2025-07-30T15:59:32.687Z] SUCCESS: 📊 Indexes updated
[2025-07-30T15:59:32.689Z] SUCCESS: ✅ Step 5 completed: Update Indexes
[2025-07-30T15:59:32.691Z] INFO: 
📋 Step 6/8: Update Permissions
[2025-07-30T15:59:32.692Z] INFO: 📝 Apply new security rules and permissions
[2025-07-30T15:59:32.694Z] INFO: 🔐 Updating permissions...
[2025-07-30T15:59:32.696Z] SUCCESS: 🔐 Permissions updated
[2025-07-30T15:59:32.697Z] SUCCESS: ✅ Step 6 completed: Update Permissions
[2025-07-30T15:59:32.700Z] INFO: 
📋 Step 7/8: Validate Migration
[2025-07-30T15:59:32.701Z] INFO: 📝 Verify data integrity and completeness
[2025-07-30T15:59:32.709Z] INFO: ✅ Validating migration...
[2025-07-30T15:59:33.341Z] ERROR: ❌ Step 7 failed: Validate Migration
[2025-07-30T15:59:33.342Z] INFO: 
📋 Step 8/8: Cleanup
[2025-07-30T15:59:33.342Z] INFO: 📝 Remove temporary data and finalize migration
[2025-07-30T15:59:33.343Z] INFO: 🧹 Cleaning up...
[2025-07-30T15:59:33.343Z] SUCCESS: 🧹 Cleanup completed
[2025-07-30T15:59:33.344Z] SUCCESS: ✅ Step 8 completed: Cleanup
[2025-07-30T15:59:33.345Z] INFO: 📄 Migration report saved to: /home/<USER>/Documents/Development/be-hvppy/HVPPYPlug-Monorepo/logs/migration-report.json
[2025-07-30T15:59:33.346Z] SUCCESS: 🎉 Database migration completed successfully!
[2025-07-30T15:59:33.346Z] INFO: 📄 Check migration report for details
