{"projectId": "hvppyplug", "projectName": "HVPPYPlug+", "functions": [{"id": "hvppy-api", "name": "HVPPYPlug+ API", "runtime": "node-18.0", "execute": ["any"], "events": [], "schedule": "", "timeout": 15, "enabled": true, "logging": true, "entrypoint": "src/index.js", "commands": "npm install && npm run build", "ignore": ["node_modules", ".git", ".appwrite"], "path": "backend"}], "databases": [{"id": "hvppyplug-main", "name": "HVPPYPlug+ Main Database", "enabled": true}], "collections": [{"id": "users", "name": "Users", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"any\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "name", "type": "string", "size": 255, "required": true}, {"key": "phone", "type": "string", "size": 20, "required": true}, {"key": "email", "type": "string", "size": 255, "required": false}, {"key": "role", "type": "string", "size": 20, "required": true}, {"key": "avatarUrl", "type": "string", "size": 2048, "required": false}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "active"}, {"key": "isVerified", "type": "boolean", "required": true, "default": false}, {"key": "lastActiveAt", "type": "datetime", "required": false}, {"key": "pushToken", "type": "string", "size": 255, "required": false}, {"key": "preferences", "type": "string", "size": 2048, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "phone_unique", "type": "unique", "attributes": ["phone"]}, {"key": "email_unique", "type": "unique", "attributes": ["email"]}, {"key": "role_index", "type": "key", "attributes": ["role"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "addresses", "name": "Addresses", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"user:self\")", "update(\"user:self\")", "delete(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "type", "type": "string", "size": 20, "required": true}, {"key": "label", "type": "string", "size": 100, "required": false}, {"key": "street", "type": "string", "size": 255, "required": true}, {"key": "city", "type": "string", "size": 100, "required": true}, {"key": "province", "type": "string", "size": 100, "required": true}, {"key": "postalCode", "type": "string", "size": 10, "required": true}, {"key": "country", "type": "string", "size": 50, "required": true, "default": "South Africa"}, {"key": "coordinates", "type": "string", "size": 100, "required": false}, {"key": "instructions", "type": "string", "size": 500, "required": false}, {"key": "isDefault", "type": "boolean", "required": true, "default": false}, {"key": "isActive", "type": "boolean", "required": true, "default": true}], "indexes": [{"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "default_index", "type": "key", "attributes": ["isDefault"]}]}, {"id": "categories", "name": "Categories", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:admin\")", "update(\"role:admin\")", "update(\"role:vendor\")", "delete(\"role:admin\")"], "attributes": [{"key": "name", "type": "string", "size": 100, "required": true}, {"key": "slug", "type": "string", "size": 100, "required": true}, {"key": "description", "type": "string", "size": 500, "required": false}, {"key": "icon", "type": "string", "size": 100, "required": false}, {"key": "color", "type": "string", "size": 20, "required": false}, {"key": "imageUrl", "type": "string", "size": 2048, "required": false}, {"key": "parentId", "type": "string", "size": 255, "required": false}, {"key": "sortOrder", "type": "integer", "required": true, "default": 0}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "metadata", "type": "string", "size": 1000, "required": false}], "indexes": [{"key": "slug_unique", "type": "unique", "attributes": ["slug"]}, {"key": "parent_index", "type": "key", "attributes": ["parentId"]}, {"key": "active_index", "type": "key", "attributes": ["isActive"]}, {"key": "sort_index", "type": "key", "attributes": ["sortOrder"]}]}, {"id": "vendors", "name": "Vend<PERSON>", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:vendor\")", "create(\"role:admin\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "name", "type": "string", "size": 255, "required": true}, {"key": "slug", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "ownerId", "type": "string", "size": 255, "required": true}, {"key": "businessType", "type": "string", "size": 50, "required": true}, {"key": "categories", "type": "string", "size": 1000, "required": false, "array": true}, {"key": "location", "type": "string", "size": 1000, "required": true}, {"key": "coordinates", "type": "string", "size": 100, "required": false}, {"key": "deliveryRadius", "type": "double", "required": false}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "rating", "type": "double", "required": true, "default": 0}, {"key": "reviewCount", "type": "integer", "required": true, "default": 0}, {"key": "imageUrl", "type": "string", "size": 2048, "required": false}, {"key": "coverImageUrl", "type": "string", "size": 2048, "required": false}, {"key": "phone", "type": "string", "size": 20, "required": false}, {"key": "email", "type": "string", "size": 255, "required": false}, {"key": "website", "type": "string", "size": 255, "required": false}, {"key": "businessHours", "type": "string", "size": 1000, "required": false}, {"key": "deliveryFee", "type": "double", "required": false, "default": 0}, {"key": "minimumOrder", "type": "double", "required": false, "default": 0}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isFeatured", "type": "boolean", "required": true, "default": false}, {"key": "verifiedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "slug_unique", "type": "unique", "attributes": ["slug"]}, {"key": "owner_index", "type": "key", "attributes": ["ownerId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "rating_index", "type": "key", "attributes": ["rating"]}, {"key": "featured_index", "type": "key", "attributes": ["isFeatured"]}, {"key": "location_index", "type": "key", "attributes": ["location"]}]}, {"id": "menu-items", "name": "Menu Items", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:vendor\")", "update(\"role:vendor\")", "update(\"role:admin\")", "delete(\"role:vendor\")", "delete(\"role:admin\")"], "attributes": [{"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "categoryId", "type": "string", "size": 255, "required": false}, {"key": "name", "type": "string", "size": 255, "required": true}, {"key": "slug", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "price", "type": "double", "required": true}, {"key": "compareAtPrice", "type": "double", "required": false}, {"key": "cost", "type": "double", "required": false}, {"key": "sku", "type": "string", "size": 100, "required": false}, {"key": "barcode", "type": "string", "size": 100, "required": false}, {"key": "images", "type": "string", "size": 2048, "required": false, "array": true}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "tags", "type": "string", "size": 100, "required": false, "array": true}, {"key": "variants", "type": "string", "size": 2048, "required": false}, {"key": "options", "type": "string", "size": 2048, "required": false}, {"key": "nutritionInfo", "type": "string", "size": 1000, "required": false}, {"key": "allergens", "type": "string", "size": 500, "required": false, "array": true}, {"key": "preparationTime", "type": "integer", "required": false}, {"key": "inventory", "type": "integer", "required": false}, {"key": "trackInventory", "type": "boolean", "required": true, "default": false}, {"key": "available", "type": "boolean", "required": true, "default": true}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isFeatured", "type": "boolean", "required": true, "default": false}, {"key": "sortOrder", "type": "integer", "required": true, "default": 0}, {"key": "rating", "type": "double", "required": true, "default": 0}, {"key": "reviewCount", "type": "integer", "required": true, "default": 0}, {"key": "salesCount", "type": "integer", "required": true, "default": 0}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "category_index", "type": "key", "attributes": ["categoryId"]}, {"key": "slug_index", "type": "key", "attributes": ["slug"]}, {"key": "available_index", "type": "key", "attributes": ["available"]}, {"key": "featured_index", "type": "key", "attributes": ["isFeatured"]}, {"key": "rating_index", "type": "key", "attributes": ["rating"]}, {"key": "price_index", "type": "key", "attributes": ["price"]}]}, {"id": "orders", "name": "Orders", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"role:customer\")", "update(\"role:vendor\")", "update(\"role:runner\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "orderNumber", "type": "string", "size": 50, "required": true}, {"key": "customerId", "type": "string", "size": 255, "required": true}, {"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "runnerId", "type": "string", "size": 255, "required": false}, {"key": "items", "type": "string", "size": 5000, "required": true}, {"key": "subtotal", "type": "double", "required": true}, {"key": "discount", "type": "double", "required": true, "default": 0}, {"key": "deliveryFee", "type": "double", "required": true, "default": 0}, {"key": "serviceFee", "type": "double", "required": true, "default": 0}, {"key": "tax", "type": "double", "required": true, "default": 0}, {"key": "tip", "type": "double", "required": true, "default": 0}, {"key": "total", "type": "double", "required": true}, {"key": "currency", "type": "string", "size": 10, "required": true, "default": "ZAR"}, {"key": "status", "type": "string", "size": 50, "required": true, "default": "pending"}, {"key": "paymentMethod", "type": "string", "size": 50, "required": true}, {"key": "paymentStatus", "type": "string", "size": 50, "required": true, "default": "pending"}, {"key": "paymentIntentId", "type": "string", "size": 255, "required": false}, {"key": "deliveryAddress", "type": "string", "size": 1000, "required": true}, {"key": "deliveryInstructions", "type": "string", "size": 500, "required": false}, {"key": "estimatedDeliveryTime", "type": "datetime", "required": false}, {"key": "actualDeliveryTime", "type": "datetime", "required": false}, {"key": "preparationTime", "type": "integer", "required": false}, {"key": "notes", "type": "string", "size": 500, "required": false}, {"key": "cancellationReason", "type": "string", "size": 500, "required": false}, {"key": "refundAmount", "type": "double", "required": false}, {"key": "refundReason", "type": "string", "size": 500, "required": false}, {"key": "trackingUpdates", "type": "string", "size": 2048, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}, {"key": "completedAt", "type": "datetime", "required": false}], "indexes": [{"key": "order_number_unique", "type": "unique", "attributes": ["orderNumber"]}, {"key": "customer_index", "type": "key", "attributes": ["customerId"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "runner_index", "type": "key", "attributes": ["runnerId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "payment_status_index", "type": "key", "attributes": ["paymentStatus"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}, {"key": "delivery_time_index", "type": "key", "attributes": ["estimatedDeliveryTime"]}]}, {"id": "reviews", "name": "Reviews", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:customer\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "orderId", "type": "string", "size": 255, "required": true}, {"key": "customerId", "type": "string", "size": 255, "required": true}, {"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "runnerId", "type": "string", "size": 255, "required": false}, {"key": "itemId", "type": "string", "size": 255, "required": false}, {"key": "type", "type": "string", "size": 20, "required": true}, {"key": "rating", "type": "integer", "required": true}, {"key": "title", "type": "string", "size": 255, "required": false}, {"key": "comment", "type": "string", "size": 1000, "required": false}, {"key": "images", "type": "string", "size": 2048, "required": false, "array": true}, {"key": "tags", "type": "string", "size": 100, "required": false, "array": true}, {"key": "isVerified", "type": "boolean", "required": true, "default": false}, {"key": "isPublic", "type": "boolean", "required": true, "default": true}, {"key": "helpfulCount", "type": "integer", "required": true, "default": 0}, {"key": "reportCount", "type": "integer", "required": true, "default": 0}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "active"}, {"key": "moderatedAt", "type": "datetime", "required": false}, {"key": "moderatedBy", "type": "string", "size": 255, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "order_unique", "type": "unique", "attributes": ["orderId", "customerId", "type"]}, {"key": "customer_index", "type": "key", "attributes": ["customerId"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "runner_index", "type": "key", "attributes": ["runnerId"]}, {"key": "item_index", "type": "key", "attributes": ["itemId"]}, {"key": "rating_index", "type": "key", "attributes": ["rating"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "messages", "name": "Messages", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"user:self\")", "update(\"user:self\")", "delete(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "conversationId", "type": "string", "size": 255, "required": true}, {"key": "senderId", "type": "string", "size": 255, "required": true}, {"key": "receiverId", "type": "string", "size": 255, "required": true}, {"key": "orderId", "type": "string", "size": 255, "required": false}, {"key": "type", "type": "string", "size": 20, "required": true, "default": "text"}, {"key": "content", "type": "string", "size": 2000, "required": false}, {"key": "attachments", "type": "string", "size": 2048, "required": false, "array": true}, {"key": "metadata", "type": "string", "size": 1000, "required": false}, {"key": "isRead", "type": "boolean", "required": true, "default": false}, {"key": "readAt", "type": "datetime", "required": false}, {"key": "isDelivered", "type": "boolean", "required": true, "default": false}, {"key": "deliveredAt", "type": "datetime", "required": false}, {"key": "isDeleted", "type": "boolean", "required": true, "default": false}, {"key": "deletedAt", "type": "datetime", "required": false}, {"key": "replyToId", "type": "string", "size": 255, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "conversation_index", "type": "key", "attributes": ["conversationId"]}, {"key": "sender_index", "type": "key", "attributes": ["senderId"]}, {"key": "receiver_index", "type": "key", "attributes": ["receiverId"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "read_index", "type": "key", "attributes": ["isRead"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "conversations", "name": "Conversations", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"user:self\")", "update(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "participants", "type": "string", "size": 255, "required": true, "array": true}, {"key": "type", "type": "string", "size": 20, "required": true, "default": "direct"}, {"key": "orderId", "type": "string", "size": 255, "required": false}, {"key": "title", "type": "string", "size": 255, "required": false}, {"key": "lastMessageId", "type": "string", "size": 255, "required": false}, {"key": "lastMessageAt", "type": "datetime", "required": false}, {"key": "unreadCount", "type": "string", "size": 500, "required": false}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isArchived", "type": "boolean", "required": true, "default": false}, {"key": "archivedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "participants_index", "type": "key", "attributes": ["participants"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "active_index", "type": "key", "attributes": ["isActive"]}, {"key": "last_message_index", "type": "key", "attributes": ["lastMessageAt"]}]}, {"id": "notifications", "name": "Notifications", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "create(\"role:admin\")", "create(\"any\")", "update(\"user:self\")", "delete(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "type", "type": "string", "size": 50, "required": true}, {"key": "title", "type": "string", "size": 255, "required": true}, {"key": "body", "type": "string", "size": 1000, "required": true}, {"key": "data", "type": "string", "size": 2048, "required": false}, {"key": "imageUrl", "type": "string", "size": 2048, "required": false}, {"key": "actionUrl", "type": "string", "size": 2048, "required": false}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "priority", "type": "string", "size": 20, "required": true, "default": "normal"}, {"key": "isRead", "type": "boolean", "required": true, "default": false}, {"key": "readAt", "type": "datetime", "required": false}, {"key": "isSent", "type": "boolean", "required": true, "default": false}, {"key": "sentAt", "type": "datetime", "required": false}, {"key": "deliveryStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "scheduledFor", "type": "datetime", "required": false}, {"key": "expiresAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "category_index", "type": "key", "attributes": ["category"]}, {"key": "read_index", "type": "key", "attributes": ["isRead"]}, {"key": "sent_index", "type": "key", "attributes": ["isSent"]}, {"key": "scheduled_index", "type": "key", "attributes": ["scheduledFor"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "promotions", "name": "Promotions", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:vendor\")", "create(\"role:admin\")", "update(\"role:vendor\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "code", "type": "string", "size": 50, "required": true}, {"key": "name", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "type", "type": "string", "size": 20, "required": true}, {"key": "discountType", "type": "string", "size": 20, "required": true}, {"key": "discountValue", "type": "double", "required": true}, {"key": "minimumOrderValue", "type": "double", "required": false}, {"key": "maximumDiscount", "type": "double", "required": false}, {"key": "vendorId", "type": "string", "size": 255, "required": false}, {"key": "categoryIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "itemIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "userIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "usageLimit", "type": "integer", "required": false}, {"key": "usageLimitPerUser", "type": "integer", "required": false}, {"key": "usageCount", "type": "integer", "required": true, "default": 0}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isPublic", "type": "boolean", "required": true, "default": true}, {"key": "startsAt", "type": "datetime", "required": true}, {"key": "endsAt", "type": "datetime", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "code_unique", "type": "unique", "attributes": ["code"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "active_index", "type": "key", "attributes": ["isActive"]}, {"key": "dates_index", "type": "key", "attributes": ["startsAt", "endsAt"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "promotion-usage", "name": "Promotion Usage", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "promotionId", "type": "string", "size": 255, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "orderId", "type": "string", "size": 255, "required": true}, {"key": "discountAmount", "type": "double", "required": true}, {"key": "originalAmount", "type": "double", "required": true}, {"key": "finalAmount", "type": "double", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "promotion_user_unique", "type": "unique", "attributes": ["promotionId", "userId", "orderId"]}, {"key": "promotion_index", "type": "key", "attributes": ["promotionId"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "analytics-events", "name": "Analytics Events", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"role:admin\")", "read(\"role:vendor\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": false}, {"key": "sessionId", "type": "string", "size": 255, "required": false}, {"key": "eventName", "type": "string", "size": 100, "required": true}, {"key": "eventCategory", "type": "string", "size": 50, "required": true}, {"key": "eventAction", "type": "string", "size": 50, "required": true}, {"key": "eventLabel", "type": "string", "size": 255, "required": false}, {"key": "eventValue", "type": "double", "required": false}, {"key": "properties", "type": "string", "size": 2048, "required": false}, {"key": "userAgent", "type": "string", "size": 500, "required": false}, {"key": "ip<PERSON><PERSON><PERSON>", "type": "string", "size": 50, "required": false}, {"key": "platform", "type": "string", "size": 20, "required": false}, {"key": "appVersion", "type": "string", "size": 20, "required": false}, {"key": "deviceId", "type": "string", "size": 255, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "session_index", "type": "key", "attributes": ["sessionId"]}, {"key": "event_index", "type": "key", "attributes": ["eventName"]}, {"key": "category_index", "type": "key", "attributes": ["eventCategory"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}, {"key": "platform_index", "type": "key", "attributes": ["platform"]}]}, {"id": "payments", "name": "Payments", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "orderId", "type": "string", "size": 255, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "paymentMethod", "type": "string", "size": 50, "required": true}, {"key": "provider", "type": "string", "size": 50, "required": true}, {"key": "providerTransactionId", "type": "string", "size": 255, "required": false}, {"key": "paymentIntentId", "type": "string", "size": 255, "required": false}, {"key": "amount", "type": "double", "required": true}, {"key": "currency", "type": "string", "size": 10, "required": true, "default": "ZAR"}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "failureReason", "type": "string", "size": 500, "required": false}, {"key": "refundAmount", "type": "double", "required": false}, {"key": "refundReason", "type": "string", "size": 500, "required": false}, {"key": "refundedAt", "type": "datetime", "required": false}, {"key": "metadata", "type": "string", "size": 2048, "required": false}, {"key": "processedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "order_unique", "type": "unique", "attributes": ["orderId"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "provider_transaction_index", "type": "key", "attributes": ["providerTransactionId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "runner-profiles", "name": "Runner Profiles", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"role:runner\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "vehicleType", "type": "string", "size": 50, "required": true}, {"key": "vehicleDetails", "type": "string", "size": 1000, "required": false}, {"key": "licenseNumber", "type": "string", "size": 50, "required": false}, {"key": "licenseExpiryDate", "type": "datetime", "required": false}, {"key": "insuranceDetails", "type": "string", "size": 1000, "required": false}, {"key": "bankDetails", "type": "string", "size": 1000, "required": false}, {"key": "emergencyContact", "type": "string", "size": 500, "required": false}, {"key": "working<PERSON><PERSON><PERSON>", "type": "string", "size": 1000, "required": false, "array": true}, {"key": "maxDeliveryRadius", "type": "double", "required": false, "default": 10}, {"key": "isAvailable", "type": "boolean", "required": true, "default": false}, {"key": "isOnline", "type": "boolean", "required": true, "default": false}, {"key": "currentLocation", "type": "string", "size": 100, "required": false}, {"key": "rating", "type": "double", "required": true, "default": 0}, {"key": "reviewCount", "type": "integer", "required": true, "default": 0}, {"key": "totalDeliveries", "type": "integer", "required": true, "default": 0}, {"key": "totalEarnings", "type": "double", "required": true, "default": 0}, {"key": "verificationStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "verifiedAt", "type": "datetime", "required": false}, {"key": "documentsUploaded", "type": "string", "size": 500, "required": false, "array": true}, {"key": "backgroundCheckStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "backgroundCheckDate", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_unique", "type": "unique", "attributes": ["userId"]}, {"key": "available_index", "type": "key", "attributes": ["isAvailable"]}, {"key": "online_index", "type": "key", "attributes": ["isOnline"]}, {"key": "rating_index", "type": "key", "attributes": ["rating"]}, {"key": "verification_index", "type": "key", "attributes": ["verificationStatus"]}, {"key": "location_index", "type": "key", "attributes": ["currentLocation"]}]}, {"id": "support-tickets", "name": "Support Tickets", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "read(\"role:support\")", "create(\"user:self\")", "update(\"user:self\")", "update(\"role:admin\")", "update(\"role:support\")", "delete(\"role:admin\")"], "attributes": [{"key": "ticketNumber", "type": "string", "size": 50, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "orderId", "type": "string", "size": 255, "required": false}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "priority", "type": "string", "size": 20, "required": true, "default": "medium"}, {"key": "subject", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 2000, "required": true}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "open"}, {"key": "assignedTo", "type": "string", "size": 255, "required": false}, {"key": "attachments", "type": "string", "size": 2048, "required": false, "array": true}, {"key": "tags", "type": "string", "size": 100, "required": false, "array": true}, {"key": "resolution", "type": "string", "size": 2000, "required": false}, {"key": "resolutionTime", "type": "integer", "required": false}, {"key": "satisfactionRating", "type": "integer", "required": false}, {"key": "satisfactionComment", "type": "string", "size": 1000, "required": false}, {"key": "closedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "ticket_number_unique", "type": "unique", "attributes": ["ticketNumber"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "category_index", "type": "key", "attributes": ["category"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "priority_index", "type": "key", "attributes": ["priority"]}, {"key": "assigned_index", "type": "key", "attributes": ["assignedTo"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "vendor-settings", "name": "<PERSON><PERSON><PERSON>", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"role:vendor\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "autoAcceptOrders", "type": "boolean", "required": true, "default": false}, {"key": "preparationTime", "type": "integer", "required": true, "default": 30}, {"key": "maxOrdersPerHour", "type": "integer", "required": false}, {"key": "deliverySettings", "type": "string", "size": 1000, "required": false}, {"key": "paymentSettings", "type": "string", "size": 1000, "required": false}, {"key": "notificationSettings", "type": "string", "size": 1000, "required": false}, {"key": "businessHours", "type": "string", "size": 2000, "required": false}, {"key": "holidaySchedule", "type": "string", "size": 2000, "required": false}, {"key": "taxSettings", "type": "string", "size": 500, "required": false}, {"key": "integrations", "type": "string", "size": 2000, "required": false}, {"key": "customFields", "type": "string", "size": 2000, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "vendor_unique", "type": "unique", "attributes": ["vendorId"]}]}, {"id": "otp-codes", "name": "OTP Codes", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"any\")"], "attributes": [{"key": "phone", "type": "string", "size": 20, "required": true}, {"key": "code", "type": "string", "size": 10, "required": true}, {"key": "type", "type": "string", "size": 20, "required": true, "default": "verification"}, {"key": "attempts", "type": "integer", "required": true, "default": 0}, {"key": "maxAttempts", "type": "integer", "required": true, "default": 3}, {"key": "isUsed", "type": "boolean", "required": true, "default": false}, {"key": "usedAt", "type": "datetime", "required": false}, {"key": "expiresAt", "type": "datetime", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "phone_index", "type": "key", "attributes": ["phone"]}, {"key": "code_index", "type": "key", "attributes": ["code"]}, {"key": "expires_index", "type": "key", "attributes": ["expiresAt"]}, {"key": "used_index", "type": "key", "attributes": ["isUsed"]}]}], "buckets": [{"id": "images", "name": "Images", "permissions": ["read(\"any\")"], "fileSecurity": true, "enabled": true, "maximumFileSize": 50000000, "allowedFileExtensions": ["jpg", "jpeg", "png", "webp"], "compression": "gzip", "encryption": true, "antivirus": true}, {"id": "documents", "name": "Documents", "permissions": ["read(\"user:self\")", "read(\"role:admin\")"], "fileSecurity": true, "enabled": true, "maximumFileSize": 10000000, "allowedFileExtensions": ["pdf", "doc", "docx", "txt"], "compression": "gzip", "encryption": true, "antivirus": true}]}