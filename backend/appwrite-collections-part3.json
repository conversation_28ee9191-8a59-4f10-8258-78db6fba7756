{"collections": [{"id": "runner-profiles", "name": "Runner Profiles", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"role:runner\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "vehicleType", "type": "string", "size": 50, "required": true}, {"key": "vehicleDetails", "type": "string", "size": 1000, "required": false}, {"key": "licenseNumber", "type": "string", "size": 50, "required": false}, {"key": "licenseExpiryDate", "type": "datetime", "required": false}, {"key": "insuranceDetails", "type": "string", "size": 1000, "required": false}, {"key": "bankDetails", "type": "string", "size": 1000, "required": false}, {"key": "emergencyContact", "type": "string", "size": 500, "required": false}, {"key": "working<PERSON><PERSON><PERSON>", "type": "string", "size": 1000, "required": false, "array": true}, {"key": "maxDeliveryRadius", "type": "double", "required": false, "default": 10}, {"key": "isAvailable", "type": "boolean", "required": true, "default": false}, {"key": "isOnline", "type": "boolean", "required": true, "default": false}, {"key": "currentLocation", "type": "string", "size": 100, "required": false}, {"key": "rating", "type": "double", "required": true, "default": 0}, {"key": "reviewCount", "type": "integer", "required": true, "default": 0}, {"key": "totalDeliveries", "type": "integer", "required": true, "default": 0}, {"key": "totalEarnings", "type": "double", "required": true, "default": 0}, {"key": "verificationStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "verifiedAt", "type": "datetime", "required": false}, {"key": "documentsUploaded", "type": "string", "size": 500, "required": false, "array": true}, {"key": "backgroundCheckStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "backgroundCheckDate", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_unique", "type": "unique", "attributes": ["userId"]}, {"key": "available_index", "type": "key", "attributes": ["isAvailable"]}, {"key": "online_index", "type": "key", "attributes": ["isOnline"]}, {"key": "rating_index", "type": "key", "attributes": ["rating"]}, {"key": "verification_index", "type": "key", "attributes": ["verificationStatus"]}, {"key": "location_index", "type": "key", "attributes": ["currentLocation"]}]}, {"id": "support-tickets", "name": "Support Tickets", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "read(\"role:support\")", "create(\"user:self\")", "update(\"user:self\")", "update(\"role:admin\")", "update(\"role:support\")", "delete(\"role:admin\")"], "attributes": [{"key": "ticketNumber", "type": "string", "size": 50, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "orderId", "type": "string", "size": 255, "required": false}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "priority", "type": "string", "size": 20, "required": true, "default": "medium"}, {"key": "subject", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 2000, "required": true}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "open"}, {"key": "assignedTo", "type": "string", "size": 255, "required": false}, {"key": "attachments", "type": "string", "size": 2048, "required": false, "array": true}, {"key": "tags", "type": "string", "size": 100, "required": false, "array": true}, {"key": "resolution", "type": "string", "size": 2000, "required": false}, {"key": "resolutionTime", "type": "integer", "required": false}, {"key": "satisfactionRating", "type": "integer", "required": false}, {"key": "satisfactionComment", "type": "string", "size": 1000, "required": false}, {"key": "closedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "ticket_number_unique", "type": "unique", "attributes": ["ticketNumber"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "category_index", "type": "key", "attributes": ["category"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "priority_index", "type": "key", "attributes": ["priority"]}, {"key": "assigned_index", "type": "key", "attributes": ["assignedTo"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "vendor-settings", "name": "<PERSON><PERSON><PERSON>", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"role:vendor\")", "update(\"user:self\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "autoAcceptOrders", "type": "boolean", "required": true, "default": false}, {"key": "preparationTime", "type": "integer", "required": true, "default": 30}, {"key": "maxOrdersPerHour", "type": "integer", "required": false}, {"key": "deliverySettings", "type": "string", "size": 1000, "required": false}, {"key": "paymentSettings", "type": "string", "size": 1000, "required": false}, {"key": "notificationSettings", "type": "string", "size": 1000, "required": false}, {"key": "businessHours", "type": "string", "size": 2000, "required": false}, {"key": "holidaySchedule", "type": "string", "size": 2000, "required": false}, {"key": "taxSettings", "type": "string", "size": 500, "required": false}, {"key": "integrations", "type": "string", "size": 2000, "required": false}, {"key": "customFields", "type": "string", "size": 2000, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "vendor_unique", "type": "unique", "attributes": ["vendorId"]}]}, {"id": "otp-codes", "name": "OTP Codes", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"any\")"], "attributes": [{"key": "phone", "type": "string", "size": 20, "required": true}, {"key": "code", "type": "string", "size": 10, "required": true}, {"key": "type", "type": "string", "size": 20, "required": true, "default": "verification"}, {"key": "attempts", "type": "integer", "required": true, "default": 0}, {"key": "maxAttempts", "type": "integer", "required": true, "default": 3}, {"key": "isUsed", "type": "boolean", "required": true, "default": false}, {"key": "usedAt", "type": "datetime", "required": false}, {"key": "expiresAt", "type": "datetime", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "phone_index", "type": "key", "attributes": ["phone"]}, {"key": "code_index", "type": "key", "attributes": ["code"]}, {"key": "expires_index", "type": "key", "attributes": ["expiresAt"]}, {"key": "used_index", "type": "key", "attributes": ["isUsed"]}]}]}