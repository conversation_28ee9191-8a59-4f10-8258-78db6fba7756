{"collections": [{"id": "conversations", "name": "Conversations", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"user:self\")", "update(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "participants", "type": "string", "size": 255, "required": true, "array": true}, {"key": "type", "type": "string", "size": 20, "required": true, "default": "direct"}, {"key": "orderId", "type": "string", "size": 255, "required": false}, {"key": "title", "type": "string", "size": 255, "required": false}, {"key": "lastMessageId", "type": "string", "size": 255, "required": false}, {"key": "lastMessageAt", "type": "datetime", "required": false}, {"key": "unreadCount", "type": "string", "size": 500, "required": false}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isArchived", "type": "boolean", "required": true, "default": false}, {"key": "archivedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "participants_index", "type": "key", "attributes": ["participants"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "active_index", "type": "key", "attributes": ["isActive"]}, {"key": "last_message_index", "type": "key", "attributes": ["lastMessageAt"]}]}, {"id": "notifications", "name": "Notifications", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "create(\"role:admin\")", "create(\"any\")", "update(\"user:self\")", "delete(\"user:self\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "type", "type": "string", "size": 50, "required": true}, {"key": "title", "type": "string", "size": 255, "required": true}, {"key": "body", "type": "string", "size": 1000, "required": true}, {"key": "data", "type": "string", "size": 2048, "required": false}, {"key": "imageUrl", "type": "string", "size": 2048, "required": false}, {"key": "actionUrl", "type": "string", "size": 2048, "required": false}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "priority", "type": "string", "size": 20, "required": true, "default": "normal"}, {"key": "isRead", "type": "boolean", "required": true, "default": false}, {"key": "readAt", "type": "datetime", "required": false}, {"key": "isSent", "type": "boolean", "required": true, "default": false}, {"key": "sentAt", "type": "datetime", "required": false}, {"key": "deliveryStatus", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "scheduledFor", "type": "datetime", "required": false}, {"key": "expiresAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "category_index", "type": "key", "attributes": ["category"]}, {"key": "read_index", "type": "key", "attributes": ["isRead"]}, {"key": "sent_index", "type": "key", "attributes": ["isSent"]}, {"key": "scheduled_index", "type": "key", "attributes": ["scheduledFor"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "promotions", "name": "Promotions", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"any\")", "create(\"role:vendor\")", "create(\"role:admin\")", "update(\"role:vendor\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "code", "type": "string", "size": 50, "required": true}, {"key": "name", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "type", "type": "string", "size": 20, "required": true}, {"key": "discountType", "type": "string", "size": 20, "required": true}, {"key": "discountValue", "type": "double", "required": true}, {"key": "minimumOrderValue", "type": "double", "required": false}, {"key": "maximumDiscount", "type": "double", "required": false}, {"key": "vendorId", "type": "string", "size": 255, "required": false}, {"key": "categoryIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "itemIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "userIds", "type": "string", "size": 500, "required": false, "array": true}, {"key": "usageLimit", "type": "integer", "required": false}, {"key": "usageLimitPerUser", "type": "integer", "required": false}, {"key": "usageCount", "type": "integer", "required": true, "default": 0}, {"key": "isActive", "type": "boolean", "required": true, "default": true}, {"key": "isPublic", "type": "boolean", "required": true, "default": true}, {"key": "startsAt", "type": "datetime", "required": true}, {"key": "endsAt", "type": "datetime", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "code_unique", "type": "unique", "attributes": ["code"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "type_index", "type": "key", "attributes": ["type"]}, {"key": "active_index", "type": "key", "attributes": ["isActive"]}, {"key": "dates_index", "type": "key", "attributes": ["startsAt", "endsAt"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "promotion-usage", "name": "Promotion Usage", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "promotionId", "type": "string", "size": 255, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "orderId", "type": "string", "size": 255, "required": true}, {"key": "discountAmount", "type": "double", "required": true}, {"key": "originalAmount", "type": "double", "required": true}, {"key": "finalAmount", "type": "double", "required": true}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "promotion_user_unique", "type": "unique", "attributes": ["promotionId", "userId", "orderId"]}, {"key": "promotion_index", "type": "key", "attributes": ["promotionId"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "order_index", "type": "key", "attributes": ["orderId"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}, {"id": "analytics-events", "name": "Analytics Events", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"role:admin\")", "read(\"role:vendor\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "userId", "type": "string", "size": 255, "required": false}, {"key": "sessionId", "type": "string", "size": 255, "required": false}, {"key": "eventName", "type": "string", "size": 100, "required": true}, {"key": "eventCategory", "type": "string", "size": 50, "required": true}, {"key": "eventAction", "type": "string", "size": 50, "required": true}, {"key": "eventLabel", "type": "string", "size": 255, "required": false}, {"key": "eventValue", "type": "double", "required": false}, {"key": "properties", "type": "string", "size": 2048, "required": false}, {"key": "userAgent", "type": "string", "size": 500, "required": false}, {"key": "ip<PERSON><PERSON><PERSON>", "type": "string", "size": 50, "required": false}, {"key": "platform", "type": "string", "size": 20, "required": false}, {"key": "appVersion", "type": "string", "size": 20, "required": false}, {"key": "deviceId", "type": "string", "size": 255, "required": false}, {"key": "createdAt", "type": "datetime", "required": true}], "indexes": [{"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "session_index", "type": "key", "attributes": ["sessionId"]}, {"key": "event_index", "type": "key", "attributes": ["eventName"]}, {"key": "category_index", "type": "key", "attributes": ["eventCategory"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}, {"key": "platform_index", "type": "key", "attributes": ["platform"]}]}, {"id": "payments", "name": "Payments", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "permissions": ["read(\"user:self\")", "read(\"role:admin\")", "create(\"any\")", "update(\"role:admin\")", "delete(\"role:admin\")"], "attributes": [{"key": "orderId", "type": "string", "size": 255, "required": true}, {"key": "userId", "type": "string", "size": 255, "required": true}, {"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "paymentMethod", "type": "string", "size": 50, "required": true}, {"key": "provider", "type": "string", "size": 50, "required": true}, {"key": "providerTransactionId", "type": "string", "size": 255, "required": false}, {"key": "paymentIntentId", "type": "string", "size": 255, "required": false}, {"key": "amount", "type": "double", "required": true}, {"key": "currency", "type": "string", "size": 10, "required": true, "default": "ZAR"}, {"key": "status", "type": "string", "size": 20, "required": true, "default": "pending"}, {"key": "failureReason", "type": "string", "size": 500, "required": false}, {"key": "refundAmount", "type": "double", "required": false}, {"key": "refundReason", "type": "string", "size": 500, "required": false}, {"key": "refundedAt", "type": "datetime", "required": false}, {"key": "metadata", "type": "string", "size": 2048, "required": false}, {"key": "processedAt", "type": "datetime", "required": false}, {"key": "createdAt", "type": "datetime", "required": true}, {"key": "updatedAt", "type": "datetime", "required": true}], "indexes": [{"key": "order_unique", "type": "unique", "attributes": ["orderId"]}, {"key": "user_index", "type": "key", "attributes": ["userId"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "provider_transaction_index", "type": "key", "attributes": ["providerTransactionId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}, {"key": "created_index", "type": "key", "attributes": ["createdAt"]}]}]}